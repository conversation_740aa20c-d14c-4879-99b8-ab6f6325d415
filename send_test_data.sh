#!/bin/bash

echo "=== UDP 数据循环发送测试脚本 ==="
echo "固定每 500ms 发送一次 32 字节数据到 UDP 端口 6699"
echo ""

# 检查 nc 是否可用
if ! command -v nc &> /dev/null; then
    echo "❌ nc (netcat) 命令未找到，请安装 netcat"
    exit 1
fi

# 要发送的固定数据（十六进制）
DATA='\x12\x34\x56\x78\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1A\x1B\x1C'

echo "📡 开始循环发送数据包..."
while true; do
    echo -n -e "$DATA" | nc -u -w0 127.0.0.1 6699
    echo "✅ 已发送数据包，时间：$(date '+%H:%M:%S.%3N')"
    sleep 0.5  # 500ms 间隔
done