#pragma once

#include <QObject>
#include <QEvent>
#include <QVariant>
#include <QString>

namespace robosense::visualization {

// 可视化事件类型
enum class VisualizationEventType {
    ShowVisualization = QEvent::User + 1000,
    UpdateVisualization,
    HideVisualization,
    ConfigureVisualization
};

// 可视化事件基类
class VisualizationEvent : public QEvent
{
public:
    VisualizationEvent(VisualizationEventType type, const QString& node_id)
        : QEvent(static_cast<QEvent::Type>(type)), node_id_(node_id) {}

    QString getNodeId() const { return node_id_; }

private:
    QString node_id_;
};

// 显示可视化事件
class ShowVisualizationEvent : public VisualizationEvent
{
public:
    ShowVisualizationEvent(const QString& node_id, 
                          const QString& data_type,
                          const QVariant& data,
                          const QVariantMap& config = {})
        : VisualizationEvent(VisualizationEventType::ShowVisualization, node_id)
        , data_type_(data_type), data_(data), config_(config) {}

    QString getDataType() const { return data_type_; }
    QVariant getData() const { return data_; }
    QVariantMap getConfig() const { return config_; }

private:
    QString data_type_;
    QVariant data_;
    QVariantMap config_;
};

// 可视化事件处理器
class VisualizationEventHandler : public QObject
{
    Q_OBJECT

public:
    static VisualizationEventHandler& instance();

    // 发送可视化事件
    void postVisualizationEvent(VisualizationEvent* event);

    // 注册可视化组件工厂
    void registerVisualizationFactory(const QString& data_type, 
                                    const QString& widget_name,
                                    std::function<QWidget*()> factory);

protected:
    bool event(QEvent* event) override;

private:
    void handleShowVisualization(ShowVisualizationEvent* event);
    void handleUpdateVisualization(const QString& node_id, const QVariant& data);
    void handleHideVisualization(const QString& node_id);

    QMap<QString, QMap<QString, std::function<QWidget*()>>> factories_;
    QMap<QString, QWidget*> active_widgets_;
};

// 节点可视化宏定义
#define SHOW_VISUALIZATION(node_id, data_type, data, config) \
    VisualizationEventHandler::instance().postVisualizationEvent( \
        new ShowVisualizationEvent(node_id, data_type, data, config))

#define UPDATE_VISUALIZATION(node_id, data_type, data) \
    VisualizationEventHandler::instance().postVisualizationEvent( \
        new UpdateVisualizationEvent(node_id, data_type, data))

#define HIDE_VISUALIZATION(node_id) \
    VisualizationEventHandler::instance().postVisualizationEvent( \
        new HideVisualizationEvent(node_id))

} // namespace robosense::visualization

/*
事件驱动模式的优势：

1. 完全解耦：
   - 节点通过事件发送可视化请求
   - UI系统异步处理事件
   - 节点不需要知道UI实现细节

2. 线程安全：
   - 事件自动在主线程中处理
   - 避免跨线程UI操作问题

3. 简单易用：
   - 宏定义简化调用
   - 节点只需要一行代码即可显示可视化

4. 扩展性好：
   - 容易添加新的事件类型
   - 支持复杂的可视化配置

使用示例：
```cpp
// 在UDP数据解析节点中
QVariantMap voltage_data;
voltage_data["voltage1"] = 3.3;
voltage_data["voltage2"] = 5.0;

SHOW_VISUALIZATION(name(), "numeric", voltage_data, 
                  {{"widget_type", "gauge"}, {"title", "电压监控"}});
```
*/
