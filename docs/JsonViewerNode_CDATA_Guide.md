# JsonViewerNode CDATA 使用指南

## 概述

JsonViewerNode 支持使用 CDATA 包装 JSON 数据，这样可以避免 JSON 中的特殊字符与 BehaviorTree XML 解析器产生冲突。

## 为什么需要 CDATA？

在 BehaviorTree 的 XML 配置中，当 JSON 数据包含以下特殊字符时，可能会导致 XML 解析错误：

- `<` 和 `>` (XML 标签字符)
- `&` (XML 实体字符)
- `"` (XML 属性引号)
- 复杂的嵌套结构

## CDATA 语法

```xml
<JsonViewerNode name="example"
    json_data='<![CDATA[{
  "your": "json data here",
  "with": "<special> characters & symbols"
}]]>'
    enable_visualization="true"/>
```

## 支持的特殊字符

使用 CDATA 包装后，JSON 数据可以包含：

### 1. XML 标签字符
```json
{
  "html_content": "<div class=\"container\"><p>Hello World</p></div>",
  "xml_data": "<root><item id=\"1\">value</item></root>"
}
```

### 2. 逻辑运算符
```json
{
  "validation_rules": {
    "temperature": "value > 0 && value < 100",
    "pressure": "pressure >= 1000 && pressure <= 1100"
  }
}
```

### 3. 特殊符号和引号
```json
{
  "device_name": "Sensor <Model-X>",
  "description": "High-precision sensor with \"special\" features & capabilities",
  "tags": ["<critical>", "&important", "\"priority\""]
}
```

### 4. 数据库连接字符串
```json
{
  "database": {
    "connection": "server=localhost;database=test;uid=user;pwd=****;",
    "query": "SELECT * FROM table WHERE id > {id} AND status = \"active\""
  }
}
```

## 使用示例

### 示例 1: 包含特殊字符的设备配置
```xml
<JsonViewerNode name="DeviceConfig"
    json_data='<![CDATA[{
  "device_info": {
    "name": "Sensor <Model-X>",
    "description": "High-precision sensor with <special> characters & symbols",
    "formula": "value > threshold && value < max_limit"
  },
  "measurements": {
    "temperature": 25.6,
    "humidity": 68.2,
    "pressure": 1013.25
  }
}]]>'
    enable_visualization="true"
    data_path="measurements"/>
```

### 示例 2: 包含 XML 内容的数据
```xml
<JsonViewerNode name="XmlContent"
    json_data='<![CDATA[{
  "xml_content": {
    "raw_xml": "<root><item id=\"1\"><name>Test</name></item></root>",
    "xpath": "//item[@id=\"1\"]/name"
  },
  "sensor_data": {
    "temperature": 28.5,
    "humidity": 72.1
  }
}]]>'
    enable_visualization="true"
    data_path="sensor_data"/>
```

## 技术实现

JsonViewerNode 会自动检测 CDATA 格式的 JSON 数据：

1. **检测 CDATA 标记**: 查找 `<![CDATA[` 和 `]]>` 标记
2. **提取 JSON 内容**: 从 CDATA 中提取纯 JSON 数据
3. **解析 JSON**: 使用 nlohmann/json 库解析提取的数据
4. **可视化处理**: 正常进行数据可视化

## 优势

1. **避免 XML 冲突**: 完全避免特殊字符与 XML 解析器的冲突
2. **保持原始格式**: JSON 数据保持原始格式，无需转义
3. **支持复杂数据**: 支持包含任意特殊字符的复杂 JSON 结构
4. **向后兼容**: 同时支持普通 JSON 数据和 HTML 编码数据

## 注意事项

1. **CDATA 内部不能包含 `]]>`**: 这是 XML CDATA 的限制
2. **保持格式正确**: 确保 `<![CDATA[` 和 `]]>` 标记完整
3. **JSON 格式验证**: CDATA 内的内容仍需要是有效的 JSON 格式

## 最佳实践

1. **优先使用 CDATA**: 当 JSON 包含特殊字符时，优先使用 CDATA 包装
2. **保持可读性**: 在 CDATA 中保持 JSON 的良好格式和缩进
3. **测试验证**: 使用测试程序验证 CDATA JSON 数据的正确性

通过使用 CDATA，JsonViewerNode 可以处理任意复杂的 JSON 数据，而不会与 BehaviorTree 的 XML 格式产生冲突。
