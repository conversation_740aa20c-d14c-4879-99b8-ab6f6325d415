# JsonViewerNode 可视化功能使用指南

## 📋 概述

JsonViewerNode 已经被改造为专门用于JSON数据可视化的节点。它去掉了复杂的UI创建逻辑，专注于JSON数据的解析和可视化显示，使用我们的可视化系统来展示数据。

## 🎯 主要特性

- ✅ **简化设计**: 去掉了复杂的UI创建和窗口管理
- ✅ **专注可视化**: 专门用于JSON数据的可视化显示
- ✅ **多种模式**: 支持数值、状态、自动三种可视化模式
- ✅ **灵活配置**: 丰富的配置选项，满足不同需求
- ✅ **易于使用**: 提供合理的默认值，开箱即用

## 🔧 端口配置

### 输入端口

| 端口名称 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `json_data` | String | 示例JSON | 输入的JSON字符串数据 |
| `json_file` | String | "" | JSON文件路径 |
| `enable_visualization` | Boolean | true | 是否启用可视化显示 |
| `visualization_mode` | String | "auto" | 可视化模式: auto/numeric/status |
| `widget_type` | String | "NumericTable" | 可视化组件: NumericTable/NumericGauge |
| `visualization_title` | String | "JSON数据可视化" | 可视化标题 |
| `visualization_duration` | Integer | 5000 | 可视化显示时长(毫秒) |
| `data_path` | String | "sensor_data" | JSON中数据的路径 |
| `min_value` | Double | 0.0 | 数值范围最小值 |
| `max_value` | Double | 100.0 | 数值范围最大值 |
| `decimal_places` | Integer | 2 | 小数位数 |

### 输出端口

| 端口名称 | 类型 | 描述 |
|---------|------|------|
| `result` | String | 处理结果状态 |
| `error_message` | String | 错误信息 |
| `json_size` | Integer | JSON数据大小（字节） |
| `visualization_shown` | Boolean | 可视化是否已显示 |
| `visualization_data_count` | Integer | 可视化数据项数量 |

## 📊 可视化模式

### 1. 数值模式 (numeric)
专门用于显示数值型数据，如传感器读数、测量值等。

```xml
<JsonViewerNode name="数值可视化"
              json_data='{"sensors": {"temp": 25.6, "humidity": 68.2}}'
              visualization_mode="numeric"
              widget_type="NumericTable"
              data_path="sensors"/>
```

### 2. 状态模式 (status)
用于显示布尔值和状态信息，如设备开关状态、连接状态等。

```xml
<JsonViewerNode name="状态可视化"
              json_data='{"status": {"power": true, "connected": false}}'
              visualization_mode="status"
              widget_type="NumericTable"
              data_path="status"/>
```

### 3. 自动模式 (auto)
根据组件类型自动选择合适的可视化方式。

```xml
<JsonViewerNode name="自动可视化"
              json_data='{"data": {"value1": 12.3, "value2": 45.6}}'
              visualization_mode="auto"
              widget_type="NumericGauge"
              data_path="data"/>
```

## 🎨 可视化组件

### NumericTable (数值表格)
以表格形式显示数值数据，适合显示多个数值。

### NumericGauge (数值仪表)
以仪表盘形式显示数值，适合显示单个或少量关键数值。

## 📝 默认JSON示例

节点提供了一个默认的JSON示例：

```json
{
  "sensor_data": {
    "temperature": 25.6,
    "humidity": 68.2,
    "pressure": 1013.25,
    "voltage": 3.3
  },
  "status": {
    "power_on": true,
    "sensor_ready": true,
    "error_count": 0
  }
}
```

## 🚀 使用示例

### 基础使用
```xml
<JsonViewerNode name="基础可视化"
              json_data='{"temp": 25.6, "humidity": 68.2}'
              enable_visualization="true"/>
```

### 高级配置
```xml
<JsonViewerNode name="高级可视化"
              json_data='{"sensors": {"temp": 25.6, "pressure": 1013.25}}'
              enable_visualization="true"
              visualization_mode="numeric"
              widget_type="NumericGauge"
              visualization_title="环境监测"
              visualization_duration="8000"
              data_path="sensors"
              min_value="0.0"
              max_value="50.0"
              decimal_places="1"/>
```

## 💡 最佳实践

1. **数据路径**: 使用 `data_path` 指定JSON中的具体数据位置
2. **合理范围**: 设置合适的 `min_value` 和 `max_value` 以获得最佳显示效果
3. **显示时长**: 根据数据重要性调整 `visualization_duration`
4. **组件选择**: 
   - 多个数值使用 `NumericTable`
   - 关键指标使用 `NumericGauge`

## 🔍 故障排除

- **可视化不显示**: 检查 `enable_visualization` 是否为 true
- **数据为空**: 检查 `data_path` 是否正确指向JSON中的数据
- **数值显示异常**: 检查 `min_value` 和 `max_value` 设置是否合理

## 📚 相关文件

- 示例配置: `examples/json_visualization_example.xml`
- 测试程序: `test_json_visualization.cpp`
- 源代码: `src/common/node_models/json_viewer_node.cpp`
