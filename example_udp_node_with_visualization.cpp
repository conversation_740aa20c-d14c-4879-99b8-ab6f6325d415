#include "src/common/node_models/udp_data_parser_node.h"
#include "src/common/visualization/visualization_manager.h"
#include "src/common/visualization/widgets/numeric_gauge_widget.h"

namespace robosense::lidar {

// 扩展UDP数据解析节点，添加可视化功能
class UdpDataParserNodeWithVisualization : public UdpDataParserNode
{
public:
  UdpDataParserNodeWithVisualization(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

protected:
  BT::NodeStatus tick() override;

private:
  // 解析电压电流数据
  void parseVoltageCurrentData(const QByteArray& _udp_data);
  
  // 解析图像数据
  void parseImageData(const QByteArray& _udp_data);
  
  // 可视化助手
  std::unique_ptr<NodeVisualizationHelper> visualization_helper_;
  
  // 配置参数
  QString visualization_type_;
  bool enable_visualization_;
  int visualization_duration_;
};

UdpDataParserNodeWithVisualization::UdpDataParserNodeWithVisualization(const std::string& _name, 
                                                                       const BT::NodeConfig& _config)
  : UdpDataParserNode(_name, _config)
{
  visualization_helper_ = std::make_unique<NodeVisualizationHelper>(QString::fromStdString(_name));
  
  // 读取可视化配置
  auto visualization_type_opt = getInput<std::string>("visualization_type");
  visualization_type_ = visualization_type_opt ? QString::fromStdString(visualization_type_opt.value()) : "none";
  
  auto enable_visualization_opt = getInput<bool>("enable_visualization");
  enable_visualization_ = enable_visualization_opt ? enable_visualization_opt.value() : false;
  
  auto visualization_duration_opt = getInput<int>("visualization_duration");
  visualization_duration_ = visualization_duration_opt ? visualization_duration_opt.value() : 5000;
}

BT::PortsList UdpDataParserNodeWithVisualization::providedPorts()
{
  auto base_ports = UdpDataParserNode::providedPorts();
  
  // 添加可视化相关端口
  base_ports.insert({
    BT::InputPort<std::string>("visualization_type", "none", "可视化类型: voltage_current, image, none"),
    BT::InputPort<bool>("enable_visualization", false, "是否启用可视化"),
    BT::InputPort<int>("visualization_duration", 5000, "可视化显示时长(毫秒)，0表示持续显示"),
    BT::InputPort<std::string>("widget_type", "", "指定可视化组件类型: NumericGauge, NumericTable, ImageDisplay"),
    BT::InputPort<std::string>("visualization_title", "", "可视化标题"),
    BT::InputPort<double>("min_value", 0.0, "数值范围最小值"),
    BT::InputPort<double>("max_value", 10.0, "数值范围最大值")
  });
  
  return base_ports;
}

BT::NodeStatus UdpDataParserNodeWithVisualization::tick()
{
  // 先执行基础的UDP数据解析
  BT::NodeStatus base_result = UdpDataParserNode::tick();
  
  if (base_result != BT::NodeStatus::SUCCESS || !enable_visualization_)
  {
    return base_result;
  }
  
  // 获取解析后的UDP数据
  auto parsed_data_opt = getInput<std::string>("parsed_data");
  if (!parsed_data_opt)
  {
    return base_result;
  }
  
  QByteArray udp_data = QByteArray::fromStdString(parsed_data_opt.value());
  
  // 根据可视化类型进行不同的数据解析和显示
  if (visualization_type_ == "voltage_current")
  {
    parseVoltageCurrentData(udp_data);
  }
  else if (visualization_type_ == "image")
  {
    parseImageData(udp_data);
  }
  
  return base_result;
}

void UdpDataParserNodeWithVisualization::parseVoltageCurrentData(const QByteArray& _udp_data)
{
  // 假设UDP数据格式：前4字节是电压1，接下来4字节是电压2，然后是电流数据
  if (_udp_data.size() < 16)
  {
    LOG_ERROR("[UdpVisualizationNode] UDP数据长度不足，无法解析电压电流数据");
    return;
  }
  
  // 解析数据（这里是示例，实际解析逻辑根据你的协议）
  const float* float_data = reinterpret_cast<const float*>(_udp_data.constData());
  
  QVariantMap voltage_current_data;
  voltage_current_data["电压1"] = static_cast<double>(float_data[0]);
  voltage_current_data["电压2"] = static_cast<double>(float_data[1]);
  voltage_current_data["电流1"] = static_cast<double>(float_data[2]);
  voltage_current_data["电流2"] = static_cast<double>(float_data[3]);
  
  // 构建可视化配置
  QVariantMap visualization_config;
  
  // 读取配置参数
  auto widget_type_opt = getInput<std::string>("widget_type");
  if (widget_type_opt && !widget_type_opt.value().empty())
  {
    visualization_config["widget_type"] = QString::fromStdString(widget_type_opt.value());
  }
  else
  {
    visualization_config["widget_type"] = "NumericGauge";  // 默认使用仪表盘
  }
  
  auto title_opt = getInput<std::string>("visualization_title");
  visualization_config["title"] = title_opt ? QString::fromStdString(title_opt.value()) : "电压电流监控";
  
  auto min_value_opt = getInput<double>("min_value");
  visualization_config["min_value"] = min_value_opt ? min_value_opt.value() : 0.0;
  
  auto max_value_opt = getInput<double>("max_value");
  visualization_config["max_value"] = max_value_opt ? max_value_opt.value() : 10.0;
  
  if (visualization_duration_ > 0)
  {
    visualization_config["auto_close_duration"] = visualization_duration_;
  }
  
  // 显示可视化
  visualization_helper_->showNumericData(voltage_current_data, visualization_config);
  
  LOG_INFO("[UdpVisualizationNode] 显示电压电流数据: 电压1={}, 电压2={}, 电流1={}, 电流2={}", 
           voltage_current_data["电压1"].toDouble(),
           voltage_current_data["电压2"].toDouble(),
           voltage_current_data["电流1"].toDouble(),
           voltage_current_data["电流2"].toDouble());
}

void UdpDataParserNodeWithVisualization::parseImageData(const QByteArray& _udp_data)
{
  // 假设UDP数据格式：前8字节是宽度和高度，后面是图像数据
  if (_udp_data.size() < 12)
  {
    LOG_ERROR("[UdpVisualizationNode] UDP数据长度不足，无法解析图像数据");
    return;
  }
  
  // 解析图像尺寸
  const int* int_data = reinterpret_cast<const int*>(_udp_data.constData());
  int image_width = int_data[0];
  int image_height = int_data[1];
  
  // 提取图像数据
  QByteArray image_data = _udp_data.mid(8);  // 跳过前8字节的尺寸信息
  
  // 验证数据长度
  int expected_size = image_width * image_height;  // 假设是8位灰度图
  if (image_data.size() < expected_size)
  {
    LOG_ERROR("[UdpVisualizationNode] 图像数据长度不匹配: 期望={}, 实际={}", 
              expected_size, image_data.size());
    return;
  }
  
  // 构建可视化配置
  QVariantMap visualization_config;
  
  auto title_opt = getInput<std::string>("visualization_title");
  visualization_config["title"] = title_opt ? QString::fromStdString(title_opt.value()) : "UDP图像数据";
  
  visualization_config["enable_zoom"] = true;
  visualization_config["enable_save"] = true;
  visualization_config["scale_factor"] = 1.0;
  
  if (visualization_duration_ > 0)
  {
    visualization_config["auto_close_duration"] = visualization_duration_;
  }
  
  // 显示图像
  visualization_helper_->showImageData(image_data, image_width, image_height, "grayscale", visualization_config);
  
  LOG_INFO("[UdpVisualizationNode] 显示图像数据: {}x{}, 数据大小={}", 
           image_width, image_height, image_data.size());
}

}  // namespace robosense::lidar

/*
使用示例XML配置：

1. 电压电流监控：
<UdpDataParserNodeWithVisualization 
    name="VoltageCurrentMonitor"
    ip_address="*************"
    port="8080"
    visualization_type="voltage_current"
    enable_visualization="true"
    visualization_duration="5000"
    widget_type="NumericGauge"
    visualization_title="设备电压电流监控"
    min_value="0.0"
    max_value="12.0"
    parsed_data="{voltage_current_data}" />

2. 图像显示：
<UdpDataParserNodeWithVisualization 
    name="ImageDataViewer"
    ip_address="*************"
    port="8081"
    visualization_type="image"
    enable_visualization="true"
    visualization_duration="0"
    visualization_title="实时图像数据"
    parsed_data="{image_data}" />

优势：
1. 完全符合你的编码规范
2. 与现有UDP节点无缝集成
3. 通过配置参数灵活控制可视化行为
4. 支持多种数据类型和显示方式
5. 线程安全，自动在主线程中显示UI
6. 支持自动关闭和持续显示模式
*/
