# UDP数据解析节点集成文档

## 概述

UDP数据解析节点是一个基于MEMSUDP的BehaviorTree节点，用于接收UDP数据并通过Lua脚本进行自定义解析。该节点支持实时数据接收、灵活的脚本化解析逻辑，以及与BehaviorTree黑板的无缝集成。

## 特性

- **基于MEMSUDP的UDP通信**: 使用高性能的MEMSUDP库进行UDP数据接收
- **Lua脚本化解析**: 支持通过Lua脚本进行灵活的数据解析
- **黑板集成**: 解析结果可直接存储到BehaviorTree黑板
- **同步/异步模式**: 提供同步和异步两种执行模式
- **错误处理**: 完善的错误处理和超时机制
- **数据格式支持**: 支持字节数组和十六进制字符串格式
- **实时监控**: 提供数据接收状态和统计信息

## 节点类型

### 1. UdpDataParser (同步节点)
- **类型**: SyncActionNode
- **用途**: 适用于简单的数据解析任务
- **特点**: 阻塞执行，直到数据接收或超时

### 2. UdpDataParserAsync (异步节点)
- **类型**: StatefulActionNode  
- **用途**: 适用于长时间运行的解析任务
- **特点**: 非阻塞执行，支持状态管理

## 输入端口

| 端口名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| udp_ip | string | "0.0.0.0" | UDP监听IP地址 |
| udp_port | int | 6699 | UDP监听端口 |
| packet_length | int | 1024 | 期望的数据包长度 |
| timeout_ms | int | 1000 | 接收超时时间(毫秒) |
| group_ip | string | "" | 组播IP地址(可选) |
| parser_script | string | 默认脚本 | Lua解析脚本内容 |
| parser_script_file | string | "" | Lua解析脚本文件路径 |
| auto_start | bool | true | 是否自动启动UDP接收 |

## 输出端口

| 端口名 | 类型 | 描述 |
|--------|------|------|
| parse_result | string | 解析结果信息 |
| error_message | string | 错误信息 |
| data_length | int | 接收到的数据长度 |
| data_hex | string | 数据的十六进制表示 |
| data_available | bool | 是否有新数据可用 |
| timeout_occurred | bool | 是否发生超时 |

## Lua脚本环境

### 预定义变量

解析脚本执行时，以下变量会自动设置：

```lua
data_bytes    -- 字节数组 (Lua table, 索引从1开始)
data_hex      -- 十六进制字符串
data_length   -- 数据长度 (整数)
```

### 黑板访问函数

```lua
-- 基本访问函数
bb_get(key)                    -- 获取字符串值
bb_set(key, value, type)       -- 设置值 (type: "string", "int", "double", "bool")
bb_has(key)                    -- 检查键是否存在

-- 类型特化函数
bb_get_int(key)                -- 获取整数值
bb_get_double(key)             -- 获取浮点值  
bb_get_bool(key)               -- 获取布尔值
bb_set_int(key, value)         -- 设置整数值
bb_set_double(key, value)      -- 设置浮点值
bb_set_bool(key, value)        -- 设置布尔值
```

### 日志函数

```lua
log(message, level)            -- 记录日志 (level: "info", "warn", "error")
```

### 返回值常量

```lua
OK                             -- 成功状态
NG                             -- 失败状态  
RUNNING                        -- 运行中状态
```

## 使用示例

### 基本XML配置

```xml
<UdpDataParser 
    udp_ip="0.0.0.0"
    udp_port="6699"
    packet_length="64"
    timeout_ms="5000"
    parser_script="
        log('接收到数据: ' .. data_length .. ' 字节', 'info')
        bb_set('udp_data', data_hex)
        bb_set_int('data_size', data_length)
        return OK
    "
    parse_result="{result}"
    error_message="{error}" />
```

### 使用脚本文件

```xml
<UdpDataParser 
    udp_ip="*************"
    udp_port="8080"
    packet_length="128"
    parser_script_file="scripts/sensor_parser.lua"
    parse_result="{result}" />
```

### 传感器数据解析示例

```lua
-- 解析传感器数据包
log('开始解析传感器数据', 'info')

-- 检查数据包头部
if data_length >= 4 then
    local header = 0
    for i = 1, 4 do
        header = header * 256 + data_bytes[i]
    end
    
    if header == 0x12345678 then
        log('有效的数据包头部', 'info')
        
        -- 解析传感器类型
        if data_length >= 5 then
            local sensor_type = data_bytes[5]
            if sensor_type == 0x01 then
                bb_set('sensor_type', 'TEMPERATURE')
            elseif sensor_type == 0x02 then
                bb_set('sensor_type', 'HUMIDITY')
            end
        end
        
        -- 解析传感器值 (假设在第6-9字节)
        if data_length >= 9 then
            local value = 0
            for i = 6, 9 do
                value = value * 256 + data_bytes[i]
            end
            bb_set_double('sensor_value', value / 1000.0)
        end
        
        return OK
    else
        log('无效的数据包头部', 'error')
        return NG
    end
else
    log('数据包太短', 'error')
    return NG
end
```

## 集成到现有项目

### 1. 注册节点

```cpp
#include "src/common/node_models/udp_data_parser_factory.h"

// 在BehaviorTreeFactory中注册节点
BehaviorTreeFactory factory;
registerUdpDataParserNodes(factory);
```

### 2. CMakeLists.txt配置

节点源文件会自动通过GLOB_RECURSE包含到构建中：

```cmake
file(GLOB_RECURSE COMMON_CPP
    ${CMAKE_CURRENT_SOURCE_DIR}/node_models/*.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/node_models/*.h)
```

### 3. 依赖项

确保项目包含以下依赖：
- BehaviorTree.CPP
- Sol2 (Lua绑定)
- MEMSUDP
- Boost.Asio

## 测试和调试

### 发送测试数据

使用netcat发送测试UDP数据：

```bash
# 发送传感器数据包
echo -n -e '\x12\x34\x56\x78\x01\x00\x01\x02\x03' | nc -u localhost 6699

# 发送状态数据包  
echo -n -e '\xAA\xBB\xCC\xDD\x02\x05\x06\x07\x08' | nc -u localhost 6699
```

### 调试技巧

1. **启用详细日志**: 在Lua脚本中使用`log()`函数输出调试信息
2. **检查黑板状态**: 通过输出端口监控解析结果
3. **验证数据格式**: 使用`data_hex`输出检查接收的原始数据
4. **超时处理**: 监控`timeout_occurred`端口处理网络异常

## 性能考虑

- **数据包大小**: 根据实际需求设置合适的`packet_length`
- **超时设置**: 平衡响应性和资源使用，设置合适的`timeout_ms`
- **脚本复杂度**: 避免在Lua脚本中进行过于复杂的计算
- **内存使用**: 大数据包会增加内存使用，注意监控

## 故障排除

### 常见问题

1. **UDP数据接收失败**
   - 检查防火墙设置
   - 验证IP地址和端口配置
   - 确认网络连接

2. **Lua脚本执行错误**
   - 检查脚本语法
   - 验证变量名和函数调用
   - 查看错误日志

3. **数据解析异常**
   - 验证数据包格式
   - 检查字节序问题
   - 确认数据长度

### 日志分析

节点会输出详细的日志信息，包括：
- UDP连接状态
- 数据接收情况  
- Lua脚本执行结果
- 错误和异常信息

## 扩展功能

### 自定义数据处理

可以通过继承基类实现自定义的数据处理逻辑：

```cpp
class CustomUdpParser : public UdpDataParserNode
{
protected:
    BT::NodeStatus executeLuaParser(const std::vector<char>& data) override
    {
        // 自定义解析逻辑
        return BT::NodeStatus::SUCCESS;
    }
};
```

### 多端口支持

可以创建多个UDP解析节点实例来监听不同端口：

```xml
<Parallel>
    <UdpDataParser udp_port="6699" parser_script="..." />
    <UdpDataParser udp_port="6700" parser_script="..." />
</Parallel>
```
