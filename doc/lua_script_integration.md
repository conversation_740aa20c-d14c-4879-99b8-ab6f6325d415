# Lua 脚本与 BehaviorTree 黑板集成

## 概述

本项目集成了 Sol2 库，实现了 Lua 脚本与 BehaviorTree.CPP 黑板系统的无缝连接。这使得用户可以：

- 在 Lua 脚本中访问和修改 BT 黑板数据
- 自定义节点可以执行 Lua 脚本来实现复杂逻辑
- 节点间通过黑板和 Lua 脚本进行数据交互

## 功能特性

### 🚀 核心功能

1. **黑板数据访问**: Lua 脚本可以读取和写入 BT 黑板中的数据
2. **类型安全**: 支持 int、double、string、bool 等基本数据类型
3. **同步/异步执行**: 提供同步和异步两种 Lua 脚本节点
4. **错误处理**: 完善的错误处理和日志记录
5. **脚本文件支持**: 可以从外部文件加载 Lua 脚本

### 📋 可用的 Lua 函数

#### 黑板访问函数

```lua
-- 通用读写函数
bb_get(key)                    -- 获取值（返回字符串）
bb_set(key, value, type)       -- 设置值
bb_has(key)                    -- 检查键是否存在

-- 类型特化函数
bb_get_int(key)                -- 获取整数值
bb_get_double(key)             -- 获取浮点数值
bb_get_bool(key)               -- 获取布尔值

bb_set_int(key, value)         -- 设置整数值
bb_set_double(key, value)      -- 设置浮点数值
bb_set_bool(key, value)        -- 设置布尔值
```

#### 日志函数

```lua
log(message, level)            -- 输出日志
-- level 可以是: "info", "warn", "error"
```

#### BT 状态常量

```lua
OK                             -- 成功状态
NG                             -- 失败状态
RUNNING                        -- 运行中状态
```

## 节点类型

### LuaScript (同步节点)

用于执行短时间的 Lua 脚本。

**端口:**
- `script` (输入): 要执行的 Lua 脚本代码
- `script_file` (输入): 要执行的 Lua 脚本文件路径
- `result` (输出): 脚本执行结果
- `error_message` (输出): 错误信息（如果有）

### LuaAsyncScript (异步节点)

用于执行可能需要较长时间的 Lua 脚本。

**端口:** 与 LuaScript 相同

## 使用示例

### 1. 基本黑板操作

```xml
<LuaScript script="
    -- 读取黑板数据
    local counter = bb_get_int('counter') or 0
    local message = bb_get('message') or 'default'
    
    -- 修改黑板数据
    bb_set_int('counter', counter + 1)
    bb_set('status', 'processed')
    bb_set_bool('completed', true)
    
    -- 记录日志
    log('Counter updated to: ' .. (counter + 1), 'info')
    
    return SUCCESS
" result="{lua_result}" />
```

### 2. 从文件执行脚本

```xml
<LuaScript script_file="scripts/data_processor.lua" 
           result="{processing_result}" />
```

### 3. 传感器数据处理示例

```lua
-- sensor_processor.lua
local sensor_x = bb_get_double('sensor_x') or 0.0
local sensor_y = bb_get_double('sensor_y') or 0.0
local sensor_z = bb_get_double('sensor_z') or 0.0

-- 计算模长
local magnitude = math.sqrt(sensor_x^2 + sensor_y^2 + sensor_z^2)

-- 检查阈值
local threshold = bb_get_double('threshold') or 1.0
if magnitude > threshold then
    bb_set('alarm_status', 'HIGH')
    log('Sensor magnitude exceeds threshold: ' .. magnitude, 'warn')
    return NG
else
    bb_set('alarm_status', 'NORMAL')
    bb_set_double('sensor_magnitude', magnitude)
    return OK
end
```

### 4. 完整的 XML 示例

```xml
<root BTCPP_format="4">
    <BehaviorTree ID="LuaDemo">
        <Sequence>
            <!-- 初始化数据 -->
            <Script code="counter = 0; threshold = 5.0" />
            
            <!-- Lua 脚本处理 -->
            <LuaScript script="
                local count = bb_get_int('counter')
                bb_set_int('counter', count + 1)
                
                if count >= 10 then
                    bb_set('status', 'completed')
                    return SUCCESS
                else
                    bb_set('status', 'processing')
                    return RUNNING
                end
            " result="{result}" />
            
            <!-- 根据结果执行后续动作 -->
            <Switch variable="{status}">
                <Case key="completed">
                    <AlwaysSuccess />
                </Case>
                <Case key="processing">
                    <AlwaysFailure />
                </Case>
            </Switch>
        </Sequence>
    </BehaviorTree>
</root>
```

## 最佳实践

### 1. 错误处理

```lua
-- 安全的黑板访问
local value = bb_get_int('sensor_value')
if not value then
    log('Sensor value not found, using default', 'warn')
    value = 0
end
```

### 2. 类型检查

```lua
-- 检查键是否存在
if bb_has('important_data') then
    local data = bb_get('important_data')
    -- 处理数据
else
    log('Important data missing', 'error')
    return NG
end
```

### 3. 性能考虑

- 对于简单操作，使用同步 `LuaScript` 节点
- 对于复杂计算或可能阻塞的操作，使用异步 `LuaAsyncScript` 节点
- 避免在 Lua 脚本中进行大量的黑板读写操作

### 4. 调试技巧

```lua
-- 使用日志进行调试
log('Debug: current state = ' .. bb_get('current_state'), 'info')

-- 输出所有相关变量
local x = bb_get_double('x')
local y = bb_get_double('y')
log('Position: (' .. x .. ', ' .. y .. ')', 'info')
```

## 集成到自定义节点

你可以在自定义节点中使用 Lua 脚本功能：

```cpp
#include "src/common/node_models/lua_script_factory.h"

// 在你的工厂注册函数中
void registerMyNodes(BT::BehaviorTreeFactory& factory)
{
    // 注册 Lua 脚本节点
    registerLuaScriptNodes(factory);
    
    // 注册你的自定义节点
    factory.registerNodeType<MyCustomNode>("MyCustomNode");
}
```

## 故障排除

### 常见问题

1. **脚本执行失败**: 检查 Lua 语法和黑板键名
2. **类型转换错误**: 确保使用正确的类型特化函数
3. **黑板键不存在**: 使用 `bb_has()` 检查键是否存在

### 调试方法

1. 使用 `log()` 函数输出调试信息
2. 检查 `error_message` 输出端口
3. 查看应用程序日志

## 示例文件

- `examples/lua_blackboard_example.cpp`: 基本黑板交互示例
- `examples/custom_lua_node_example.cpp`: 自定义节点集成示例
- `examples/scripts/sensor_data_processor.lua`: 传感器数据处理脚本

这个集成为 BehaviorTree 提供了强大的脚本化能力，使得复杂的逻辑可以通过 Lua 脚本灵活实现，同时保持与 C++ 代码的无缝集成。
