# DisplaySwitcherWidget - 节点属性侧边栏管理器

## 概述

`DisplaySwitcherWidget` 是一个用于管理节点属性显示的侧边栏组件，它替代了原有的双击弹窗显示方式，提供了更加直观和便捷的节点属性编辑体验。

## 主要功能

### 1. 实时属性显示
- 点击任意已创建的节点，即可在侧边栏实时显示该节点的属性面板
- 支持多个节点之间的快速切换，无需关闭弹窗
- 当没有选择节点时，显示友好的提示信息

### 2. 智能生命周期管理
- 自动管理每个节点的 PropertyView 实例
- 当节点被创建时，自动为其准备属性面板
- 当节点被删除时，自动清理对应的属性面板资源
- 避免内存泄漏和资源浪费

### 3. 属性变化监听
- 实时监听属性值的变化
- 支持属性变化事件的转发和处理
- 提供详细的日志记录用于调试

## 架构设计

### 类结构
```cpp
class DisplaySwitcherWidget : public QWidget
{
    Q_OBJECT
public:
    // 构造函数和析构函数
    explicit DisplaySwitcherWidget(QWidget* parent = nullptr);
    ~DisplaySwitcherWidget() override;

public Q_SLOTS:
    // 节点生命周期管理
    void onNodeSelected(std::size_t node_id, BtNodeModel* node_model);
    void onNodeCreated(std::size_t node_id, BtNodeModel* node_model);
    void onNodeDeleted(std::size_t node_id);
    
    // 界面管理
    void clearAllNodes();
    void refreshCurrentView();

Q_SIGNALS:
    // 属性变化通知
    void propertyValueChanged(std::size_t node_id, const std::string& group, 
                             const std::string& key, const QVariant& value);
};
```

### 核心组件
1. **QStackedWidget**: 用于在不同节点的属性面板之间切换
2. **PropertyView 映射**: 维护节点ID到PropertyView的映射关系
3. **BtNodeModel 映射**: 维护节点ID到BtNodeModel的映射关系
4. **默认页面**: 当没有选择节点时显示的提示页面

## 集成方式

### 1. MainWindow 集成
```cpp
// 在 MainWindow 中创建 DisplaySwitcherWidget
display_switcher_widget_ = new DisplaySwitcherWidget(properties_dock_);
properties_dock_->setWidget(display_switcher_widget_);

// 连接节点选择事件
QObject::connect(&flow_editor, &TestFlowEditor::nodeSelected, 
                 this, &MainWindow::slotNodeSelected);
```

### 2. 节点事件处理
```cpp
void MainWindow::slotNodeSelected(std::size_t node_id)
{
    // 获取节点模型
    auto* bt_node_model = data_graph_model->delegateModel<BtNodeModel>(node_id);
    
    // 使用 DisplaySwitcherWidget 显示属性
    display_switcher_widget_->onNodeSelected(node_id, bt_node_model);
}
```

## 使用流程

### 1. 节点创建流程
1. 用户在编辑器中创建新节点
2. `TestFlowEditor` 发出节点创建信号
3. `MainWindow::slotNodeCreated` 被调用
4. `DisplaySwitcherWidget::onNodeCreated` 为新节点准备属性面板

### 2. 节点选择流程
1. 用户点击编辑器中的节点
2. `TestFlowEditor` 发出节点选择信号
3. `MainWindow::slotNodeSelected` 被调用
4. `DisplaySwitcherWidget::onNodeSelected` 显示对应的属性面板

### 3. 节点删除流程
1. 用户删除编辑器中的节点
2. `TestFlowEditor` 发出节点删除信号
3. `MainWindow::slotNodeDeleted` 被调用
4. `DisplaySwitcherWidget::onNodeDeleted` 清理对应的属性面板

## 技术特点

### 1. 内存管理
- 使用智能指针管理 PropertyView 实例
- 采用空删除器避免重复释放 PropertyModel
- 自动清理不再使用的资源

### 2. 信号连接
- 连接到 PropertyModel 的 `signalValueChanged` 信号
- 支持属性变化的实时监听和转发
- 提供详细的调试日志

### 3. 界面优化
- 隐藏 PropertyView 的所有按钮，简化界面
- 支持单组隐藏模式，优化显示效果
- 提供友好的默认提示页面

## 优势对比

### 相比原有弹窗方式的优势：
1. **用户体验更好**: 无需双击，点击即可查看属性
2. **操作更便捷**: 支持多节点间快速切换
3. **界面更整洁**: 侧边栏固定显示，不会遮挡编辑区域
4. **资源管理更优**: 智能的生命周期管理，避免资源泄漏

### 技术优势：
1. **架构清晰**: 职责分离，易于维护和扩展
2. **性能优化**: 按需创建，及时清理
3. **扩展性强**: 支持自定义属性变化处理逻辑
4. **调试友好**: 详细的日志记录

## 注意事项

1. **PropertyModel 生命周期**: PropertyModel 由 BtNodeModel 管理，DisplaySwitcherWidget 只持有引用
2. **信号连接**: 确保在节点删除时正确断开信号连接，避免悬空指针
3. **线程安全**: 所有操作都在主线程中进行，无需考虑线程安全问题
4. **错误处理**: 对空指针和无效节点ID进行了充分的错误检查

## 未来扩展

1. **属性搜索**: 可以添加属性搜索功能
2. **属性分组**: 支持更复杂的属性分组显示
3. **历史记录**: 记录属性修改历史
4. **批量编辑**: 支持多节点属性的批量修改
