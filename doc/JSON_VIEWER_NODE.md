# JsonViewerNode 使用文档

## 概述

JsonViewerNode 是一个专门用于处理和显示 JSON 数据的行为树节点。它基于 q_json_model 库，提供了强大的 JSON 数据可视化功能，支持多种显示模式和配置选项。

## 核心功能

### 1. 数据输入方式
- **JSON 字符串**：直接输入 JSON 格式的字符串数据
- **JSON 文件**：从文件系统加载 JSON 文件
- **动态数据**：接收其他节点输出的 JSON 数据

### 2. 显示模式
- **自动弹窗** (`auto_popup`)：创建独立窗口显示 JSON 树形结构
- **Widget 嵌入** (`embed_widget`)：嵌入到指定的 QWidget 中
- **静默处理** (`none`)：只处理数据，不显示界面

### 3. 高级特性
- **树形视图**：以层次结构显示 JSON 数据
- **可编辑模式**：支持直接编辑 JSON 值
- **异常过滤**：忽略指定的键名（如注释）
- **自动展开**：控制节点的展开/收缩状态
- **样式自定义**：支持 CSS 样式定制

## 端口配置

### 输入端口

| 端口名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `json_data` | string | "" | 输入的 JSON 字符串数据 |
| `json_file` | string | "" | JSON 文件路径 |
| `widget_view` | string | "" | 目标显示 Widget 的名称或指针 |
| `display_mode` | string | "auto_popup" | 显示模式：none/auto_popup/embed_widget |
| `window_title` | string | "JSON Viewer" | 窗口标题 |
| `window_width` | int | 800 | 窗口宽度 |
| `window_height` | int | 600 | 窗口高度 |
| `auto_expand` | bool | true | 是否自动展开所有节点 |
| `read_only` | bool | true | 是否只读模式 |
| `exceptions` | string | "" | 忽略的 JSON 键名列表，逗号分隔 |

### 输出端口

| 端口名 | 类型 | 描述 |
|--------|------|------|
| `result` | string | 处理结果状态 |
| `error_message` | string | 错误信息 |
| `view_visible` | bool | 视图是否可见 |
| `json_size` | int | JSON 数据大小（字节） |

## 使用示例

### 1. 基础用法 - 显示 JSON 字符串

```xml
<JsonViewerNode
    json_data='{
        "name": "传感器数据",
        "timestamp": "2024-01-15T10:30:00Z",
        "sensors": [
            {"id": 1, "type": "temperature", "value": 25.6},
            {"id": 2, "type": "humidity", "value": 65.2}
        ]
    }'
    display_mode="auto_popup"
    window_title="传感器数据查看器"
    window_width="900"
    window_height="700"
    result="{json_result}"
    error_message="{json_error}" />
```

### 2. 从文件加载 JSON

```xml
<JsonViewerNode
    json_file="config.json"
    display_mode="auto_popup"
    window_title="配置文件查看器"
    auto_expand="false"
    read_only="false"
    result="{file_result}" />
```

### 3. 静默处理（不显示界面）

```xml
<JsonViewerNode
    json_data="{dynamic_json_data}"
    display_mode="none"
    result="{process_result}"
    json_size="{data_size}" />
```

### 4. 嵌入到指定 Widget

```xml
<JsonViewerNode
    json_data="{config_data}"
    display_mode="embed_widget"
    widget_view="config_panel"
    read_only="false"
    result="{embed_result}" />
```

### 5. 与其他节点集成

```xml
<Sequence>
    <!-- UDP 数据解析 -->
    <UdpDataParserNode
        udp_port="6699"
        output_json="{udp_json_data}"
        data_available="{udp_has_data}" />
        
    <!-- 条件显示 JSON -->
    <Switch variable="{udp_has_data}">
        <Case key="true">
            <JsonViewerNode
                json_data="{udp_json_data}"
                display_mode="auto_popup"
                window_title="UDP数据解析结果"
                result="{json_display_result}" />
        </Case>
    </Switch>
</Sequence>
```

## 高级配置

### 异常过滤

忽略特定的键名（通常用于过滤注释）：

```xml
<JsonViewerNode
    json_data="{data_with_comments}"
    exceptions="// comment,_debug,_internal,__metadata"
    display_mode="auto_popup" />
```

### 样式自定义

节点内部使用预定义的 CSS 样式，包括：
- 交替行颜色
- 选中状态高亮
- 悬停效果
- 边框和间距

### 窗口配置

```xml
<JsonViewerNode
    json_data="{large_dataset}"
    display_mode="auto_popup"
    window_title="大数据集查看器"
    window_width="1200"
    window_height="800"
    auto_expand="false" />
```

## 错误处理

### 常见错误类型

1. **JSON 解析错误**
   - 原因：输入的 JSON 格式不正确
   - 解决：检查 JSON 语法，确保格式正确

2. **文件加载失败**
   - 原因：文件不存在或无权限访问
   - 解决：检查文件路径和权限

3. **Widget 查找失败**
   - 原因：指定的 Widget 不存在
   - 解决：确保 Widget 名称正确或使用弹窗模式

### 错误信息获取

```xml
<JsonViewerNode
    json_data="{potentially_invalid_json}"
    result="{result_status}"
    error_message="{error_details}" />
    
<Script code="
    if (result_status != 'SUCCESS') {
        console.log('JSON处理失败: ' + error_details);
    }
" />
```

## 性能考虑

### 大数据处理

对于大型 JSON 数据：
- 设置 `auto_expand="false"` 避免自动展开所有节点
- 考虑使用 `display_mode="none"` 进行静默处理
- 监控 `json_size` 输出了解数据大小

### 内存管理

- 节点会自动管理 QJsonModel 和 QTreeView 的生命周期
- 窗口关闭时会自动清理资源
- 支持多个实例同时运行

## 集成建议

### 与数据源集成

1. **UDP 解析器输出**：接收 UdpDataParserNode 的 JSON 输出
2. **文件监控**：配合文件监控节点动态加载配置
3. **网络数据**：显示从 API 获取的 JSON 响应

### 工作流集成

```xml
<Sequence>
    <!-- 数据获取 -->
    <DataSourceNode output_json="{raw_data}" />
    
    <!-- 数据验证 -->
    <JsonValidatorNode 
        input_json="{raw_data}"
        is_valid="{data_valid}" />
        
    <!-- 条件显示 -->
    <Switch variable="{data_valid}">
        <Case key="true">
            <JsonViewerNode
                json_data="{raw_data}"
                display_mode="auto_popup" />
        </Case>
        <Case key="false">
            <Script code="console.log('数据验证失败');" />
        </Case>
    </Switch>
</Sequence>
```

## 编译和部署

### 依赖要求

- Qt5 (Core, Widgets, Gui)
- q_json_model 库
- BehaviorTree.CPP

### 编译配置

确保在 CMakeLists.txt 中包含：

```cmake
# 添加 QJsonModel 库
add_subdirectory(${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model)

# 链接依赖
target_link_libraries(your_target
    Qt5::QJsonModel
    Qt5::Core
    Qt5::Widgets
    Qt5::Gui
)
```

### 注册节点

```cpp
#include "src/common/node_models/json_viewer_node.h"

// 在 BehaviorTreeFactory 中注册
factory.registerNodeType<JsonViewerNode>("JsonViewerNode");
```

## 最佳实践

1. **数据验证**：在显示前验证 JSON 格式
2. **错误处理**：始终检查 `result` 和 `error_message` 输出
3. **性能优化**：对大数据使用适当的显示模式
4. **用户体验**：为窗口设置有意义的标题
5. **资源管理**：及时关闭不需要的显示窗口

JsonViewerNode 为 JSON 数据的可视化和处理提供了强大而灵活的解决方案，适用于调试、监控、配置管理等多种场景。
