# UDP 数据解析节点优化版本

## 概述

UdpDataParserNode 是一个通用的 UDP 数据解析节点，通过 Lua 脚本自定义解析逻辑，支持：
- 自定义数据输出供其他节点使用
- 黑板数据存储（支持前缀和事务）
- 常用二进制解析辅助函数
- 灵活的端口输出配置

## 输入端口

| 端口名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| udp_ip | string | "0.0.0.0" | UDP监听IP地址 |
| udp_port | int | 6699 | UDP监听端口 |
| packet_length | int | 1024 | 期望的数据包长度 |
| timeout_ms | int | 1000 | 接收超时时间(毫秒) |
| group_ip | string | "" | 组播IP地址(可选) |
| parser_script | string | 默认脚本 | Lua解析脚本 |
| parser_script_file | string | "" | Lua解析脚本文件路径 |
| auto_start | bool | true | 是否自动启动UDP接收 |
| bb_prefix | string | "" | 黑板键前缀(默认使用节点名) |

## 输出端口

| 端口名 | 类型 | 说明 |
|--------|------|------|
| parse_result | string | 解析结果状态 |
| error_message | string | 错误信息 |
| data_length | int | 接收到的数据长度 |
| data_hex | string | 数据的十六进制表示 |
| output_json | string | 自定义输出数据JSON |
| data_available | bool | 是否有新数据可用 |
| timeout_occurred | bool | 是否发生超时 |

## Lua API

### 基础数据访问
- `data_bytes`: 字节数组（Lua表，从1开始索引）
- `data_hex`: 十六进制字符串
- `data_length`: 数据长度

### 黑板操作
```lua
-- 基础操作（必须3参数）
bb_set(key, value, type)  -- type: "string"|"int"|"double"|"bool"
bb_get(key)               -- 返回字符串
bb_has(key)               -- 检查键是否存在

-- 类型特化函数
bb_set_int(key, value)
bb_set_double(key, value)
bb_set_bool(key, value)
bb_get_int(key)
bb_get_double(key)
bb_get_bool(key)
```

### 事务操作
```lua
begin_scope(prefix?)      -- 开始事务，可选择覆盖前缀
commit_scope()           -- 提交事务，一次性写入黑板
```

### 自定义输出
```lua
clear_output()           -- 清空输出数据
set_output(key, value)   -- 设置输出数据
```

### 解析辅助函数
```lua
read_u8(offset)          -- 读取无符号8位整数
slice_hex(offset, len)   -- 提取十六进制字符串片段
```

### 日志和状态
```lua
log(message, level)      -- 记录日志，level: "info"|"warn"|"error"
-- 返回状态常量
OK                       -- 成功
NG                       -- 失败
RUNNING                  -- 运行中
```

## 使用示例

### 基础解析
```lua
-- 简单解析，直接写黑板
bb_set('data_length', tostring(data_length), 'string')
bb_set('data_hex', data_hex, 'string')

-- 解析魔数和版本
local magic = read_u8(0)
local version = read_u8(1)
bb_set_int('magic', magic)
bb_set_int('version', version)

return OK
```

### 事务模式
```lua
-- 使用事务确保数据一致性
begin_scope("packet")
bb_set_int('magic', read_u8(0))
bb_set_int('version', read_u8(1))
bb_set('payload', slice_hex(4, 8), 'string')
commit_scope()

return OK
```

### 自定义输出
```lua
-- 清空之前的输出
clear_output()

-- 设置自定义输出供其他节点使用
set_output('magic', string.format('0x%02X', read_u8(0)))
set_output('version', tostring(read_u8(1)))
set_output('payload_hex', slice_hex(4, 8))
set_output('packet_type', read_u8(2) == 1 and 'data' or 'control')

-- 同时写入黑板
bb_set_int('magic', read_u8(0))
bb_set_int('version', read_u8(1))

return OK
```

## 其他节点使用解析结果

其他节点可以通过以下方式使用解析结果：

1. **从黑板读取**：使用 bb_prefix 组织的键名
2. **从输出端口读取**：连接 output_json 端口，解析JSON获取数据
3. **直接端口连接**：连接 data_hex、data_length 等基础端口

## 最佳实践

1. **使用前缀**：设置 bb_prefix 避免键名冲突
2. **事务操作**：对于复杂解析使用 begin_scope/commit_scope
3. **自定义输出**：为下游节点提供结构化的解析结果
4. **错误处理**：在脚本中检查数据有效性，返回适当状态
5. **性能考虑**：避免在脚本中进行复杂计算，专注于数据提取

## 测试示例

### 运行测试
```bash
# 基础功能测试
./test_udp_optimized.sh

# 高级功能测试
./test_udp_advanced.sh

# 完整功能测试
./test_all_udp_features.sh
```

### 发送测试数据
```bash
# 基础数据包
echo -n -e '\x2A\x01\x00\x10Hello UDP\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00' | nc -u 127.0.0.1 6699

# 传感器数据包
echo -n -e '\x2A\x01\x01\x00\x05\x01\x00\x00\x12\x34\x56\x78\x9A\xBC\xDE\xF0\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x55' | nc -u 127.0.0.1 6699
```

## 迁移指南

### 从旧版本迁移
1. **bb_set 调用**：确保所有 bb_set 调用都有3个参数
   ```lua
   -- 旧版本
   bb_set('key', 'value')

   -- 新版本
   bb_set('key', 'value', 'string')
   ```

2. **黑板键名**：考虑使用前缀避免冲突
   ```lua
   -- 自动前缀（推荐）
   bb_set('data_length', tostring(data_length), 'string')  -- 实际键名: NodeName.data_length

   -- 自定义前缀
   begin_scope('sensor')
   bb_set('temperature', '25.5', 'string')  -- 实际键名: sensor.temperature
   commit_scope()
   ```

3. **输出端口**：更新XML配置
   ```xml
   <!-- 旧版本 -->
   <UdpDataParserNode
       parsed_text="{result_text}"
       parsed_json="{result_json}" />

   <!-- 新版本 -->
   <UdpDataParserNode
       output_json="{custom_output}"
       data_hex="{raw_hex}"
       data_length="{packet_size}" />
   ```

## 注意事项

- 所有 bb_set 调用必须提供3个参数（key, value, type）
- 黑板前缀默认使用节点名，可通过 bb_prefix 端口覆盖
- 事务模式下，只有 commit_scope() 后数据才会写入黑板
- 自定义输出数据会以JSON格式从 output_json 端口输出
- 解析辅助函数使用0基索引（read_u8(0) 读取第一个字节）
- 建议在脚本开始时调用 clear_output() 清空之前的输出
