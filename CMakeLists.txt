﻿cmake_minimum_required(VERSION 3.16)

if(POLICY CMP0167)
  cmake_policy(SET CMP0167 OLD)
endif()

# =========================
# policy
# =========================
if(POLICY CMP0048)
  cmake_policy(SET CMP0048 NEW)
endif(POLICY CMP0048)

set(CMAKE_CXX_STANDARD 17) # 设置所需的 C++ 标准 (例如 11, 14, 17, 20, 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON) # 强制要求此标准，如果编译器不支持则报错

string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
project(fixture_test VERSION 0.1.0.${PROJECT_COMPILE_DATE})
if(MSVC)
  set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}\\bin")
  set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}\\bin")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
  set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
  set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /DWIN32_LEAN_AND_MEAN")
  option(USE_MP "use multiple" ON)
  option(ProjectConfig_Global_COMPILE_FLAGS_WITH_MP "Set The Global Option COMPILE_FLAGS /MP to target." ON)
  if(ProjectConfig_Global_COMPILE_FLAGS_WITH_MP OR USE_MP)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /MP")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MP")
  endif()
  set(VS_STARTUP_PROJECT ${PROJECT_NAME})
endif(MSVC)

# =========================
# compile options
# =========================
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
  set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
endif()

set(CMAKE_BUILE_TYPE RelWithDebInfo)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# =========================
# must clean before build if you change this
# =========================
option(BUILD_FIXTURE_TEST_UNIT_TEST "build gtest or not" ON)
option(COPY_TO_RELEASE_ENABLE "copy need to pack files/director or not" ON)
option(IS_DEBUG_MODE "Is compile use debug mode : " ON)

message(STATUS "-----------------------------------------------------------")
message(STATUS "build gtest or not: " ${BUILD_FIXTURE_TEST_UNIT_TEST})
message(STATUS "copy need to pack files/director: " ${COPY_TO_RELEASE_ENABLE})
message(STATUS "-----------------------------------------------------------")

# =========================
# get system name
# =========================
# if(CMAKE_SYSTEM_NAME MATCHES "Windows")
#   set(LSB_CODENAME msvc2017_64)
# elseif(CMAKE_SYSTEM_NAME MATCHES "Linux")
#   find_program(LSB_EXEC lsb_release)

#   if(LSB_EXEC MATCHES "NOTFOUND")
#     message("\n lsb_release not found, please install using: \n\t sudo apt install lsb_release\n")
#   endif()

#   execute_process(
#     COMMAND ${LSB_EXEC} -cs
#     OUTPUT_VARIABLE LSB_CODENAME
#     OUTPUT_STRIP_TRAILING_WHITESPACE)
# else()
#   message(FATAL_ERROR "unsupported system")
# endif()

add_definitions(-DRSFSCLOG_USE_QT -DBOOST_SYSTEM_USE_UTF8)

include_directories(include)

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
  add_compile_options(-O0 -g -ggdb)
endif()

# =========================
# set variable
# =========================
set(PROJECT_UNIQUE_CODE 000)
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME} ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}/lib)
set(FIXTURE_TEST_CONFIG_DIR ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}/config)

# =========================
# git && config.h
# =========================
string(TIMESTAMP PROJECT_COMPILE_TIME "%Y%m%d_%H%M%S")
find_package(Git QUIET)
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
configure_file("${PROJECT_SOURCE_DIR}/src/config.h.in" "${PROJECT_SOURCE_DIR}/src/config.h")
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

if(WIN32)
  if(CMAKE_SIZEOF_VOID_P EQUAL 8) # 64-bit
    set(Boost_ARCHITECTURE "-x64")
  elseif(CMAKE_SIZEOF_VOID_P EQUAL 4) # 32-bit
    set(Boost_ARCHITECTURE "-x32")
  endif()

  set(Boost_USE_STATIC_LIBS ON)
  set(Boost_USE_MULTITHREADED ON)
  set(Boost_USE_STATIC_RUNTIME OFF)
endif(WIN32)

find_package(Boost COMPONENTS)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# =========================
# qt
# =========================
find_package(
  Qt5
  COMPONENTS Core Gui Widgets Network Xml Svg
  REQUIRED)

qt5_add_resources(QT_RESOURCE ${CMAKE_CURRENT_SOURCE_DIR}/resource/resource.qrc)

# =========================
# sub module
# =========================

add_subdirectory(lib/communication)
add_subdirectory(lib/nodeeditor)
add_subdirectory(lib/behaviortree)

# 添加 QCodeEditor
add_subdirectory(${CMAKE_SOURCE_DIR}/include/3rd_party/q_code_editor)

add_subdirectory(src/common)
add_subdirectory(src/flow_editor)
add_subdirectory(${CMAKE_SOURCE_DIR}/include/3rd_party/sol2)
add_subdirectory(${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model)

file(GLOB_RECURSE MOC_UI_HEAD "src/ui/*.h")
# 明确列出所有源文件，避免使用GLOB_RECURSE可能导致的问题
set(CPP_SOURCE src/main.cpp src/ui/main_window.cpp src/ui/one_to_x_ui.cpp src/ui/message_browser.cpp
               src/ui/widget_about.cpp)

# 确保所有UI源文件都被包含
message(STATUS "UI源文件列表:")
foreach(SOURCE_FILE ${CPP_SOURCE})
  message(STATUS "  ${SOURCE_FILE}")
endforeach()

add_library(TopInclude INTERFACE)
target_include_directories(TopInclude INTERFACE $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>)

add_executable(${PROJECT_NAME})

# =========================
# target
# =========================
target_include_directories(${PROJECT_NAME} SYSTEM PRIVATE lib lib/uds_on_doip_release)
target_include_directories(${PROJECT_NAME} PRIVATE include src ui)
target_sources(${PROJECT_NAME} PRIVATE ${MOC_UI_HEAD} ${QT_RESOURCE} ${CPP_SOURCE})
target_compile_definitions(${PROJECT_NAME} PUBLIC QT_NO_KEYWORDS QT_NO_DEBUG_OUTPUT)
target_compile_definitions(${PROJECT_NAME} PRIVATE IS_DEBUG_MODE=$<IF:$<BOOL:${IS_DEBUG_MODE}>,1,0>)
target_link_libraries(
  ${PROJECT_NAME}
  PRIVATE Qt5::Core
          Qt5::Gui
          Qt5::Widgets
          Qt5::Network
          Qt5::Xml
          Qt5::Svg
          common::common
          flow_editor
          QCodeEditor::QCodeEditor  # 暂时注释掉
          TopInclude)
target_link_libraries(${PROJECT_NAME} PUBLIC BT::behaviortree_cpp)

# =========================
# plugins
# =========================
add_subdirectory(src/plugins)

# 添加测试目录
if(BUILD_FIXTURE_TEST_UNIT_TEST)
  # add_subdirectory(test)
endif()

# # =========================
# # UDP数据解析节点测试程序
# # =========================
# add_executable(test_udp_node test_udp_node.cpp)

# target_link_libraries(test_udp_node
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
# )

# target_include_directories(test_udp_node PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
# )

# # =========================
# # UDP数据解析节点测试程序（修复版本）
# # =========================
# add_executable(test_udp_fixed test_udp_fixed.cpp)

# target_link_libraries(test_udp_fixed
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
# )

# target_include_directories(test_udp_fixed PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
# )

# # =========================
# # UDP数据解析示例程序
# # =========================
# add_executable(udp_data_parser_example examples/udp_data_parser_example.cpp)

# target_link_libraries(udp_data_parser_example
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
# )

# target_include_directories(udp_data_parser_example PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
# )

# # =========================
# # JSON查看器示例程序
# # =========================

# # 首先添加QJsonModel库
# add_subdirectory(${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model)

# add_executable(json_viewer_example examples/json_viewer_example.cpp)

# target_link_libraries(json_viewer_example
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(json_viewer_example PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# # =========================
# # 变量绑定测试程序
# # =========================
# add_executable(test_variable_binding_simple test_variable_binding_simple.cpp)
# add_executable(test_direct_variable_binding test_direct_variable_binding.cpp)
# add_executable(test_thread_safe_json_viewer test_thread_safe_json_viewer.cpp)
# add_executable(test_popup_window test_popup_window.cpp)
# add_executable(test_window_visibility test_window_visibility.cpp)

# target_link_libraries(test_variable_binding_simple
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(test_variable_binding_simple PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# target_link_libraries(test_direct_variable_binding
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(test_direct_variable_binding PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# target_link_libraries(test_thread_safe_json_viewer
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(test_thread_safe_json_viewer PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# target_link_libraries(test_popup_window
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(test_popup_window PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# target_link_libraries(test_window_visibility
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(test_window_visibility PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# # =========================
# # UDP三节点架构测试程序
# # =========================
# add_executable(test_udp_three_nodes test_udp_three_nodes.cpp)

# target_link_libraries(test_udp_three_nodes
#     common::common
#     BT::behaviortree_cpp
#     mems_communication
#     sol2::sol2
#     Qt5::QJsonModel
#     Qt5::Core
#     Qt5::Gui
#     Qt5::Widgets
# )

# target_include_directories(test_udp_three_nodes PRIVATE
#     ${CMAKE_SOURCE_DIR}
#     ${CMAKE_SOURCE_DIR}/src
#     ${CMAKE_SOURCE_DIR}/lib/behaviortree/include
#     ${CMAKE_SOURCE_DIR}/lib/communication/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/sol2/include
#     ${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include
# )

# # 复制测试文件到构建目录
# configure_file(${CMAKE_SOURCE_DIR}/test_udp_minimal.xml ${CMAKE_BINARY_DIR}/test_udp_minimal.xml COPYONLY)
# configure_file(${CMAKE_SOURCE_DIR}/test_udp_fixed.xml ${CMAKE_BINARY_DIR}/test_udp_fixed.xml COPYONLY)
# configure_file(${CMAKE_SOURCE_DIR}/examples/udp_parser_example.xml ${CMAKE_BINARY_DIR}/udp_parser_example.xml COPYONLY)
# configure_file(${CMAKE_SOURCE_DIR}/examples/json_viewer_example.xml ${CMAKE_BINARY_DIR}/json_viewer_example.xml COPYONLY)
# configure_file(${CMAKE_SOURCE_DIR}/examples/udp_three_nodes_example.xml ${CMAKE_BINARY_DIR}/udp_three_nodes_example.xml COPYONLY)
# configure_file(${CMAKE_SOURCE_DIR}/test_json_viewer_variable_binding.xml ${CMAKE_BINARY_DIR}/test_json_viewer_variable_binding.xml COPYONLY)
