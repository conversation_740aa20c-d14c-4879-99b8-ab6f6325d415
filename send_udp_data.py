#!/usr/bin/env python3
import socket
import time
import sys

def send_udp_data():
    # 创建UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    # 目标地址和端口
    target_host = 'localhost'
    target_port = 6699
    
    # 要发送的数据（32字节）
    data = bytes([0x12, 0x34, 0x56, 0x78, 0x01, 0x02, 0x03, 0x04, 
                  0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C,
                  0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
                  0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C])
    
    print(f"📡 准备发送UDP数据到 {target_host}:{target_port}")
    print(f"📏 数据长度: {len(data)} 字节")
    print(f"🔢 数据内容: {data.hex()}")
    
    try:
        # 发送数据
        bytes_sent = sock.sendto(data, (target_host, target_port))
        print(f"✅ 成功发送 {bytes_sent} 字节")
        
        # 等待一下确保数据发送完成
        time.sleep(0.1)
        
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False
    finally:
        sock.close()
    
    return True

if __name__ == "__main__":
    print("=== Python UDP数据发送器 ===")
    
    # 如果提供了参数，发送多次
    count = 1
    if len(sys.argv) > 1:
        try:
            count = int(sys.argv[1])
        except ValueError:
            count = 1
    
    for i in range(count):
        print(f"\n--- 发送第 {i+1}/{count} 次 ---")
        if send_udp_data():
            print(f"✅ 第 {i+1} 次发送成功")
        else:
            print(f"❌ 第 {i+1} 次发送失败")
        
        if i < count - 1:
            time.sleep(1)
    
    print("\n🎉 发送完成")
