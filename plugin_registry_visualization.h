#pragma once

#include <QMap>
#include <QPluginLoader>
#include <QString>
#include <QVariant>
#include <QWidget>
#include <memory>

namespace robosense::visualization
{

// 可视化插件接口
class IVisualizationPlugin
{
public:
  virtual ~IVisualizationPlugin() = default;

  // 插件信息
  virtual QString getPluginName() const             = 0;
  virtual QString getVersion() const                = 0;
  virtual QStringList getSupportedDataTypes() const = 0;

  // 创建可视化组件
  virtual QWidget* createVisualizationWidget(const QString& _data_type) = 0;

  // 更新数据
  virtual void updateWidget(QWidget* _widget, const QVariant& _data) = 0;

  // 配置组件
  virtual void configureWidget(QWidget* _widget, const QVariantMap& _config) = 0;
};

// 可视化插件注册表
class VisualizationPluginRegistry
{
public:
  static VisualizationPluginRegistry& instance();

  // 注册插件
  void registerPlugin(std::shared_ptr<IVisualizationPlugin> plugin);

  // 从文件加载插件
  bool loadPlugin(const QString& plugin_path);

  // 获取支持指定数据类型的插件
  QList<std::shared_ptr<IVisualizationPlugin>> getPluginsForDataType(const QString& data_type);

  // 创建可视化组件
  QWidget* createVisualizationWidget(const QString& data_type, const QString& preferred_plugin = "");

private:
  QList<std::shared_ptr<IVisualizationPlugin>> plugins_;
  QMap<QString, QList<QPluginLoader*>> loaded_plugins_;
};

// 可视化显示管理器
class VisualizationDisplayManager : public QObject
{
  Q_OBJECT

public:
  static VisualizationDisplayManager& instance();

  // 显示可视化
  void showVisualization(const QString& node_id,
                         const QString& data_type,
                         const QVariant& data,
                         const QVariantMap& config = {});

  // 更新可视化
  void updateVisualization(const QString& node_id, const QVariant& data);

  // 隐藏可视化
  void hideVisualization(const QString& node_id);

  // 配置可视化
  void configureVisualization(const QString& node_id, const QVariantMap& config);

signals:
  void visualizationCreated(const QString& node_id, QWidget* widget);
  void visualizationDestroyed(const QString& node_id);

private:
  struct VisualizationInfo
  {
    QWidget* widget;
    std::shared_ptr<IVisualizationPlugin> plugin;
    QString data_type;
    QVariantMap config;
  };

  QMap<QString, VisualizationInfo> visualizations_;
};

// 具体插件实现示例：数值显示插件
class NumericVisualizationPlugin : public IVisualizationPlugin
{
public:
  QString getPluginName() const override { return "NumericVisualization"; }
  QString getVersion() const override { return "1.0.0"; }
  QStringList getSupportedDataTypes() const override { return { "numeric", "voltage", "current" }; }

  QWidget* createVisualizationWidget(const QString& data_type) override;
  void updateWidget(QWidget* widget, const QVariant& data) override;
  void configureWidget(QWidget* widget, const QVariantMap& config) override;
};

// 图像显示插件
class ImageVisualizationPlugin : public IVisualizationPlugin
{
public:
  QString getPluginName() const override { return "ImageVisualization"; }
  QString getVersion() const override { return "1.0.0"; }
  QStringList getSupportedDataTypes() const override { return { "image", "grayscale", "rgb" }; }

  QWidget* createVisualizationWidget(const QString& data_type) override;
  void updateWidget(QWidget* widget, const QVariant& data) override;
  void configureWidget(QWidget* widget, const QVariantMap& config) override;
};

// 便利宏定义
#define REGISTER_VISUALIZATION_PLUGIN(PluginClass)                                           \
  static bool registered_##PluginClass = []() {                                              \
    VisualizationPluginRegistry::instance().registerPlugin(std::make_shared<PluginClass>()); \
    return true;                                                                             \
  }();

}  // namespace robosense::visualization

Q_DECLARE_INTERFACE(robosense::visualization::IVisualizationPlugin, "robosense.visualization.IVisualizationPlugin/1.0")

/*
插件注册表模式的优势：

1. 真正的插件化：
   - 支持动态加载插件
   - 插件可以独立开发和部署
   - 支持第三方插件

2. 灵活的数据类型支持：
   - 一个插件可以支持多种数据类型
   - 多个插件可以支持同一种数据类型
   - 自动选择最合适的插件

3. 版本管理：
   - 插件版本控制
   - 兼容性检查

4. 配置灵活：
   - 支持复杂的插件配置
   - 运行时配置更新

使用示例：
```cpp
// 注册插件（在插件初始化时）
REGISTER_VISUALIZATION_PLUGIN(NumericVisualizationPlugin);

// 在节点中使用
VisualizationDisplayManager::instance().showVisualization(
    name(), "voltage", voltage_data, 
    {{"style", "gauge"}, {"range", QVariantList{0, 10}}});
```
*/
