﻿# =========================
# install && cpack
# =========================
option(COPY_TO_RELEASE_ENABLE "copy need to pack files/director or not" ON)
message(STATUS "copy need to pack files/director: " ${COPY_TO_RELEASE_ENABLE})

install(
  TARGETS ${PROJECT_NAME}
  PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ
  ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME})

if(CMAKE_SYSTEM_NAME MATCHES "Linux")
  # =========================
  # install
  # =========================
  install(FILES ${ROTATOR_LIB} DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME})
  install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/script/${PROJECT_NAME}.desktop DESTINATION /usr/share/applications)
  install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/resource/img/icon.png DESTINATION /usr/local/share/${PROJECT_NAME}/img)
  install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/config/
    DESTINATION ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}/config
    USE_SOURCE_PERMISSIONS
    DIRECTORY_PERMISSIONS
      OWNER_EXECUTE
      OWNER_WRITE
      OWNER_READ
      GROUP_EXECUTE
      GROUP_READ
      WORLD_EXECUTE
      WORLD_READ)

  # =========================
  # need to copy files
  # =========================
  set(TEMP_RELEASE ${CMAKE_CURRENT_SOURCE_DIR}/release)
  set(UBUNTU_DEB ${TEMP_RELEASE}/ubuntu_2004)
  set(CHANGELOG_FILE ${CMAKE_CURRENT_SOURCE_DIR}/CHANGELOG.md)

  # delete old deb TODO check if it is run by cpack.
  file(
    GLOB_RECURSE deb_files
    LIST_DIRECTORIES false
    RELATIVE ${TEMP_RELEASE}
    ${PROJECT_NAME}*.deb)

  foreach(deb_file IN LISTS deb_files)
    if(EXISTS "${TEMP_RELEASE}/${deb_file}")
      message(STATUS "remove deb_file: " ${TEMP_RELEASE}/${deb_file})
      add_custom_command(
        TARGET ${PROJECT_NAME}
        POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E remove ${TEMP_RELEASE}/${deb_file})
    endif()
  endforeach()

  # =========================
  # CPack
  # =========================
  # set(CMAKE_INSTALL_PREFIX "/usr")
  set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
  set(CPACK_PACKAGE_NAME ${PROJECT_NAME})
  set(CPACK_GENERATOR "DEB")
  set(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "amd64")
  set(CPACK_DEBIAN_PACKAGE_MAINTAINER "Vance Huang")
  # set(CPACK_PACKAGING_INSTALL_PREFIX ${CMAKE_INSTALL_PREFIX})
  set(CPACK_PACKAGE_ICON resource/img/icon.png)
  set(CPACK_OUTPUT_FILE_PREFIX "${UBUNTU_DEB}")
  set(CPACK_PACKAGE_FILE_NAME "${CPACK_PACKAGE_NAME}_v${CPACK_PACKAGE_VERSION}_${CMAKE_SYSTEM_PROCESSOR}")
  include(CPack)
elseif(CMAKE_SYSTEM_NAME MATCHES "Windows")
  list(APPEND RSFSC_LIBS ${CMAKE_SOURCE_DIR}/lib/rsfsc_lib/${LSB_CODENAME}/rsfsc_lib.dll)
  list(APPEND RSFSC_LIBS ${CMAKE_SOURCE_DIR}/lib/rsfsc_lib/${LSB_CODENAME}/rsfsc_lib.pdb)
  list(APPEND RSFSC_LIBS ${CMAKE_SOURCE_DIR}/lib/rsfsc_lib/${LSB_CODENAME}/bin/rsfsc_log.dll)
  add_custom_command(
    TARGET ${PROJECT_NAME}
    POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${RSFSC_LIBS} $<TARGET_FILE_DIR:${PROJECT_NAME}>
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_SOURCE_DIR}/config/ $<TARGET_FILE_DIR:${PROJECT_NAME}>/config)
  deployqt(${PROJECT_NAME})

else()
  message(STATUS "Windows do not need to install")
endif()

# =========================
# run copy file && cpack
# =========================
if(COPY_TO_RELEASE_ENABLE)
  if(CMAKE_SYSTEM_NAME MATCHES "Linux")
    execute_process(COMMAND sh ${CMAKE_CURRENT_SOURCE_DIR}/script/install/pre_build.sh)
    add_custom_command(
      TARGET ${PROJECT_NAME}
      POST_BUILD
      # COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_CURRENT_SOURCE_DIR}/doc/验证报告/ ${TEMP_RELEASE}/doc/验证报告
      # WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
      COMMAND sh ${CMAKE_CURRENT_SOURCE_DIR}/script/install/post_build.sh)
  endif()
endif(COPY_TO_RELEASE_ENABLE)
