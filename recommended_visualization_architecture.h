#pragma once

#include <QWidget>
#include <QVariant>
#include <QString>
#include <QMap>
#include <memory>
#include <functional>

namespace robosense::visualization {

// 1. 数据接口层
class IVisualizationData
{
public:
    virtual ~IVisualizationData() = default;
    virtual QString getDataType() const = 0;
    virtual QVariant getData() const = 0;
    virtual QVariantMap getMetadata() const = 0;
    virtual QString getNodeId() const = 0;
};

// 2. 可视化组件接口
class IVisualizationWidget
{
public:
    virtual ~IVisualizationWidget() = default;
    virtual QWidget* getWidget() = 0;
    virtual void updateData(const IVisualizationData& data) = 0;
    virtual void configure(const QVariantMap& config) = 0;
    virtual QString getWidgetType() const = 0;
    virtual QStringList getSupportedDataTypes() const = 0;
};

// 3. 可视化组件工厂
using VisualizationWidgetFactory = std::function<std::unique_ptr<IVisualizationWidget>()>;

// 4. 统一可视化管理器（类似DisplaySwitcherWidget的设计）
class UnifiedVisualizationManager : public QWidget
{
    Q_OBJECT

public:
    static UnifiedVisualizationManager& instance();

    // 注册可视化组件工厂
    void registerVisualizationWidget(const QString& widget_type,
                                   const QStringList& supported_data_types,
                                   VisualizationWidgetFactory factory);

    // 显示可视化（自动选择合适的组件）
    void showVisualization(std::shared_ptr<IVisualizationData> data,
                          const QVariantMap& display_config = {});

    // 更新可视化
    void updateVisualization(const QString& node_id, 
                           std::shared_ptr<IVisualizationData> data);

    // 隐藏可视化
    void hideVisualization(const QString& node_id);

    // 获取当前活跃的可视化
    QStringList getActiveVisualizations() const;

    // 设置显示区域（集成到主UI中）
    void setDisplayArea(QWidget* display_area);

signals:
    void visualizationShown(const QString& node_id, const QString& widget_type);
    void visualizationHidden(const QString& node_id);
    void visualizationUpdated(const QString& node_id);

private slots:
    void onVisualizationTimeout(const QString& node_id);

private:
    struct VisualizationInfo {
        std::unique_ptr<IVisualizationWidget> widget;
        std::shared_ptr<IVisualizationData> data;
        QTimer* auto_close_timer;
        QVariantMap config;
        QString widget_type;
    };

    // 选择最合适的可视化组件
    QString selectBestWidget(const QString& data_type, const QVariantMap& config);

    QMap<QString, QMap<QStringList, VisualizationWidgetFactory>> widget_factories_;  // widget_type -> supported_types -> factory
    QMap<QString, VisualizationInfo> active_visualizations_;  // node_id -> info
    QWidget* display_area_;
};

// 5. 具体数据类型实现
class NumericVisualizationData : public IVisualizationData
{
public:
    NumericVisualizationData(const QString& node_id, const QVariantMap& values)
        : node_id_(node_id), values_(values) {}

    QString getDataType() const override { return "numeric"; }
    QVariant getData() const override { return values_; }
    QVariantMap getMetadata() const override { return metadata_; }
    QString getNodeId() const override { return node_id_; }

    void setMetadata(const QVariantMap& metadata) { metadata_ = metadata; }

private:
    QString node_id_;
    QVariantMap values_;
    QVariantMap metadata_;
};

class ImageVisualizationData : public IVisualizationData
{
public:
    ImageVisualizationData(const QString& node_id, const QByteArray& image_data, 
                          int width, int height, const QString& format = "grayscale")
        : node_id_(node_id), image_data_(image_data), width_(width), height_(height), format_(format) {}

    QString getDataType() const override { return "image"; }
    QVariant getData() const override {
        QVariantMap data;
        data["image_data"] = image_data_;
        data["width"] = width_;
        data["height"] = height_;
        data["format"] = format_;
        return data;
    }
    QVariantMap getMetadata() const override { return metadata_; }
    QString getNodeId() const override { return node_id_; }

private:
    QString node_id_;
    QByteArray image_data_;
    int width_, height_;
    QString format_;
    QVariantMap metadata_;
};

// 6. 节点可视化助手（简化节点使用）
class NodeVisualizationHelper
{
public:
    NodeVisualizationHelper(const QString& node_id) : node_id_(node_id) {}

    // 显示数值数据
    void showNumericData(const QVariantMap& values, const QVariantMap& config = {}) {
        auto data = std::make_shared<NumericVisualizationData>(node_id_, values);
        UnifiedVisualizationManager::instance().showVisualization(data, config);
    }

    // 显示图像数据
    void showImageData(const QByteArray& image_data, int width, int height, 
                      const QString& format = "grayscale", const QVariantMap& config = {}) {
        auto data = std::make_shared<ImageVisualizationData>(node_id_, image_data, width, height, format);
        UnifiedVisualizationManager::instance().showVisualization(data, config);
    }

    // 隐藏可视化
    void hideVisualization() {
        UnifiedVisualizationManager::instance().hideVisualization(node_id_);
    }

private:
    QString node_id_;
};

// 7. 可视化组件注册宏
#define REGISTER_VISUALIZATION_WIDGET(widget_type, data_types, factory_func) \
    static bool registered_##widget_type = []() { \
        UnifiedVisualizationManager::instance().registerVisualizationWidget( \
            #widget_type, data_types, factory_func); \
        return true; \
    }();

} // namespace robosense::visualization

/*
推荐架构的优势：

1. 统一管理：
   - 类似DisplaySwitcherWidget的设计，集成到主UI中
   - 统一的可视化区域，避免多窗口混乱
   - 自动选择最合适的可视化组件

2. 插件化扩展：
   - 简单的注册机制，插件开发者容易使用
   - 支持多种数据类型和可视化方式
   - 运行时动态注册

3. 节点友好：
   - NodeVisualizationHelper提供简单易用的接口
   - 节点只需要关注数据，不关心UI实现
   - 类型安全的数据传递

4. 配置灵活：
   - 支持自动关闭、窗口大小、显示样式等配置
   - 支持运行时配置更新

使用示例：

// 在插件初始化时注册可视化组件
REGISTER_VISUALIZATION_WIDGET(GaugeWidget, 
    QStringList{"numeric", "voltage", "current"}, 
    []() { return std::make_unique<GaugeVisualizationWidget>(); });

// 在UDP数据解析节点中
NodeVisualizationHelper viz_helper(name());

// 显示电压数据
QVariantMap voltage_data;
voltage_data["voltage1"] = 3.3;
voltage_data["voltage2"] = 5.0;
viz_helper.showNumericData(voltage_data, {
    {"widget_type", "gauge"},
    {"title", "电压监控"},
    {"auto_close_duration", 5000}
});

// 显示灰度图
QByteArray gray_image = parseUdpDataToGrayscale(udp_data);
viz_helper.showImageData(gray_image, 640, 480, "grayscale", {
    {"title", "灰度图像"},
    {"zoom_enabled", true}
});
*/
