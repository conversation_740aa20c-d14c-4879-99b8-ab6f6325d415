﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   mems_tcp.h
 * <AUTHOR> Chen (<EMAIL>), Sloan Xi (<EMAIL>), Vance Huang (<EMAIL>)
 * @brief     you can use MEMSTCP to communicate with MEMS through TCP which defined by RoboSense
 * @version 1.2.0
 * @date 2023-05-10
 * 
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *  
 * You can not use, copy or spread without official authorization.
 * 
 * If you find any BUG or improvement in MEMSTCP, please contact the authors, so we can share your idea  
 * 
 */
#ifndef MEMS_TCP_H
#define MEMS_TCP_H

#include "mems_communication_common.h"
#include <array>
#include <atomic>
#include <boost/asio.hpp>
#include <cstdint>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

// namespace boost
// {
// namespace asio
// {
// template <typename Protocol>
// class stream_socket_service;

// template <typename Protocol, typename StreamSocketService = stream_socket_service<Protocol> >
// class basic_stream_socket;

// namespace ip
// {
// class tcp;
// typedef basic_stream_socket<tcp> socket;
// }  // namespace ip
// // class deadline_timer;
// class io_service;
// }  // namespace asio
// }  // namespace boost

namespace robosense
{
namespace lidar
{

constexpr std::size_t VIEWING_FIELD_PARAM_SIZE         = 20;
constexpr std::size_t VIEWING_FIELD_PARAM_MIRRORK_SIZE = 5;
constexpr int MAX_REGISTER_SIZE_PER_TIME               = 60;  /// suggest 60reg once
constexpr uint32_t MAX_REG_ADDR                        = 0x83D00000;
constexpr uint32_t MIN_REG_ADDR                        = 0x83C00000;

class MEMSTCP
{
public:
  explicit MEMSTCP();
  explicit MEMSTCP(MEMSTCP&&)        = delete;
  explicit MEMSTCP(const MEMSTCP&)   = delete;
  MEMSTCP& operator=(MEMSTCP&&)      = delete;
  MEMSTCP& operator=(const MEMSTCP&) = delete;
  ~MEMSTCP();

  /**
   * @brief connect the lidar
   * 
   * @param _ip ip address
   * @param _msop_port port
   * @param _timeout_ms timeout value, default is 5000ms
   * @return true 
   * @return false 
   */
  bool connect(const std::string& _ip, const uint16_t _msop_port, const uint32_t _timeout_ms = 5000);

  /**
   * @brief you can use this version with WidgetLidarInfo
   * 
   * @param _network_info the network info get from WidgetLidarInfo
   * @param _timeout_ms timeout value, default is 5000ms
   * @return true 
   * @return false 
   */
  bool connect(const std::pair<std::string, uint16_t>& _network_info, const uint32_t _timeout_ms = 5000);

  /**
   * @brief     you should call this after all
   *
   * @retval    true
   * @retval    false
   */
  bool disconnect();

  /**
   * @brief     return whether mems tcp is connected
   *
   * @retval    true
   * @retval    false
   */
  bool isConnected() const;

  void getLidarInfo(LidarHardwareSerial& _hardware_serial,
                    LidarSoftwareVersion& _software_version,
                    LidarFunctionSafeVersion& _func_safe_version);

  LaserModuleVersion getLaserModuleInfo();

  DigitalBoardVersion getDigitalBoardInfo();

  /**
   * @brief     call this function in async to abort read or write or collect while do time-consuming operation
   *            NOTE: when abort is called, you must call reset befor other test function
   */
  void abort();

  /**
   * @brief     check if abort is called or not
   *
   * @retval    return true if the abort flag is true, otherwise return false
   */
  bool isAbort();

  /**
   * @brief     reset the abort flag
   *            NOTE: this function must be called to reset abort flag, otherwise test function will return error
   */
  bool reset(const uint32_t _msec_10 = 400);
  void resetAbortFlag();

  /**
   * @brief     write vector of address and value to mems
   * 
   * @param     _reg_addr           vector of address to write
   * @param     _reg_val            vector of value corresponding to reg_addr
   * @param     _msec_10            wait n 10 milliseconds for reply from lidar
   * @retval    true                write successfully
   * @retval    false               some error happen
  **/
  bool writeRegData(const std::vector<uint32_t>& _reg_addr,
                    const std::vector<int32_t>& _reg_val,
                    const uint32_t _msec_10                = 400,
                    const std::string& _download_data_path = "");
  /**
   * @brief     write vector of value start from address start_reg_addr
   * 
   * @param     _start_reg_addr     start from register address
   * @param     _reg_val            vector of register value to write
   * @param     _msec_10            wait n 10 seconds for reply from lidar
   * @retval    true                write successfully
   * @retval    false               some error happen
  **/
  // clang-format off
  [[deprecated("We recommend use another interface, because lidar exist bug")]]
  bool writeRegData(const uint32_t _start_reg_addr, std::vector<int32_t>& _reg_val, const uint32_t _msec_10 = 400);
  // clang-format on
  /**
    * @brief    write register address and value, you can get this type from CSVParser
    * 
    * @param    _reg_addr_and_value  get this from CSVParser
    * @param    _msec_10             wait n 10 milliseconds for reply from lidar
    * @param    _download_data_path  path where to write download data file
    * @retval   true                 write successfully
    * @retval   false                some error happen
    */
  bool writeRegData(const std::pair<std::vector<uint32_t>, std::vector<int32_t>>& _reg_addr_and_value,
                    const uint32_t _msec_10                = 400,
                    const std::string& _download_data_path = "");

  /**
   * @brief     read register data of address in a vector
   * 
   * @param     _addrs2read         vector of address to read 
   * @param     _reg_val            vector of value corresponding to reg_addr
   * @param     _msec_10            wait n 10 milliseconds for reply from lidar
   * @retval    true                read successfully
   * @retval    false               some error happen
  **/
  bool readRegData(const std::vector<uint32_t>& _addrs2read,
                   std::vector<int32_t>& _reg_val,
                   const uint32_t _msec_10 = 400);
  /**
   * @brief     read vector of value start from address start_reg_addr
   * 
   * @param     _start_reg_addr     start from register address
   * @param     _reg_number         total number register to read
   * @param     _reg_val            vector of register value to read
   * @param     _msec_10            wait n 10 milliseconds for reply from lidar
   * @retval    true                read successfully
   * @retval    false               some error happen
  **/
  bool readRegData(const uint32_t _start_reg_addr,
                   const uint32_t _reg_number,
                   std::vector<int32_t>& _reg_val,
                   const uint32_t _msec_10 = 400);

  /**
   * @brief     use to get intensity data, same as readRegData
  **/
  bool getIntensityData(const std::vector<uint32_t>& _addrs2read,
                        std::vector<int32_t>& _calib_data,
                        const uint32_t _msec_10 = 400);

  /**
   * @brief     read viewing field params
   * 
   * @param     _param             viewing field params in lidar
   * @retval    true               get param successfully
   * @retval    false              get param failed
  **/
  bool readViewingFieldParams(std::vector<double>& _param);

  /**
   * @brief     write viewing field params in csv to lidar, it read params in csv and call writeViewingFieldParams(const std::vector<double>& param)
   * 
   * @param     _csv_path           csv file path
   * @param     _mirrork_csv_path   mirrork csv file path
   * @retval    true                write successfully
   * @retval    false               write failed
  **/
  bool writeViewingFieldParams(const std::string& _csv_path, const std::string& _mirrork_csv_path = "");

  /**
   * @brief     write viewing field params to lidar
   * 
   * @param     _param              params to write
   * @retval    true                write successfully
   * @retval    false               write failed
  **/
  bool writeViewingFieldParams(const std::vector<double>& _param);

  /**
   * @brief     set lidar's network info // NOTE remember to getNetworkInfo before, and only change info you want to change
   * 
   * @param     _network_info       lidar info to set
   * @param     _msec_10            wait n 10 seconds for reply from lidar
   * @retval    true                set successfully
   * @retval    false               some error happen
  **/
  bool setNetworkInfo(const NetworkInfo& _network_info, const uint32_t _msec_10 = 400);

  /**
   * @brief     get the network info
   * 
   * @param     _network_info              
  **/
  void getNetworkInfo(NetworkInfo& _network_info) const;

  /**
   * @brief     when write to MEMS, you just write to FPGA, register will reset after power off, if you want lidar keep 
   *            register value after restart, you must fixRegister to write data to flash
   * @param     _msec_10            wait n 10 seconds for reply from lidar
   * @retval    true                set successfully
   * @retval    false               some error happen
  **/
  bool fixRegister(const uint32_t _msec_10 = 400);

  /**
   * @brief reset electronic control unit (ECU), register will get value from RAM
   *
   * @param _msec_10                wait n 10 milliseconds for reply from lidar
   * @retval true                  reset ECU successfully
   * @retval false                 some error happen
   * @note 不能固化的寄存器在软重启后不会恢复到默认值 只有物理断电重启才会恢复到默认值 [2022/07/26 ZYL]
   */
  bool softResetLidar(const uint32_t _msec_10 = 400);

  /**
   * @brief update firmware
   *
   * @param _firmware_path the path of firmware
   * @param _addr_offset   address offset decide whether update (0x0) or backup (0xA00000) firmware
   * @param _msec_10       wait n 10 milliseconds for reply from lidar
   * @retval              true update firmware successfully
   * @retval              false some error happen
   */
  bool writeToFlash(const std::string& _firmware_path, const uint32_t _addr_offset, const uint32_t _msec_10 = 12000);

  /**
   * @brief write to flash
   *
   * @param _data          what do you want to write
   * @param _addr_offset   address offset decide whether update (0x0) or backup (0xA00000) firmware ohter for nvm clear
   * @param _msec_10       wait n 10 milliseconds for reply from lidar
   * @retval              true update firmware successfully
   * @retval              false some error happen
   */
  bool writeToFlash(const std::vector<char>& _data, const uint32_t _addr_offset, const uint32_t _msec_10 = 12000);

  /**
   * @brief erase all registers 
   * 
   * @param[in] _msec_10 wait n 10 milliseconds for reply from lidar
   * \retval true erase register successfully
   * \retval false some error happen
   */
  bool eraseRegister(const uint32_t _msec_10 = 400);

  /**
   * @brief check if register's value from lidar is same as that of download_data.csv 
   * 
   * @param _path the path of download_data_file saved
   * @retval true same as download_data.csv
   * @retval false not same as download_data.csv
   */
  bool checkDownloadData(const std::string& _path);

  /**
   * @brief write download data to lidar
   * 
   * @param _path the path of download_data_file saved
   * @retval true read download data file successfully
   * @retval false  some error happen
   */
  bool writeDownloadData(const std::string& _path);

  // Q_SIGNALS:  // TODO
  //   /**
  //    * @brief     MEMS disconnect unexpected
  //    *
  //   **/
  //   void signalTCPDisconnected();

  static bool ping(const std::string& _ip);

private:
  enum ReadWriteStateIndex
  {
    STATE_TCP_START = 0,
    STATE_TCP_END,
    STATE_READ_REG,
    STATE_WRITE_REG,
    STATE_FIX_REG,
    STATE_READ_NETWORK_INFO,
    STATE_WRITE_NETWORK_INFO,
    STATE_READ_INTENSITY,
    STATE_READ_VIEWING_FIELD_PARAM,
    STATE_WRITE_SPLICE_PARAMS,
    STATE_READ_REG_2,
    STATE_WRITE_REG_2,
    STATE_WRITE_FLASH,
    STATE_RESET,
    STATE_ERASE_REGISTER,
    STATE_TOTAL_NUM  //　must be the last element of this enum
  };

  void slotReadTCPData();
  bool writeCMD(const uint32_t _cmd, const ReadWriteStateIndex _index, uint32_t _msec_10);
  bool sendData(const std::vector<char>& _send_data, const ReadWriteStateIndex _index, uint32_t _msec_10);
  bool readNetworkInfo();
  void checkDeadline();

  /**
   * @brief set the async operation timeout in socket
   * 
   * @param _timeout_count_ms the timeout value, unit is ms
   */
  void setAsyncTimeout(const std::size_t _timeout_count_ms);

  bool writeRegDataWithLimit(const std::vector<uint32_t>& _reg_addr,
                             const std::vector<int32_t>& _reg_val,
                             const uint32_t _msec_10                = 400,
                             const std::string& _download_data_path = "");

private:
  static std::mutex ping_mutex_;
  std::mutex tcp_mutex_;
  std::vector<char> read_data_buffer_;
  NetworkInfo net_work_info_ {};  // NOTE need to init in call

  std::unique_ptr<boost::asio::ip::tcp::socket> ptr_socket_ { nullptr };
  std::unique_ptr<boost::asio::deadline_timer> ptr_deadline_timer_ { nullptr };
  std::unique_ptr<boost::asio::io_context> ptr_io_context_ { nullptr };

  std::shared_ptr<std::thread> ptr_thread_ { nullptr };
  std::atomic<bool> flag_thread_run_ { false };

  std::array<char, 4096> reg_back_data_ { 0x0 };
  std::array<char, 4096> collect_back_data_ { 0x0 };
  std::array<bool, STATE_TOTAL_NUM> read_write_state_ { false };  //init in construct
  std::string msg_header_;
  std::atomic<bool> is_abort_ { false };
  bool is_connect_success_ { false };
  std::size_t timeout_count_ms_ { 5000 };
  std::vector<std::function<void()>> vec_timeout_cb_;
  int reg_back_data_length_;

  LidarHardwareSerial lidar_hardware_serial_ { LidarHardwareSerial::UNKNOWN_LIDAR_HARDWARE_SERIAL };
  LidarSoftwareVersion lidar_software_version_ { LidarSoftwareVersion::UNKNOWN_LIDAR_SOFTWARE_SERIAL };
  LidarFunctionSafeVersion lidar_function_safe_version_ {
    LidarFunctionSafeVersion::UNKNOWN_LIDAR_FUNCTION_SAFE_VERSION
  };
  LaserModuleVersion laser_module_version_ { UNKNOWN_LASER_MODULE_VERSION };
  DigitalBoardVersion digital_board_version_ { UNKNOWN_DIGITAL_BOARD_VERSION };

  std::string ip_;
  uint16_t port_ { 6699 };
};

}  // namespace lidar
}  // namespace robosense
#endif  // MEMS_TCP_H
