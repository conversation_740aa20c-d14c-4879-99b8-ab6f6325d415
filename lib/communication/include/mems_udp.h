﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   mems_udp.h
 * <AUTHOR> Chen (<EMAIL>), Sloan Xi (<EMAIL>), Vance Huang (<EMAIL>)
 * @brief     you can use MEMSUDP to capture UDP data upload from lidar which defined by RoboSense
 * @version 1.1.0
 * @date 2023-05-11
 * 
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *  
 * You can not use, copy or spread without official authorization.
 * 
 * If you find any BUG or improvement in MEMSUDP, please contact the authors, so we can share your idea  
 * 
 */

#ifndef MEMS_UDP_H
#define MEMS_UDP_H

#include <atomic>
#include <functional>
#include <memory>
#include <thread>
#include <utility>  // for pair
#include <vector>

#include <boost/asio.hpp>

namespace robosense
{
namespace lidar
{
class MEMSUDP
{
public:
  /**
   * @brief     Construct a new MEMSUDP object
   * 
   * @param     _len               captured udp's length is not equal with _len will be delete
   */
  explicit MEMSUDP(std::size_t _len);
  explicit MEMSUDP(MEMSUDP&&)        = delete;
  explicit MEMSUDP(const MEMSUDP&)   = delete;
  MEMSUDP& operator=(MEMSUDP&&)      = delete;
  MEMSUDP& operator=(const MEMSUDP&) = delete;
  ~MEMSUDP();

  /**
   * @brief     reset socket and start a thread to capture data
   * 
   * @param     _ip                not used in here
   * @param     _port              udp port of local computer
   * @param     _group_ip          set the join group ip
   * @retval    true               start successfully
   * @retval    false              start failed
   */
  bool start(const std::string& _ip, const uint16_t _port, const std::string& _group_ip = "");
  /// you can use this version with WidgetLidarInfo
  bool start(const std::pair<std::string, uint16_t>& _network_info, const std::string& _group_ip = "");
  /**
   * @brief     close socket and delete thread   
   */
  bool stop();
  /**
   * @brief     _callback will be called if capture a udp that satisfy the rule
   * 
   * @param     _callback          callback function
   */
  void regRecvCallback(const std::function<void(const char*)>& _callback);
  /**
   * @brief     _callback will be called if failed to capture a udp that satisfy the rule in timeout_count_ms_
   * 
   * @param     _callback          callback function
   */
  void regTimeoutCallback(const std::function<void()>& _callback);
  /**
   * @brief     check if is timeout
   */
  bool isTimeout() const { return timeout_; }
  /**
   * @brief     Set the Timeout count in ms
   * 
   * @param     _timeout_count_ms  wait for _timeout_count_ms ms to capture a udp
   */
  void setTimeoutMS(const std::size_t _timeout_count_ms);
  /**
   * @brief     Set the user layer length, thers is no user layer if it is 0
   * 
   * @param     _user_layer_bytes  the length of user layer
   */
  void setUserLayerLength(const std::size_t _user_layer_bytes);

private:
  void dataProcess();
  void checkDeadline();

  std::string ip_;
  uint16_t port_ { 6699 };
  std::size_t pkt_length_ { 0 };
  std::size_t user_layer_length_ { 0 };
  std::string msg_header_ { "MEMSUDP::" };

  std::unique_ptr<boost::asio::ip::udp::socket> ptr_socket_ { nullptr };
  std::unique_ptr<boost::asio::deadline_timer> ptr_deadline_timer_ { nullptr };
  std::unique_ptr<boost::asio::io_context> ptr_io_context_ { nullptr };
  std::vector<std::function<void(const char*)>> vec_recv_cb_;
  std::vector<std::function<void()>> vec_timeout_cb_;

  std::atomic<bool> flag_thread_run_ { false };
  std::unique_ptr<std::thread> ptr_thread_ { nullptr };
  std::atomic<bool> timeout_ { false };
  std::size_t timeout_count_ms_ { 1000 };
};
}  // namespace lidar
}  // namespace robosense

#endif  // MEMS_UDP_H