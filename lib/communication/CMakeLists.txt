﻿cmake_minimum_required(VERSION 3.1.0)

if(POLICY CMP0048)
    cmake_policy(SET CMP0048 NEW)
endif(POLICY CMP0048)

if(POLICY CMP0076)
    cmake_policy(SET CMP0076 NEW)
endif(POLICY CMP0076)

string(TIMESTAMP PROJECT_COMPILE_DATE "%Y%m%d")
project(mems_communication VERSION 1.1.7.${PROJECT_COMPILE_DATE})

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
    set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
endif()

set(CMAKE_BUILD_TYPE RelWithDebInfo)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# =========================
# option
# =========================
option(BUILD_MEMS_COMMUNICATION_EXAMPLE "build mems communication example or not" OFF)
option(BUILD_MEMS_COMMUNICATION_TEST "build mems communication gtest or not" OFF)

message("build mems communication example or not: " ${BUILD_MEMS_COMMUNICATION_EXAMPLE})

find_package(Git QUIET)
execute_process(
    COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
    WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
    OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
    ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
    WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

if(NOT Boost_FOUND)
    if(WIN32)
        if(CMAKE_SIZEOF_VOID_P EQUAL 8) # 64-bit
            set(Boost_ARCHITECTURE "-x64")
        elseif(CMAKE_SIZEOF_VOID_P EQUAL 4) # 32-bit
            set(Boost_ARCHITECTURE "-x32")
        endif()

        set(Boost_USE_STATIC_LIBS ON)
        set(Boost_USE_MULTITHREADED ON)
        set(Boost_USE_STATIC_RUNTIME OFF)
    endif(WIN32)

    find_package(
        Boost
        COMPONENTS system date_time
        REQUIRED)
endif()

find_package(
  Qt5
  COMPONENTS Core
  REQUIRED)

# =========================
# library
# =========================
add_library(${PROJECT_NAME} STATIC)
target_include_directories(${PROJECT_NAME} PUBLIC ${CMAKE_CURRENT_BINARY_DIR} include)
target_include_directories(${PROJECT_NAME} SYSTEM PUBLIC ${Boost_INCLUDE_DIRS})
target_link_libraries(${PROJECT_NAME} PUBLIC Qt5::Core ${Boost_LIBRARIES})
target_sources(${PROJECT_NAME} PRIVATE src/mems_udp.cpp src/mems_tcp.cpp)

# 配置boost采用UTF-8编码，在windows默认是使用GB2312编码，不配置会导致log乱码
target_compile_definitions(${PROJECT_NAME} PUBLIC BOOST_SYSTEM_USE_UTF8)

set_target_properties(${PROJECT_NAME} PROPERTIES
    CXX_STANDARD 14
    CXX_STANDARD_REQUIRED YES
    CXX_EXTENSIONS NO)

# =========================
# create example
# =========================
if(BUILD_MEMS_COMMUNICATION_EXAMPLE)
    add_subdirectory(example)
endif(BUILD_MEMS_COMMUNICATION_EXAMPLE)

# =========================
# create unit test
# =========================
if(BUILD_MEMS_COMMUNICATION_TEST)
    add_subdirectory(test)
endif(BUILD_MEMS_COMMUNICATION_TEST)
