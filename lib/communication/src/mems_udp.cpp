﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "../include/mems_udp.h"
#include "rsfsc_log/rsfsc_log.h"
#include <boost/asio.hpp>
#include <boost/bind.hpp>
#include <iostream>

// NOTE learn from:
// https://www.boost.org/doc/libs/1_52_0/doc/html/boost_asio/example/timeouts/blocking_udp_client.cpp

namespace robosense
{
namespace lidar
{

MEMSUDP::MEMSUDP(std::size_t _len) : pkt_length_(_len)
{
  ptr_io_context_     = std::make_unique<boost::asio::io_context>();
  ptr_deadline_timer_ = std::make_unique<boost::asio::deadline_timer>(*ptr_io_context_);
  RSFSCLog::getInstance()->info(msg_header_ + "MEMSUDP -> construct with len: " + std::to_string(_len));
}

MEMSUDP::~MEMSUDP()
{
  stop();
  if (nullptr != ptr_deadline_timer_)
  {
    ptr_deadline_timer_.reset();
  }
  if (nullptr != ptr_io_context_)
  {
    ptr_io_context_.reset();
  }
}

bool MEMSUDP::start(const std::pair<std::string, uint16_t>& _network_info, const std::string& _group_ip)
{
  return start(_network_info.first, _network_info.second, _group_ip);
}

bool MEMSUDP::start(const std::string& _ip, const uint16_t _port, const std::string& _group_ip)
{
  ip_         = _ip;
  port_       = _port;
  timeout_    = false;
  msg_header_ = "MEMSUDP" + std::to_string(_port) + "::";
  // NOTE start timer before start thread, otherwise timer in thread will be reset by this
  ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
  checkDeadline();  // Start the persistent actor that checks for deadline expiry.
  try
  {
    stop();
    ptr_socket_ = std::make_unique<boost::asio::ip::udp::socket>(
      *ptr_io_context_, boost::asio::ip::udp::endpoint(boost::asio::ip::udp::v4(), port_));
    if (!_group_ip.empty())
    {
      struct ip_mreq mreq;  // 多播地址结构体
      mreq.imr_multiaddr.s_addr = inet_addr(_group_ip.c_str());
      mreq.imr_interface.s_addr = inet_addr(ip_.c_str());
      if (setsockopt(ptr_socket_->native_handle(), IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) == -1)
      {
        RSFSCLog::getInstance()->error(msg_header_ + std::string("start -> _multicast_ip failed: ") + _group_ip);
        RSFSCLog::getInstance()->error(std::string("errno = ") + strerror(errno));
        return false;
      }
      // ptr_socket_->set_option(boost::asio::ip::udp::socket::reuse_address(true));
      // ptr_socket_->set_option(boost::asio::ip::multicast::join_group(boost::asio::ip::make_address(_group_ip)));
    }
    flag_thread_run_.store(true);
    ptr_thread_ = std::make_unique<std::thread>([this]() { dataProcess(); });
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error(msg_header_ + std::string("start -> start udp failed: ") + e.what());
    return false;
  }
  RSFSCLog::getInstance()->info(msg_header_ + "start -> ip: " + ip_ + " port_: " + std::to_string(port_));
  return true;
}

bool MEMSUDP::stop()
{
  bool res = true;
  try
  {
    boost::system::error_code err_code;
    if (flag_thread_run_.load())
    {
      flag_thread_run_.store(false);
      // NOTE must stop timer before close
      ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
      ptr_socket_->cancel(err_code);
      if (err_code)
      {
        RSFSCLog::getInstance()->error(msg_header_ + std::string("  -> cancel failed: ") + err_code.message());
      }
    }
    if (ptr_thread_ != nullptr && ptr_thread_->joinable())
    {
      RSFSCLog::getInstance()->info(msg_header_ + "stop -> udp thread start joining");
      ptr_thread_->join();
      ptr_socket_->close(err_code);
      if (err_code)
      {
        RSFSCLog::getInstance()->error(msg_header_ + std::string("  -> close failed: ") + err_code.message());
      }
      RSFSCLog::getInstance()->info(msg_header_ + "stop");
    }
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error(msg_header_ + std::string("stop -> stop udp failed: ") + e.what());
    res = false;
  }

  if (nullptr != ptr_socket_)
  {
    ptr_socket_.reset();
  }
  if (nullptr != ptr_thread_)
  {
    ptr_thread_.reset();
  }

  return res;
}

void MEMSUDP::regRecvCallback(const std::function<void(const char*)>& _callback)
{
  vec_recv_cb_.emplace_back(_callback);
}

void MEMSUDP::regTimeoutCallback(const std::function<void()>& _callback) { vec_timeout_cb_.emplace_back(_callback); }

static void handleReceive(const boost::system::error_code& _ec,
                          std::size_t _length,
                          boost::system::error_code* _out_ec,
                          std::size_t* _out_length)
{
  *_out_ec     = _ec;
  *_out_length = _length;
}

void MEMSUDP::dataProcess()
{
  ptr_socket_->cancel();
  std::vector<char> recv_buffer(pkt_length_, 0x0);
  while (flag_thread_run_.load())
  {
    std::size_t rec_len          = 0;
    boost::system::error_code ec = boost::asio::error::would_block;  // must be set would_block
    // Set a deadline for the asynchronous operation.
    ptr_deadline_timer_->expires_from_now(boost::posix_time::milliseconds(timeout_count_ms_));
    ptr_socket_->async_receive(boost::asio::buffer(recv_buffer), [capture0 = &ec, capture1 = &rec_len](auto&& _ph1,
                                                                                                       auto&& _ph2) {
      return handleReceive(std::forward<decltype(_ph1)>(_ph1), std::forward<decltype(_ph2)>(_ph2), capture0, capture1);
    });
    // Block until the asynchronous operation has completed.
    do
    {
      ptr_io_context_->run_one();
    } while (ec == boost::asio::error::would_block);

    if (ec)
    {
      RSFSCLog::getInstance()->warn(std::string(msg_header_ + "dataProcess -> capture error: ") + ec.message());
      // if (ec == boost::asio::error::connection_aborted) // TODO check disconnect and
      // {

      // }
      continue;
    }
    if (rec_len != pkt_length_)
    {
      std::cout << "[DEBUG] MEMSUDP数据长度不匹配: 接收到=" << rec_len << ", 期望=" << pkt_length_ << std::endl;
      RSFSCLog::getInstance()->debug(
        std::string(msg_header_ + "dataProcess -> capture len is not equal with required: ") + std::to_string(rec_len));
      continue;
    }
    std::cout << "[DEBUG] MEMSUDP接收到正确长度的数据: " << rec_len << " 字节" << std::endl;
    timeout_ = false;
    for (auto& iter : vec_recv_cb_)
    {
      iter(&recv_buffer.at(user_layer_length_));
    }
  }
}

void MEMSUDP::checkDeadline()
{
  if (ptr_deadline_timer_->expires_at() <= boost::asio::deadline_timer::traits_type::now())
  {
    ptr_socket_->cancel();
    timeout_ = true;
    for (auto& iter : vec_timeout_cb_)
    {
      iter();
    }
    ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
  }
  // Put the actor back to sleep.
  ptr_deadline_timer_->async_wait([this](const boost::system::error_code& _ec) {
    (void)_ec;  //unused
    checkDeadline();
  });
}

void MEMSUDP::setTimeoutMS(const std::size_t _timeout_count_ms) { timeout_count_ms_ = _timeout_count_ms; }

void MEMSUDP::setUserLayerLength(const std::size_t _user_layer_bytes) { user_layer_length_ = _user_layer_bytes; }

}  // namespace lidar
}  // namespace robosense