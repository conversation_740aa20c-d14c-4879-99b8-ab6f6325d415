﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   rsfsc_log.h
 * <AUTHOR> Shen (<EMAIL>), Antoine Chen (<EMAIL>), David Dai (<EMAIL>)
 * @brief
 * @version 2.0.0
 * @date 2024-08-07
 *
 * Copyright (c) 2024, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *
 * If you find any BUG or improvement in RSFSCLog, please contact the authors,
 * so we can share your idea
 *
 */
#ifndef RSFSCLOG_IMPL_H
#define RSFSCLOG_IMPL_H

// for compatibility
#ifdef RSFSCLOG_UES_SPDLOG
#  ifndef RSFSCLOG_USE_SPDLOG
#    define RSFSCLOG_USE_SPDLOG
#  endif  // RSFSCLOG_USE_SPDLOG
#endif

#ifndef RSFSCLOG_SUPPORT_INDEX
#  define RSFSCLOG_SUPPORT_INDEX
#endif

#include "rsfsc_fmt.h"
#include <QtCore/QString>
#include <array>
#include <mutex>
#include <unordered_map>

namespace spdlog
{
class logger;
}  // namespace spdlog
class QObject;

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
constexpr static int RSFSCLOG_DEFAULT_INDEX { -1 };

/**
 * @brief    RSFSCLog -> RoboSense Factory Software Common Log
 *           
 * @details RSFSCLog is a singleton class that all Factory Software group's project will use to log information.
 *          It provides trace, debug, info, warn, error totally 5 levels 
 *          and terminal, QMessageBrowser, rotate file totally 3 way to show message.
 *          Implement base on spdlog, add crash stack trace for linux 
 */
class RSFSCLog
{
public:
  RSFSCLog(RSFSCLog&&)                 = delete;
  RSFSCLog(const RSFSCLog&)            = delete;
  RSFSCLog& operator=(RSFSCLog&&)      = delete;
  RSFSCLog& operator=(const RSFSCLog&) = delete;

  template <typename... Args>
  static inline std::string format(Args&&... _args)
  {
    return fmt::format(std::forward<Args>(_args)...);
  }

#ifndef SPDLOG_NO_EXCEPTIONS
  // NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#  ifdef RSFSCLOG_USE_SPDLOG
  // NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#    define RSFSCLOG_LOGGER_CATCH(message)                                                                        \
      catch (const std::exception& _ex)                                                                           \
      {                                                                                                           \
        logError(std::string("log format error: ") + _ex.what() + ", raw fmt: \"" + std::string(message) + "\""); \
      }
#  else
  // NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#    define RSFSCLOG_LOGGER_CATCH(message)                                                                          \
      catch (const std::exception& _ex)                                                                             \
      {                                                                                                             \
        fmt::print(std::string("log format error: ") + _ex.what() + ", raw fmt: \"" + std::string(message) + "\""); \
      }
#  endif  // SPDLOG_NO_EXCEPTIONS
#else
#  define RSFSCLOG_LOGGER_CATCH(message)
#endif

#ifdef RSFSCLOG_USE_SPDLOG
  static RSFSCLog* getInstance(const int _index = RSFSCLOG_DEFAULT_INDEX);
  static RSFSCLog* getSettingInstance();
  ~RSFSCLog();
  /**
   * @brief RSFSCLog support 5 level totally
   */
  enum LogLevel
  {
    // NOTE must be same as spdlog
    RSFSCLOG_LEVEL_TRACE = 0,
    RSFSCLOG_LEVEL_DEBUG = 1,
    RSFSCLOG_LEVEL_INFO  = 2,
    RSFSCLOG_LEVEL_WARN  = 3,
    RSFSCLOG_LEVEL_ERROR = 4,
    RSFSCLOG_LEVEL_OFF   = 6
  };
  /**
   * @brief     now we support three sink type
   */
  enum SinkType
  {
    SINK_TYPE_TERMINAL = 0,
    SINK_TYPE_FILE,
    SINK_TYPE_QMESSAGE_BROWSER
  };
  /// log level's name string
  constexpr static std::array<const char*, 7> LOG_LEVEL_STR = { "trace", "debug", "info", "warn", "error", "", "off" };
  /// sink type's name string
  constexpr static std::array<const char*, 3> SINK_TYPE_STR = { "terminal", "file", "qmessage_browser" };

  void setFileLogLevel(LogLevel _ll);
  void setTerminalLogLevel(LogLevel _ll);
  void setQMessageBrowserLogLevel(LogLevel _ll);

  static void setAllFileLogLevel(LogLevel _ll);
  static void setAllTerminalLogLevel(LogLevel _ll);
  static void setAllQMessageBrowserLogLevel(LogLevel _ll);

  LogLevel getFileLogLevel();
  LogLevel getTerminalLogLevel();
  LogLevel getQMessageBrowserLogLevel();

  /**
   * @brief     change file save path, this function will cut of all the log for about 100ms to ensure thread safety
   *            there is RSFSCLog's duty to create the file, but user should guarantee the path is writeable
   * @param     _file_path         new file's path to save the log, like /home/<USER>/Desktop/log.txt
   * @return    change successfully or not
   */
  bool changeFileSavePath(const std::string& _file_path);
  /**
   * @brief     get current file save path
   * 
   * @return    std::string        current file save path
   */
  std::string getFileSavePath() const;
  /**
   * @brief     set file rotating param, when file's size bigger then _single_file_size_mb, a new file will be create
   *            when more than _total_files files have been created in a folder, the older file will be delete
   * @param     _single_file_size_mb    single file's size, unit is MB
   * @param     _total_files            total files saved in the folder
   */
  void setFileRotatingParam(std::size_t _single_file_size_mb, std::size_t _total_files);
  /**
   * @brief     reset file save path to ~/.RoboSense/normal_log/program_name_log.txt
   * 
   * @return    change successfully or not           
   */
  bool resetFileSavePath();
  /**
   * @brief     flush all the log users have log, otherwise RSFSCLog will flush every 3 seconds
   * 
   */
  void flush();
  /**
   * @brief     Set the Qt Log Widget
   * 
   * @param     _qt_object         only support robosense::lidar::MessageBrowser, you can get message_browser.h and message_browser.cpp in release folder
   * @param     _func_name         default is slotShowMessage
   */
  static void setQtLogWidget(QObject* _qt_object, const std::string& _func_name = "slotShowMessage");

#else  // UNUSE RSFSCLOG_USE_SPDLOG
  static std::string toStdString(const QString& _str) { return _str.toStdString(); }
  static RSFSCLog* getInstance(const int /*_index*/ = RSFSCLOG_DEFAULT_INDEX)
  {
    static RSFSCLog instance("normal_log");
    return &instance;
  }

  static RSFSCLog* getSettingInstance()
  {
    static RSFSCLog instance("setting_log");
    return &instance;
  }
  ~RSFSCLog() = default;

#endif  // UNUSE RSFSCLOG_USE_SPDLOG

  template <typename String, typename... Args>
  inline void info(String&& _fmt, Args&&... _args)
  {
    try
    {
#ifdef RSFSCLOG_USE_SPDLOG
      logInfo(format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#else
      fmt::print("\033[32m{}\033[0m\n", fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#endif
    }
    RSFSCLOG_LOGGER_CATCH(_fmt);
  }

  template <typename String, typename... Args>
  inline void debug(String&& _fmt, Args&&... _args)
  {
    try
    {
#ifdef RSFSCLOG_USE_SPDLOG
      logDebug(format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#else
      fmt::print("\033[34m{}\033[0m\n", fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#endif
    }
    RSFSCLOG_LOGGER_CATCH(_fmt);
  }

  template <typename String, typename... Args>
  inline void warn(String&& _fmt, Args&&... _args)
  {
    try
    {
#ifdef RSFSCLOG_USE_SPDLOG
      logWarn(format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#else
      fmt::print("\033[33m{}\033[0m\n", fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#endif
    }
    RSFSCLOG_LOGGER_CATCH(_fmt);
  }

  template <typename String, typename... Args>
  inline void error(String&& _fmt, Args&&... _args)
  {
    try
    {
#ifdef RSFSCLOG_USE_SPDLOG
      logError(format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#else
      fmt::print("\033[31m{}\033[0m\n", fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#endif
    }
    RSFSCLOG_LOGGER_CATCH(_fmt);
  }

  template <typename String, typename... Args>
  inline void trace(String&& _fmt, Args&&... _args)
  {
    try
    {
#ifdef RSFSCLOG_USE_SPDLOG
      logTrace(format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#else
      fmt::print("\033[36m{}\033[0m\n", fmt::format(std::forward<String>(_fmt), std::forward<Args>(_args)...));
#endif
    }
    RSFSCLOG_LOGGER_CATCH(_fmt);
  }

protected:
  std::shared_ptr<spdlog::logger> getLogger() const;

private:
#ifdef RSFSCLOG_USE_SPDLOG
  explicit RSFSCLog(const std::string& _log_name, const int _index = RSFSCLOG_DEFAULT_INDEX);
  /**
   * @brief     Get the Program Name
   * 
   * @return    std::string        name of current program
   */
  static std::string getProgramName();
  static std::string getHomePath();
  /**
   * @brief     when crash happen, a file with crash stack trace will be created in ~/.RoboSense/crash_log
   * 
   * @param     _program_name      file name of the crash log file
   */
  static void crashStackTrace(const std::string& _program_name);
  /**
   * @brief     Get the Min Level of all sink type
   * 
   * @return    LogLevel           min level return
   */
  static LogLevel getMinLevel();

  // NOTE log should be as small as possible, file need date, we will not watch terminal across two days, so terminal no need date info
  constexpr static const char* FILE_LOG_PATTERN     = "%C%m%d_%H%M%S.%e %L [%n] %t %v";
  constexpr static const char* TERMINAL_LOG_PATTERN = "%^%H:%M:%S.%e %L [%n] %t %v%$";
  constexpr static const char* QT_LOG_PATTERN = "[%L] [%n] %v";  /// this should change synchronous with MessageBrowser
  std::string init_log_file_;
  /// when file's size bigger then file_rotating_size_mb_, a new file will be create
  std::size_t file_rotating_size_mb_ = 10;
  /// when more than files_saved_quantity_ files have been created in a folder, the older file will be delete
  std::size_t files_saved_quantity_ = 10;
  /// current file path
  std::string curr_file_path_;
  static std::unordered_map<int, std::unique_ptr<RSFSCLog>> instances_;
  static std::mutex mutex_;

  std::shared_ptr<spdlog::logger> ptr_spdlog_logger_;

  void logInfo(const std::string& _msg);
  void logDebug(const std::string& _msg);
  void logWarn(const std::string& _msg);
  void logError(const std::string& _msg);
  void logTrace(const std::string& _msg);
#else
  explicit RSFSCLog(const std::string& _log_name, const int _index = RSFSCLOG_DEFAULT_INDEX) {};
#endif  // RSFSCLOG_USE_SPDLOG
};  // class RSFSCLog

}  // namespace lidar
}  // namespace robosense

template <>
struct fmt::formatter<QString> : fmt::formatter<fmt::string_view>
{
  template <typename FormatContext>
  auto format(const QString& _qstr, FormatContext& _ctx) const -> decltype(_ctx.out())
  {
    std::string str = robosense::lidar::RSFSCLog::toStdString(_qstr);
    return fmt::formatter<fmt::string_view>::format(str, _ctx);
  }
};

#endif  // RSFSCLOG_IMPL_H
