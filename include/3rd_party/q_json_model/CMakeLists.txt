cmake_minimum_required(VERSION 3.16)
project(
  Qt5JsonModel
  VERSION 0.0.2
  LANGUAGES CXX
  DESCRIPTION
    "QJsonModel is a json tree model class for Qt5/C++17 based on QAbstractItemModel. MIT License."
)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Enable Qt's automoc for QObject/Q_OBJECT
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC OFF)
set(CMAKE_AUTOUIC OFF)

find_package(Qt5 REQUIRED COMPONENTS Core Widgets Gui)

# Object library with actual sources
add_library(QJsonModelObj OBJECT QJsonModel.cpp include/QJsonModel.hpp)
target_link_libraries(QJsonModelObj PUBLIC Qt5::Core Qt5::Gui Qt5::Widgets)
target_include_directories(QJsonModelObj PUBLIC include)

# Static library aggregating object files
add_library(QJsonModelStatic STATIC)
add_library(Qt5::QJsonModelStatic ALIAS QJsonModelStatic)
if(WIN32)
  set_target_properties(QJsonModelStatic PROPERTIES OUTPUT_NAME "QJsonModelLIB")
else()
  set_target_properties(QJsonModelStatic PROPERTIES OUTPUT_NAME "QJsonModel")
endif()

target_sources(QJsonModelStatic PRIVATE $<TARGET_OBJECTS:QJsonModelObj>)
target_link_libraries(QJsonModelStatic PUBLIC Qt5::Core Qt5::Gui Qt5::Widgets)
target_include_directories(QJsonModelStatic PUBLIC include)

# Shared library aggregating object files
add_library(QJsonModelShared SHARED)
add_library(Qt5::QJsonModelShared ALIAS QJsonModelShared)
set_target_properties(QJsonModelShared PROPERTIES OUTPUT_NAME "QJsonModel")

target_sources(QJsonModelShared PRIVATE $<TARGET_OBJECTS:QJsonModelObj>)
target_link_libraries(QJsonModelShared PUBLIC Qt5::Core Qt5::Gui Qt5::Widgets)
target_include_directories(QJsonModelShared PUBLIC include)

# Provide a convenient alias pointing to the static lib by default
add_library(Qt5::QJsonModel ALIAS QJsonModelStatic)