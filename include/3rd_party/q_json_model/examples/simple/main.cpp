#include <QApplication>
#include <QTreeView>
#include <QHeaderView>
#include <QAbstractItemView>
#include <QByteArray>

#include "QJsonModel.hpp"

static QByteArray createSampleJson()
{
  const QByteArray k_default = R"JSON(
{
  "name": "Qt5 QJsonModel Example",
  "version": 1,
  "features": [
    {"id": 1, "name": "tree-view"},
    {"id": 2, "name": "editable-values"}
  ],
  "flags": {
    "enabled": true,
    "beta": false
  }
}
)JSON";
  return k_default;
}

int main(int _argc, char **_argv)
{
  QApplication app_ctx(_argc, _argv);

  QJsonModel model_ctx;
  model_ctx.loadJson(createSampleJson());

  // 可选：忽略注释类键
  // model_ctx.addException({"//", "__comment"});

  QTreeView tree_view;
  tree_view.setModel(&model_ctx);
  tree_view.setAlternatingRowColors(true);
  tree_view.setUniformRowHeights(true);
  tree_view.setEditTriggers(QAbstractItemView::NoEditTriggers);

  tree_view.header()->setSectionResizeMode(QHeaderView::ResizeToContents); // 自适应列宽
  tree_view.header()->setDefaultAlignment(Qt::AlignCenter);                // 表头居中
  tree_view.setAnimated(true);                                             // 用来控制树形控件中节点展开/收起时是否启用动画效果

  tree_view.setWindowTitle("QJsonModel Qt5 Example");
  tree_view.setStyleSheet(("QTreeView::item {border:1px solid #E8E8E8}"
                           "QTreeView::item::selected {background-color:#4682B4}"));
  tree_view.resize(600, 400);
  tree_view.show();

  return app_ctx.exec();
}
