cmake_minimum_required(VERSION 3.16)
project(qjsonmodel_example_simple LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(CMAKE_AUTOMOC ON)

add_executable(qjsonmodel_example_simple
  main.cpp
)

# Link against Qt5 and the QJsonModel library alias
# The alias resolves to the static lib by default (Qt5::QJsonModel -> QJsonModelStatic)
target_link_libraries(qjsonmodel_example_simple PRIVATE
  Qt5::Core Qt5::Gui Qt5::Widgets
  Qt5::QJsonModel
)

# Enable useful warnings for deprecated APIs
target_compile_definitions(qjsonmodel_example_simple PRIVATE
  QT_DEPRECATED_WARNINGS
)

