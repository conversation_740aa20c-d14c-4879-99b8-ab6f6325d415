<?xml version="1.0" encoding="UTF-8" ?>
<root>
    <section name="Keyword">
        <name>attribute</name>
        <name>const</name>
        <name>uniform</name>
        <name>varying</name>
        <name>buffer</name>
        <name>shared</name>
        <name>coherent</name>
        <name>volatile</name>
        <name>restrict</name>
        <name>readonly</name>
        <name>writeonly</name>
        <name>layout</name>
        <name>centroid</name>
        <name>flat</name>
        <name>smooth</name>
        <name>noperspective</name>
        <name>patch</name>
        <name>sample</name>
        <name>break</name>
        <name>continue</name>
        <name>do</name>
        <name>for</name>
        <name>while</name>
        <name>switch</name>
        <name>case</name>
        <name>default</name>
        <name>if</name>
        <name>else</name>
        <name>subroutine</name>
        <name>in</name>
        <name>out</name>
        <name>inout</name>
        <name>invariant</name>
        <name>precise</name>
        <name>discard</name>
        <name>return</name>
        <name>lowp</name>
        <name>mediump</name>
        <name>highp</name>
        <name>precision</name>
        <name>struct</name>
    </section>
    <section name="Function">
        <name>radians</name>
        <name>degrees</name>
        <name>sin</name>
        <name>cos</name>
        <name>tan</name>
        <name>asin</name>
        <name>acos</name>
        <name>atan</name>
        <name>sinh</name>
        <name>cosh</name>
        <name>tanh</name>
        <name>asinh</name>
        <name>acosh</name>
        <name>atanh</name>
        <name>pow</name>
        <name>exp</name>
        <name>log</name>
        <name>exp2</name>
        <name>log2</name>
        <name>sqrt</name>
        <name>inversesqrt</name>
        <name>abs</name>
        <name>sign</name>
        <name>floor</name>
        <name>trunc</name>
        <name>round</name>
        <name>roundEven</name>
        <name>ceil</name>
        <name>fract</name>
        <name>mod</name>
        <name>modf</name>
        <name>min</name>
        <name>max</name>
        <name>clamp</name>
        <name>mix</name>
        <name>step</name>
        <name>smoothstep</name>
        <name>isnan</name>
        <name>isinf</name>
        <name>floatBitsToInt</name>
        <name>floatBitsToUint</name>
        <name>intBitsToFloat</name>
        <name>uintBitsToFloat</name>
        <name>fma</name>
        <name>frexp</name>
        <name>ldexp</name>
        <name>packUnorm2x16</name>
        <name>packSnorm2x16</name>
        <name>packUnorm4x8</name>
        <name>packSnorm4x8</name>
        <name>unpackUnorm2x16</name>
        <name>unpackSnorm2x16</name>
        <name>unpackUnorm4x8</name>
        <name>unpackSnorm4x8</name>
        <name>packDouble2x32</name>
        <name>unpackDouble2x32</name>
        <name>packHalf2x16</name>
        <name>unpackHalf2x16</name>
        <name>length</name>
        <name>distance</name>
        <name>dot</name>
        <name>cross</name>
        <name>normalize</name>
        <name>ftransform</name>
        <name>faceforward</name>
        <name>reflect</name>
        <name>refract</name>
        <name>matrixCompMult</name>
        <name>outerProduct</name>
        <name>transpose</name>
        <name>determinant</name>
        <name>inverse</name>
        <name>lessThan</name>
        <name>lessThanEqual</name>
        <name>greaterThan</name>
        <name>greaterThanEqual</name>
        <name>equal</name>
        <name>notEqual</name>
        <name>any</name>
        <name>all</name>
        <name>not</name>
        <name>uaddCarry</name>
        <name>usubBorrow</name>
        <name>umulExtended</name>
        <name>imulExtended</name>
        <name>bitfieldExtract</name>
        <name>bitfieldInsert</name>
        <name>bitfieldReverse</name>
        <name>bitCount</name>
        <name>findLSB</name>
        <name>findMSB</name>
        <name>textureSize</name>
        <name>textureQueryLod</name>
        <name>textureQueryLevels</name>
        <name>textureSamples</name>
        <name>texture</name>
        <name>textureProj</name>
        <name>textureLod</name>
        <name>textureOffset</name>
        <name>texelFetch</name>
        <name>texelFetchOffset</name>
        <name>textureProjOffset</name>
        <name>textureLodOffset</name>
        <name>textureProjLod</name>
        <name>textureProjLodOffset</name>
        <name>textureGrad</name>
        <name>textureGradOffset</name>
        <name>textureProjGrad</name>
        <name>textureProjGradOffset</name>
        <name>textureGather</name>
        <name>textureGatherOffset</name>
        <name>textureGatherOffsets</name>
        <name>texture1D</name>
        <name>texture1DProj</name>
        <name>texture1DLod</name>
        <name>texture1DProjLod</name>
        <name>texture2D</name>
        <name>texture2DProj</name>
        <name>texture2DLod</name>
        <name>texture2DProjLod</name>
        <name>texture3D</name>
        <name>texture3DProj</name>
        <name>texture3DLod</name>
        <name>texture3DProjLod</name>
        <name>textureCube</name>
        <name>textureCubeLod</name>
        <name>shadow1D</name>
        <name>shadow2D</name>
        <name>shadow1DProj</name>
        <name>shadow2DProj</name>
        <name>shadow1DLod</name>
        <name>shadow2DLod</name>
        <name>shadow1DProjLod</name>
        <name>shadow2DProjLod</name>
        <name>atomicCounterIncrement</name>
        <name>atomicCounterDecrement</name>
        <name>atomicCounter</name>
        <name>atomicAdd</name>
        <name>atomicMin</name>
        <name>atomicMax</name>
        <name>atomicAnd</name>
        <name>atomicOr</name>
        <name>atomicXor</name>
        <name>atomicExchange</name>
        <name>atomicCompSwap</name>
        <name>imageSize</name>
        <name>imageSamples</name>
        <name>imageLoad</name>
        <name>imageStore</name>
        <name>imageAtomicAdd</name>
        <name>imageAtomicMin</name>
        <name>imageAtomicMax</name>
        <name>imageAtomicAnd</name>
        <name>imageAtomicOr</name>
        <name>imageAtomicXor</name>
        <name>imageAtomicExchange</name>
        <name>imageAtomicCompSwap</name>
        <name>dFdx</name>
        <name>dFdy</name>
        <name>dFdxFine</name>
        <name>dFdyFine</name>
        <name>dFdxCoarse</name>
        <name>dFdyCoarse</name>
        <name>fwidth</name>
        <name>fwidthFine</name>
        <name>fwidthCoarse</name>
        <name>interpolateAtCentroid</name>
        <name>interpolateAtSample</name>
        <name>interpolateAtOffset</name>
        <name>noise1</name>
        <name>noise2</name>
        <name>noise3</name>
        <name>noise4</name>
        <name>EmitStreamVertex</name>
        <name>EndStreamPrimitive</name>
        <name>EndStreamPrimitive</name>
        <name>EndStreamPrimitive</name>
        <name>memoryBarrier</name>
        <name>memoryBarrierAtomicCounter</name>
        <name>memoryBarrierBuffer</name>
        <name>memoryBarrierShared</name>
        <name>memoryBarrierImage</name>
        <name>groupMemoryBarrier</name>
    </section>
    <section name="PrimitiveType">
        <name>float</name>
        <name>atomic_uint</name>
        <name>double</name>
        <name>int</name>
        <name>void</name>
        <name>bool</name>
        <name>true</name>
        <name>false</name>
        <name>mat2</name>
        <name>mat3</name>
        <name>mat4</name>
        <name>dmat2</name>
        <name>dmat3</name>
        <name>dmat4</name>
        <name>mat2x2</name>
        <name>mat2x3</name>
        <name>mat2x4</name>
        <name>dmat2x2</name>
        <name>dmat2x3</name>
        <name>dmat2x4</name>
        <name>mat3x2</name>
        <name>mat3x3</name>
        <name>mat3x4</name>
        <name>dmat3x2</name>
        <name>dmat3x3</name>
        <name>dmat3x4</name>
        <name>mat4x2</name>
        <name>mat4x3</name>
        <name>mat4x4</name>
        <name>dmat4x2</name>
        <name>dmat4x3</name>
        <name>dmat4x4</name>
        <name>vec2</name>
        <name>vec3</name>
        <name>vec4</name>
        <name>ivec2</name>
        <name>ivec3</name>
        <name>ivec4</name>
        <name>bvec2</name>
        <name>bvec3</name>
        <name>bvec4</name>
        <name>dvec2</name>
        <name>dvec3</name>
        <name>dvec4</name>
        <name>uint</name>
        <name>uvec2</name>
        <name>uvec3</name>
        <name>uvec4</name>
        <name>sampler1D</name>
        <name>sampler2D</name>
        <name>sampler3D</name>
        <name>samplerCube</name>
        <name>sampler1DShadow</name>
        <name>sampler2DShadow</name>
        <name>samplerCubeShadow</name>
        <name>sampler1DArray</name>
        <name>sampler2DArray</name>
        <name>sampler1DArrayShadow</name>
        <name>sampler2DArrayShadow</name>
        <name>sampler1D</name>
        <name>isampler2D</name>
        <name>isampler3D</name>
        <name>isamplerCube</name>
        <name>isampler1DArray</name>
        <name>isampler2DArray</name>
        <name>usampler1D</name>
        <name>usampler2D</name>
        <name>usampler3D</name>
        <name>usamplerCube</name>
        <name>usampler1DArray</name>
        <name>usampler2DArray</name>
        <name>sampler2DRect</name>
        <name>sampler2DRectShadow</name>
        <name>isampler2DRect</name>
        <name>usampler2DRect</name>
        <name>samplerBuffer</name>
        <name>isamplerBuffer</name>
        <name>usamplerBuffer</name>
        <name>sampler2DMS</name>
        <name>isampler2DMS</name>
        <name>usampler2DMS</name>
        <name>sampler2DMSArray</name>
        <name>isampler2DMSArray</name>
        <name>usampler2DMSArray</name>
        <name>samplerCubeArray</name>
        <name>samplerCubeArrayShadow</name>
        <name>isamplerCubeArray</name>
        <name>usamplerCubeArray</name>
        <name>image1D</name>
        <name>iimage1D</name>
        <name>uimage1D</name>
        <name>image2D</name>
        <name>iimage2D</name>
        <name>uimage2D</name>
        <name>image3D</name>
        <name>iimage3D</name>
        <name>uimage3D</name>
        <name>image2DRect</name>
        <name>iimage2DRect</name>
        <name>uimage2DRect</name>
        <name>imageCube</name>
        <name>iimageCube</name>
        <name>uimageCube</name>
        <name>imageBuffer</name>
        <name>iimageBuffer</name>
        <name>uimageBuffer</name>
        <name>image1DArray</name>
        <name>iimage1DArray</name>
        <name>uimage1DArray</name>
        <name>image2DArray</name>
        <name>iimage2DArray</name>
        <name>uimage2DArray</name>
        <name>imageCubeArray</name>
        <name>iimageCubeArray</name>
        <name>uimageCubeArray</name>
        <name>image2DMS</name>
        <name>iimage2DMS</name>
        <name>uimage2DMS</name>
        <name>image2DMSArray</name>
        <name>iimage2DMSArray</name>
        <name>uimage2DMSArray</name>
    </section>
</root>