<?xml version="1.0" encoding="UTF-8" ?>
<root>
    <section name="Keyword">
        <name>alignas</name>
        <name>alignof</name>
        <name>and</name>
        <name>and_eq</name>
        <name>asm</name>
        <name>atomic_cancel</name>
        <name>atomic_commit</name>
        <name>atomic_noexcept</name>
        <name>auto</name>
        <name>bitand</name>
        <name>bitor</name>
        <name>break</name>
        <name>case</name>
        <name>catch</name>
        <name>class</name>
        <name>compl</name>
        <name>concept</name>
        <name>const</name>
        <name>constexpr</name>
        <name>const_cast</name>
        <name>continue</name>
        <name>co_await</name>
        <name>co_return</name>
        <name>co_yield</name>
        <name>decltype</name>
        <name>default</name>
        <name>delete</name>
        <name>do</name>
        <name>dynamic_cast</name>
        <name>else</name>
        <name>enum</name>
        <name>explicit</name>
        <name>export</name>
        <name>extern</name>
        <name>false</name>
        <name>for</name>
        <name>friend</name>
        <name>goto</name>
        <name>if</name>
        <name>import</name>
        <name>inline</name>
        <name>module</name>
        <name>mutable</name>
        <name>namespace</name>
        <name>new</name>
        <name>noexcept</name>
        <name>not</name>
        <name>not_eq</name>
        <name>nullptr</name>
        <name>operator</name>
        <name>or</name>
        <name>or_eq</name>
        <name>private</name>
        <name>protected</name>
        <name>public</name>
        <name>reflexpr</name>
        <name>register(2)</name>
        <name>reinterpret_cast</name>
        <name>requires</name>
        <name>return</name>
        <name>sizeof</name>
        <name>static</name>
        <name>static_assert</name>
        <name>static_cast</name>
        <name>struct</name>
        <name>switch</name>
        <name>synchronized</name>
        <name>template</name>
        <name>this</name>
        <name>thread_local</name>
        <name>throw</name>
        <name>true</name>
        <name>try</name>
        <name>typedef</name>
        <name>typeid</name>
        <name>typename</name>
        <name>union</name>
        <name>using</name>
        <name>virtual</name>
        <name>volatile</name>
        <name>while</name>
        <name>xor</name>
        <name>xor_eq</name>
    </section>
    <section name="PrimitiveType">
        <name>bool</name>
        <name>char</name>
        <name>char16_t</name>
        <name>char32_t</name>
        <name>double</name>
        <name>float</name>
        <name>int</name>
        <name>long</name>
        <name>short</name>
        <name>signed</name>
        <name>unsigned</name>
        <name>void</name>
        <name>wchar_t</name>
    </section>
</root>