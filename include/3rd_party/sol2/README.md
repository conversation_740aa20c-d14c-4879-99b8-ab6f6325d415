# Sol2 预编译版本

这是一个预编译的 Sol2 库，包含了 Sol2 头文件和 Lua 5.4.4 动态库，可以直接通过 CMake 的 `add_subdirectory` 使用。

## 目录结构

```
sol2/
├── CMakeLists.txt          # CMake 配置文件
├── README.md              # 本文件
├── include/               # 头文件目录
│   ├── sol/               # Sol2 头文件
│   │   ├── sol.hpp        # Sol2 单头文件
│   │   ├── forward.hpp    # 前向声明
│   │   └── config.hpp     # 配置文件
│   └── lua/               # Lua 头文件
│       ├── lua.h          # Lua 主头文件
│       ├── lualib.h       # Lua 库头文件
│       ├── lauxlib.h      # Lua 辅助库头文件
│       └── ...            # 其他 Lua 头文件
└── bin/                   # 预编译的库文件
    └── liblua-5.4.4.dylib # Lua 5.4.4 动态库 (macOS)
```

## 使用方法

### 1. 复制到你的项目

将整个 `sol2` 文件夹复制到你的项目根目录下。

### 2. 在 CMakeLists.txt 中添加

```cmake
cmake_minimum_required(VERSION 3.16.0)
project(my_project)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加 sol2 子目录
add_subdirectory(sol2)

# 创建你的可执行文件
add_executable(my_app main.cpp)

# 链接 sol2 - 就这么简单！
target_link_libraries(my_app PRIVATE sol2::sol2)
```

### 3. 在代码中使用

```cpp
#include <sol/sol.hpp>
#include <iostream>

int main() {
    sol::state lua;
    lua.open_libraries(sol::lib::base);
    lua.script("print('Hello from Sol2!')");
    return 0;
}
```

### 4. 编译和运行

```bash
mkdir build && cd build
cmake ..
make
./my_app
```

## 提供的目标

- `sol2::sol2` - 主要的 Sol2 接口库目标

## 自动配置

这个 CMakeLists.txt 会自动：

1. ✅ 设置正确的头文件包含路径
2. ✅ 链接 Lua 5.4.4 动态库
3. ✅ 链接线程库 (Threads::Threads)
4. ✅ 链接动态加载库 (${CMAKE_DL_LIBS})
5. ✅ 设置 C++17 标准要求
6. ✅ 启用 Sol2 安全检查 (SOL_ALL_SAFETIES_ON=1)
7. ✅ 启用错误打印 (SOL_PRINT_ERRORS=1)

## 平台支持

当前版本支持：
- ✅ macOS (Apple Silicon & Intel)
- ❌ Linux (需要重新编译 Lua 库)
- ❌ Windows (需要重新编译 Lua 库)

## 系统要求

- CMake 3.16.0 或更高版本
- C++17 兼容的编译器
- macOS 10.15 或更高版本

## 故障排除

### 找不到动态库

如果运行时提示找不到 `liblua-5.4.4.dylib`，请确保：

1. 库文件在正确的位置
2. 设置了正确的 RPATH
3. 或者将库文件复制到可执行文件同目录

### 编译错误

如果遇到编译错误，请检查：

1. C++ 标准是否设置为 C++17 或更高
2. 是否有足够的编译器支持
3. CMake 版本是否满足要求

## 许可证

本预编译版本遵循 Sol2 的 MIT 许可证。
