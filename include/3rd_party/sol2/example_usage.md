# Sol2 使用示例

## 完整的项目结构

```
my_project/
├── CMakeLists.txt          # 主项目配置
├── main.cpp               # 主程序文件
└── sol2/                  # Sol2 预编译库（复制整个文件夹）
    ├── CMakeLists.txt
    ├── README.md
    ├── include/
    │   ├── sol/
    │   └── lua/
    └── bin/
        └── liblua-5.4.4.dylib
```

## CMakeLists.txt

```cmake
cmake_minimum_required(VERSION 3.16.0)
project(my_sol2_app VERSION 1.0.0 LANGUAGES CXX)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加 sol2 子目录
add_subdirectory(sol2)

# 创建你的可执行文件
add_executable(my_app main.cpp)

# 链接 sol2 - 就这么简单！
target_link_libraries(my_app PRIVATE sol2::sol2)

# 可选：添加编译选项
target_compile_options(my_app PRIVATE
    -Wall -Wextra -Wpedantic
)

# 在 macOS 上设置运行时库路径
if(APPLE)
    set_target_properties(my_app PROPERTIES
        INSTALL_RPATH "@executable_path"
        BUILD_WITH_INSTALL_RPATH TRUE
    )
    
    # 复制 Lua 动态库到可执行文件目录
    add_custom_command(TARGET my_app POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/sol2/bin/liblua-5.4.4.dylib"
        $<TARGET_FILE_DIR:my_app>
        COMMENT "复制 Lua 动态库到输出目录"
    )
endif()
```

## main.cpp

```cpp
#include <sol/sol.hpp>
#include <iostream>

int main() {
    std::cout << "=== Sol2 + Lua 测试 ===" << std::endl;
    
    try {
        // 创建 Lua 状态
        sol::state lua_state_;
        
        // 打开标准库
        lua_state_.open_libraries(sol::lib::base, sol::lib::math, sol::lib::string);
        
        // 执行简单的 Lua 代码
        lua_state_.script("print('Hello from Lua!')");
        
        // 设置变量
        lua_state_["my_number"] = 42;
        lua_state_["my_string"] = "Hello Sol2!";
        
        // 从 Lua 获取变量
        int number_value = lua_state_["my_number"];
        std::string string_value = lua_state_["my_string"];
        
        std::cout << "从 Lua 获取的数字: " << number_value << std::endl;
        std::cout << "从 Lua 获取的字符串: " << string_value << std::endl;
        
        // 定义 C++ 函数供 Lua 调用
        lua_state_["cpp_function"] = [](int x, int y) {
            return x + y;
        };
        
        // 在 Lua 中调用 C++ 函数
        lua_state_.script("result = cpp_function(10, 20)");
        int result = lua_state_["result"];
        std::cout << "Lua 调用 C++ 函数的结果: " << result << std::endl;
        
        std::cout << "测试成功完成！" << std::endl;
        
    } catch (const sol::error& e) {
        std::cerr << "Sol2 错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

## 编译和运行

```bash
# 1. 创建项目目录
mkdir my_project && cd my_project

# 2. 复制 sol2 文件夹到项目目录
cp -r /path/to/sol2 .

# 3. 创建 CMakeLists.txt 和 main.cpp（使用上面的内容）

# 4. 编译
mkdir build && cd build
cmake ..
make

# 5. 运行
./my_app
```

## 预期输出

```
=== Sol2 + Lua 测试 ===
Hello from Lua!
从 Lua 获取的数字: 42
从 Lua 获取的字符串: Hello Sol2!
Lua 调用 C++ 函数的结果: 30
测试成功完成！
```

## 关键特性

✅ **零配置**: 只需要 `add_subdirectory(sol2)` 和 `target_link_libraries(my_app PRIVATE sol2::sol2)`

✅ **自动依赖**: 自动链接 Lua 库、线程库和动态加载库

✅ **头文件包含**: 自动设置 Sol2 和 Lua 头文件路径

✅ **安全模式**: 默认启用 `SOL_ALL_SAFETIES_ON=1` 和 `SOL_PRINT_ERRORS=1`

✅ **跨平台兼容**: CMake 配置支持不同平台（当前版本仅支持 macOS）
