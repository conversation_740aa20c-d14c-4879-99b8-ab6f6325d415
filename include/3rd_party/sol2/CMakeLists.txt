# # # # sol2 预编译版本
# The MIT License (MIT)
# 
# Copyright (c) 2013-2022 Rapptz, ThePhD, and contributors
# 
# Permission is hereby granted, free of charge, to any person obtaining a copy of
# this software and associated documentation files (the "Software"), to deal in
# the Software without restriction, including without limitation the rights to
# use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
# the Software, and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
# 
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
# 
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
# FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
# COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
# IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

cmake_minimum_required(VERSION 3.16.0)

# 如果作为子项目被包含，不要重新定义项目
if(NOT CMAKE_PROJECT_NAME)
    project(sol2_prebuilt VERSION 4.0.0 LANGUAGES CXX)
endif()

# 查找线程库
find_package(Threads)
if(NOT Threads_FOUND)
    # 在某些系统上，线程库可能不需要显式链接
    message(STATUS "Threads library not found, continuing without it")
endif()

# 检查必要的文件是否存在
set(SOL2_HEADER_FILE "${CMAKE_CURRENT_SOURCE_DIR}/include/sol/sol.hpp")
set(LUA_LIBRARY_FILE "${CMAKE_CURRENT_SOURCE_DIR}/bin/liblua-5.4.4.dylib")

if(NOT EXISTS "${SOL2_HEADER_FILE}")
    message(FATAL_ERROR "Sol2 头文件不存在: ${SOL2_HEADER_FILE}")
endif()

if(NOT EXISTS "${LUA_LIBRARY_FILE}")
    message(FATAL_ERROR "Lua 库文件不存在: ${LUA_LIBRARY_FILE}")
endif()

# 创建 Lua 导入库目标
add_library(lua_prebuilt SHARED IMPORTED)
set_target_properties(lua_prebuilt PROPERTIES
    IMPORTED_LOCATION "${LUA_LIBRARY_FILE}"
)

# 创建 sol2 接口库
add_library(sol2 INTERFACE)
add_library(sol2::sol2 ALIAS sol2)

# 设置 sol2 的包含目录
target_include_directories(sol2 INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include/lua>
    $<INSTALL_INTERFACE:include>
    $<INSTALL_INTERFACE:include/lua>
)

# 链接必要的库
target_link_libraries(sol2 INTERFACE
    lua_prebuilt
    ${CMAKE_DL_LIBS}
)

# 如果找到了线程库，则链接它
if(Threads_FOUND)
    target_link_libraries(sol2 INTERFACE Threads::Threads)
endif()

# 设置编译特性
target_compile_features(sol2 INTERFACE cxx_std_17)

# 可选的编译定义
target_compile_definitions(sol2 INTERFACE
    SOL_ALL_SAFETIES_ON=1
    SOL_PRINT_ERRORS=1
)

# 导出目标（如果需要安装）
if(CMAKE_PROJECT_NAME STREQUAL PROJECT_NAME)
    include(GNUInstallDirs)
    
    # 安装头文件
    install(DIRECTORY include/
        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
        FILES_MATCHING PATTERN "*.hpp"
    )
    
    # 安装库文件
    install(FILES bin/liblua-5.4.4.dylib
        DESTINATION ${CMAKE_INSTALL_LIBDIR}
    )
    
    # 安装目标
    install(TARGETS sol2
        EXPORT sol2Targets
        INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
    )
    
    # 导出目标
    install(EXPORT sol2Targets
        FILE sol2Targets.cmake
        NAMESPACE sol2::
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/sol2
    )
endif()

# 提供一些有用的变量给父项目
set(SOL2_INCLUDE_DIRS "${CMAKE_CURRENT_SOURCE_DIR}/include" PARENT_SCOPE)
set(SOL2_LIBRARIES sol2::sol2 PARENT_SCOPE)
set(SOL2_FOUND TRUE PARENT_SCOPE)

# 显示配置信息
message(STATUS "Sol2 预编译版本配置完成")
message(STATUS "  - Sol2 头文件: ${SOL2_HEADER_FILE}")
message(STATUS "  - Lua 库文件: ${LUA_LIBRARY_FILE}")
message(STATUS "  - 目标: sol2::sol2")
