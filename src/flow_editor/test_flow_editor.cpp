﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "test_flow_editor.h"
#include "QtNodes/internal/Definitions.hpp"
#include "QtNodes/internal/NodeGraphicsObject.hpp"
#include "behaviortree_cpp/basic_types.h"
#include "common_types.h"
#include "data_serializer.h"
#include "node_models/bt_node_model.h"
#include "node_models/lua_script_node.h"
#include "node_models/node_manager.h"
#include "node_models/udp_collect_node.h"
#include "node_models/udp_start_node.h"
#include "node_models/udp_stop_node.h"
#include "node_utils.h"
#include "qt_logger.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "side_panel/side_panel_node_model.h"
#include "side_panel/side_panel_node_view.h"
#include "side_panel/side_panel_node_widget.h"
#include <QDrag>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMouseEvent>
#include <QtNodes/AbstractGraphModel>
#include <QtWidgets>
#include <memory>
#include <qchar.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

FlowScene::FlowScene(QtNodes::DataFlowGraphModel& _model, QObject* _parent) :
  QtNodes::DataFlowGraphicsScene(_model, _parent)
{

  QObject::connect(&_model, &QtNodes::DataFlowGraphModel::nodeCreated, this, &FlowScene::slotOnNodeCreate);
  QObject::connect(&_model, &QtNodes::DataFlowGraphModel::nodeDeleted, this, &FlowScene::slotOnNodeDelete);
  QObject::connect(this, &QtNodes::DataFlowGraphicsScene::nodeDoubleClicked, this, &FlowScene::slotOnNodeDoubleClicked);
  QObject::connect(this, &QtNodes::DataFlowGraphicsScene::nodeContextMenu, this, &FlowScene::slotNodeContextMenu);
}

FlowScene::~FlowScene() = default;

void FlowScene::slotOnNodeDoubleClicked(QtNodes::NodeId _node_id)
{
  LOG_DEBUG("double clicked, node id : {}", _node_id);
  // 双击事件现在由DisplaySwitcherWidget处理，这里不再显示弹窗
  // 可以发出信号通知外部处理双击事件
  emit nodeDoubleClicked(_node_id);
}

void FlowScene::slotOnNodeCreate(const QtNodes::NodeId _node_id)
{
  LOG_DEBUG("创建节点, node id : {}", _node_id);
  auto& model_ptr        = graphModel();
  auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
  if (data_graph_model == nullptr)
  {
    LOG_ERROR("Failed to cast to DataFlowGraphModel");
    return;
  }
  BtNodeModel* node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
  if (node_model == nullptr)
  {
    LOG_ERROR("Failed to cast to BtNodeModel");
    return;
  }
  // node_model->setInstanceName(QString::number(_node_id)); TODO: node id映射表格
  register_node_id_map_[node_model->registrationName()] = _node_id;
  LOG_DEBUG("slotOnNodeCreate, node id : {}, name : {}", _node_id, node_model->registrationName());

  // 转发节点创建信号
  emit nodeCreated(_node_id);
}

void FlowScene::slotOnNodeDelete(const QtNodes::NodeId _node_id)
{
  LOG_DEBUG("删除节点, node id : {}", _node_id);

  // 从映射中移除节点
  for (auto it = register_node_id_map_.begin(); it != register_node_id_map_.end(); ++it)
  {
    if (it->second == _node_id)
    {
      LOG_DEBUG("slotOnNodeDelete, node id : {}, name : {}", _node_id, it->first.toStdString());
      register_node_id_map_.erase(it);
      break;
    }
  }

  // 转发节点删除信号
  emit nodeDeleted(_node_id);
}

void FlowScene::slotNodeContextMenu(QtNodes::NodeId const _node_id, QPointF const _scene_pos)
{
  Q_UNUSED(_scene_pos);

  LOG_DEBUG("Node context menu requested for Node ID: {}", _node_id);

  if (_node_id == QtNodes::InvalidNodeId)
  {
    LOG_ERROR("Node context menu InvalidNodeId requested for Node ID: {}", _node_id);
    return;
  }

  // 创建菜单
  QMenu context_menu;
  context_menu.addSeparator();

  // 复制相关菜单项
  QAction* copy_action = context_menu.addAction("复制节点");
  copy_action->setShortcut(QKeySequence::Copy);
  QAction* duplicate_action = context_menu.addAction("重复节点");
  duplicate_action->setShortcut(QKeySequence(Qt::CTRL + Qt::Key_D));

  context_menu.addSeparator();
  QAction* delete_action = context_menu.addAction("删除节点");
  context_menu.addSeparator();
  QAction* reset_state_action = context_menu.addAction("重置状态");
  context_menu.addSeparator();
  QAction* properties_action = context_menu.addAction("属性...");

  // 复制节点 - 使用 QtNodes 内置功能
  connect(copy_action, &QAction::triggered, this, [this, _node_id]() {
    LOG_DEBUG("Copy node requested for Node ID: {}", _node_id);
    // 选中节点然后触发复制
    clearSelection();
    auto* node_graphics = nodeGraphicsObject(_node_id);
    if (node_graphics)
    {
      node_graphics->setSelected(true);
      // 触发 GraphicsView 的复制功能
      if (auto* view = views().first())
      {
        if (auto* graphics_view = qobject_cast<QtNodes::GraphicsView*>(view))
        {
          graphics_view->onCopySelectedObjects();
        }
      }
    }
  });

  // 重复节点 - 使用 QtNodes 内置功能
  connect(duplicate_action, &QAction::triggered, this, [this, _node_id]() {
    LOG_DEBUG("Duplicate node requested for Node ID: {}", _node_id);
    // 选中节点然后触发重复
    clearSelection();
    auto* node_graphics = nodeGraphicsObject(_node_id);
    if (node_graphics)
    {
      node_graphics->setSelected(true);
      // 触发 GraphicsView 的重复功能
      if (auto* view = views().first())
      {
        if (auto* graphics_view = qobject_cast<QtNodes::GraphicsView*>(view))
        {
          graphics_view->onDuplicateSelectedObjects();
        }
      }
    }
  });

  // 删除
  connect(delete_action, &QAction::triggered, this, [this, _node_id]() {
    auto& model_ptr = graphModel();  // 直接获取 DataFlowGraphModel 引用
    LOG_DEBUG("Node remove requested, node id : {}", _node_id);
    model_ptr.deleteNode(_node_id);  // 删除节点，这会触发 nodeDeleted 信号
  });

  // 重置状态
  connect(reset_state_action, &QAction::triggered, this, [this, _node_id]() {
    auto& model_ptr        = graphModel();
    auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
    if (data_graph_model == nullptr)
    {
      LOG_ERROR("Failed to cast to DataFlowGraphModel");
      return;
    }
    auto* node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
    node_model->updateNodeInfo(BT::Duration(), "", BT::NodeStatus::IDLE, BT::NodeStatus::IDLE);
  });

  // 属性
  connect(properties_action, &QAction::triggered, this,
          [_node_id]() { LOG_DEBUG("Properties requested for Node ID: {}", _node_id); });

  // 使用全局鼠标光标位置
  context_menu.exec(QCursor::pos());
}

void FlowScene::slotNodeStatusChanged(BT::Duration _timestamp,
                                      const std::string& _name,
                                      BT::NodeStatus _prev_status,
                                      BT::NodeStatus _status,
                                      const QVariantMap& _output_ports)
{
  // 检查 UID 是否在我们的映射中
  auto id_iter = register_node_id_map_.find(QString::fromStdString(_name));
  if (id_iter != register_node_id_map_.end())
  {
    auto node_id = id_iter->second;

    auto& model_ptr = graphModel();
    if (model_ptr.nodeExists(node_id))
    {
      auto* data_graph_model = dynamic_cast<QtNodes::DataFlowGraphModel*>(&model_ptr);
      if (data_graph_model == nullptr)
      {
        LOG_ERROR("Failed to cast to DataFlowGraphModel");
        return;
      }
      auto* node_model = data_graph_model->delegateModel<BtNodeModel>(node_id);
      if (node_model == nullptr)
      {
        LOG_ERROR("Failed to cast to BtNodeModel");
        return;
      }
      if (BT::NodeStatus::IDLE != _status)
      {
        node_model->updateNodeInfo(_timestamp, _name, _prev_status, _status, _output_ports);
      }
    }
    else
    {
      LOG_DEBUG("Node ID {} no longer exists in the graph model.", node_id);
      // 可以考虑从 map 中移除无效的 ID
      register_node_id_map_.erase(id_iter);
    }
  }
  else
  {
    // 如果 UID 未找到，可能是不需要可视化的节点或映射错误
    LOG_DEBUG("BT UID {} not fount in the mapping", _name);
  }
}

FlowView::FlowView(QWidget* _parent) : QtNodes::GraphicsView(_parent) { setAcceptDrops(true); }

FlowView::~FlowView() = default;

bool FlowView::canAcceptMimeData(const QMimeData* _mime_data)
{
  // Check if the MIME data format is the one we defined in NodeTreeWidget
  return _mime_data->hasFormat(G_MIME_TYPE);
}

void FlowView::dragEnterEvent(QDragEnterEvent* _event)
{
  if (canAcceptMimeData(_event->mimeData()))
  {
    // Accept the drag if the format is correct
    _event->acceptProposedAction();
  }
  else
  {
    // Ignore if the format is not correct
    _event->ignore();
  }
}

void FlowView::dragMoveEvent(QDragMoveEvent* _event)
{
  if (canAcceptMimeData(_event->mimeData()))
  {
    // Accept the move if the format is correct
    _event->acceptProposedAction();
  }
  else
  {
    // Ignore if the format is not correct
    _event->ignore();
  }
}

void FlowView::dropEvent(QDropEvent* _event)
{
  if (!canAcceptMimeData(_event->mimeData()))
  {
    _event->ignore();
    return;
  }

  _event->acceptProposedAction();

  auto encoded_data = _event->mimeData()->data(G_MIME_TYPE);
  QDataStream stream(&encoded_data, QIODevice::ReadOnly);
  QString registered_model_name;
  QString category;
  stream >> registered_model_name >> category;

  if (registered_model_name.isEmpty())
  {
    LOG_DEBUG("FlowView::dropEvent: Drop event: Empty registered model name");
    _event->ignore();
    return;
  }

  auto* scene = dynamic_cast<FlowScene*>(this->scene());
  if (scene == nullptr)
  {
    LOG_ERROR("FlowView::dropEvent: scene is nullptr");
    return;
  }

  QPointF pos = mapToScene(mapFromGlobal(QCursor::pos()));
  auto& model = scene->graphModel();

  QtNodes::NodeId const NEW_ID = model.addNode(registered_model_name);
  model.setNodeData(NEW_ID, QtNodes::NodeRole::Position, pos);
}

void FlowView::mouseReleaseEvent(QMouseEvent* _event)
{
  QtNodes::GraphicsView::mouseReleaseEvent(_event);

  auto* scene = dynamic_cast<FlowScene*>(this->scene());
  if (scene == nullptr)
  {
    LOG_ERROR("FlowView::mouseReleaseEvent: scene is nullptr");
    return;
  }

  auto& model = scene->graphModel();

  // 遍历所有节点，更新它们的位置
  for (QtNodes::NodeId node_id : model.allNodeIds())
  {
    auto new_pos = scene->nodeGraphicsObject(node_id)->pos();
    // LOG_WARN("mouseReleaseEvent : node_id : {}, pos : ({}, {})", node_id, new_pos.x(), new_pos.y());
    model.setNodeData(node_id, QtNodes::NodeRole::Position, new_pos);
  }
}

TestFlowEditor::TestFlowEditor(QWidget* _parent) : QWidget(_parent)
{
  setFlowView();

  // 设置窗口标题
  setWindowTitle("Test Flow Editor");

  // 连接节点选择事件
  if (ptr_)
  {
    auto& scene = ptr_->get<FlowScene>();
    QObject::connect(&scene, &QtNodes::BasicGraphicsScene::nodeSelected, this, &TestFlowEditor::nodeSelected);
  }
}

TestFlowEditor::~TestFlowEditor() = default;

std::shared_ptr<QtNodes::NodeDelegateModelRegistry> TestFlowEditor::registerDataModels()
{
  auto registry = NodeManager::getInstance().qtNodeRegistry();

  auto node_creator = [&](const QString& _id, std::shared_ptr<NodeModel> _model) {
    QString category = QString::fromStdString(BT::toStr(_model->type));
    if (_id == "开始")
    {
      category = "根节点";
    }
    auto factory = [_model]() {
      NodeModel* model_ptr = _model.get();
      return std::make_unique<BtNodeModel>(model_ptr);
    };
    registry->registerModel<BtNodeModel>(std::move(factory), category);
  };

  for (const auto& model : BuiltinNodeModels())
  {
    auto model_ptr = std::make_shared<NodeModel>();
    // 复制必要的属性
    model_ptr->type            = model.second->type;
    model_ptr->registration_id = model.second->registration_id;
    model_ptr->instance_name   = model.second->instance_name;
    model_ptr->display_name    = model.second->display_name;
    model_ptr->description     = model.second->description;
    // 复制端口信息
    model_ptr->ports = model.second->ports;
    // 共享PropertyModel
    model_ptr->property_model = model.second->property_model;

    tree_node_models_[model.first] = model_ptr;
  }

  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::CONTROL;
    model_ptr->registration_id    = "Sequence";
    model_ptr->instance_name      = "Sequence";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Sequence"] = model_ptr;
  }
  {
    auto model_ptr                     = std::make_shared<NodeModel>();
    model_ptr->type                    = BT::NodeType::CONTROL;
    model_ptr->registration_id         = "SequenceCheck";
    model_ptr->instance_name           = "SequenceCheck";
    model_ptr->ports                   = PortModels {};
    tree_node_models_["SequenceCheck"] = model_ptr;
  }
  {
    auto model_ptr                     = std::make_shared<NodeModel>();
    model_ptr->type                    = BT::NodeType::CONTROL;
    model_ptr->registration_id         = "AsyncSequence";
    model_ptr->instance_name           = "AsyncSequence";
    model_ptr->ports                   = PortModels {};
    tree_node_models_["AsyncSequence"] = model_ptr;
  }
  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::CONTROL;
    model_ptr->registration_id    = "Fallback";
    model_ptr->instance_name      = "Fallback";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Fallback"] = model_ptr;
  }
  {
    auto model_ptr                     = std::make_shared<NodeModel>();
    model_ptr->type                    = BT::NodeType::CONTROL;
    model_ptr->registration_id         = "AsyncFallback";
    model_ptr->instance_name           = "AsyncFallback";
    model_ptr->ports                   = PortModels {};
    tree_node_models_["AsyncFallback"] = model_ptr;
  }

  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::CONTROL;
    model_ptr->registration_id    = "Parallel";
    model_ptr->instance_name      = "Parallel";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Parallel"] = model_ptr;
  }
  {
    auto model_ptr                   = std::make_shared<NodeModel>();
    model_ptr->type                  = BT::NodeType::CONTROL;
    model_ptr->registration_id       = "ParallelAll";
    model_ptr->instance_name         = "ParallelAll";
    model_ptr->ports                 = PortModels {};
    tree_node_models_["ParallelAll"] = model_ptr;
  }
  {
    auto model_ptr                        = std::make_shared<NodeModel>();
    model_ptr->type                       = BT::NodeType::CONTROL;
    model_ptr->registration_id            = "ReactiveSequence";
    model_ptr->instance_name              = "ReactiveSequence";
    model_ptr->ports                      = PortModels {};
    tree_node_models_["ReactiveSequence"] = model_ptr;
  }

  {
    auto model_ptr                  = std::make_shared<NodeModel>();
    model_ptr->type                 = BT::NodeType::CONTROL;
    model_ptr->registration_id      = "IfThenElse";
    model_ptr->instance_name        = "IfThenElse";
    model_ptr->ports                = PortModels {};
    tree_node_models_["IfThenElse"] = model_ptr;
  }
  {
    auto model_ptr                   = std::make_shared<NodeModel>();
    model_ptr->type                  = BT::NodeType::CONTROL;
    model_ptr->registration_id       = "WhileDoElse";
    model_ptr->instance_name         = "WhileDoElse";
    model_ptr->ports                 = PortModels {};
    tree_node_models_["WhileDoElse"] = model_ptr;
  }

  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "Sleep";
    model_ptr->instance_name   = "Sleep";
    PortModels ports;
    ports["延时"]              = { "msec", BT::PortDirection::INPUT, "延时", "1000" };
    model_ptr->ports           = std::move(ports);
    tree_node_models_["Sleep"] = model_ptr;
  }

  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "LuaScriptNode";
    model_ptr->instance_name   = "LuaScriptNode";
    PortModels ports;
    PortModel script_port;
    script_port.type_name     = "script";
    script_port.direction     = BT::PortDirection::INPUT;
    script_port.description   = "要执行的Lua脚本代码";
    script_port.description   = "要执行的Lua脚本文件路径:\n"
                                "-- 读取黑板数据\n"
                                "local counter = bb_get_int('counter') or 0\n"
                                "local message = bb_get('message') or 'default'\n\n"
                                "-- 修改黑板数据\n"
                                "bb_set_int('counter', counter + 1)\n"
                                "bb_set('status', 'processed')\n"
                                "bb_set_bool('completed', true)\n\n"
                                "-- 记录日志\n"
                                "log('Counter updated to: ' .. (counter + 1), 'info')\n\n"
                                "return OK\n";
    script_port.default_value = "-- 读取黑板数据\n"
                                "local counter = bb_get_int('counter') or 0\n"
                                "local message = bb_get('message') or 'default'\n"
                                "\n"
                                "-- 修改黑板数据\n"
                                "bb_set_int('counter', counter + 1)\n"
                                "bb_set('status', 'processed')\n"
                                "bb_set_bool('completed', true)\n"
                                "\n"
                                "-- 记录日志\n"
                                "log('Counter updated to: ' .. (counter + 1), 'info')\n"
                                "\n"
                                "return OK\n";
    script_port.data_type     = PortDataType::String;
    script_port.height_factor = 10;  // 大的多行编辑区域
    ports["Lua输入脚本"]      = std::move(script_port);

    PortModel script_file_port;
    script_file_port.type_name   = "script_file";
    script_file_port.direction   = BT::PortDirection::INPUT;
    script_file_port.data_type   = PortDataType::FilePath;
    script_file_port.description = "要执行的Lua脚本文件路径";
    ports["Lua输入脚本文件"]     = std::move(script_file_port);

    PortModel result_port;
    result_port.type_name   = "result";
    result_port.direction   = BT::PortDirection::OUTPUT;
    result_port.description = "脚本执行结果";
    result_port.data_type   = PortDataType::String;
    ports["执行结果"]       = std::move(result_port);

    PortModel error_message_port;
    error_message_port.type_name   = "error_message";
    error_message_port.direction   = BT::PortDirection::OUTPUT;
    error_message_port.description = "错误信息";
    error_message_port.data_type   = PortDataType::String;
    ports["错误信息"]              = std::move(error_message_port);

    model_ptr->ports                   = std::move(ports);
    tree_node_models_["LuaScriptNode"] = model_ptr;
  }
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "UdpStartNode";
    model_ptr->instance_name   = "UdpStartNode";
    PortModels ports;

    // 输入端口 - UDP配置
    PortModel udp_ip_port;
    udp_ip_port.type_name     = "udp_ip";
    udp_ip_port.direction     = BT::PortDirection::INPUT;
    udp_ip_port.description   = "UDP监听IP地址";
    udp_ip_port.default_value = "0.0.0.0";
    udp_ip_port.data_type     = PortDataType::String;
    ports["UDP IP地址"]       = std::move(udp_ip_port);

    PortModel udp_port_port;
    udp_port_port.type_name     = "udp_port";
    udp_port_port.direction     = BT::PortDirection::INPUT;
    udp_port_port.description   = "UDP监听端口";
    udp_port_port.default_value = "6699";
    udp_port_port.data_type     = PortDataType::String;
    ports["UDP端口"]            = std::move(udp_port_port);

    // 数据包长度端口
    PortModel packet_length_port;
    packet_length_port.type_name     = "packet_length";
    packet_length_port.direction     = BT::PortDirection::INPUT;
    packet_length_port.description   = "期望的UDP数据包长度（字节）";
    packet_length_port.default_value = "1024";
    packet_length_port.data_type     = PortDataType::Integer;
    packet_length_port.min_value     = 1;
    packet_length_port.max_value     = 65535;
    ports.insert({ "数据包长度", std::move(packet_length_port) });

    PortModel timeout_ms_port;
    timeout_ms_port.type_name     = "timeout_ms";
    timeout_ms_port.direction     = BT::PortDirection::INPUT;
    timeout_ms_port.description   = "接收超时时间(毫秒)";
    timeout_ms_port.default_value = "1000";
    timeout_ms_port.data_type     = PortDataType::String;
    ports["超时时间"]             = std::move(timeout_ms_port);

    PortModel group_ip_port;
    group_ip_port.type_name     = "group_ip";
    group_ip_port.direction     = BT::PortDirection::INPUT;
    group_ip_port.description   = "组播IP地址(可选)";
    group_ip_port.default_value = "";
    group_ip_port.data_type     = PortDataType::String;
    ports["组播IP"]             = std::move(group_ip_port);

    PortModel receiver_id_port;
    receiver_id_port.type_name     = "receiver_id";
    receiver_id_port.direction     = BT::PortDirection::INPUT;
    receiver_id_port.description   = "接收数据ID";
    receiver_id_port.default_value = "";
    receiver_id_port.data_type     = PortDataType::String;
    ports["接收数据ID"]            = std::move(receiver_id_port);

    // // 输入端口 - 脚本配置
    // PortModel script_port;
    // script_port.type_name     = "parser_script";
    // script_port.direction     = BT::PortDirection::INPUT;
    // script_port.description   = "Lua解析脚本:\n"
    //                             "-- UDP数据解析脚本（优化版本）\n"
    //                             "log('=== UDP数据解析测试 ===', 'info')\n"
    //                             "log('接收到数据长度: ' .. data_length .. ' 字节', 'info')\n"
    //                             "log('数据十六进制: ' .. data_hex, 'info')\n"
    //                             "\n"
    //                             "-- 存储到黑板（会自动添加节点名前缀）\n"
    //                             "bb_set('udp_data_received', 'true', 'string')\n"
    //                             "bb_set_int('udp_data_length', data_length)\n"
    //                             "bb_set('udp_data_hex', data_hex, 'string')\n"
    //                             "\n"
    //                             "-- 清空自定义输出\n"
    //                             "clear_output()\n"
    //                             "\n"
    //                             "-- 解析数据包头部（使用新的解析辅助函数）\n"
    //                             "if data_length >= 4 then\n"
    //                             "    local header = 0\n"
    //                             "    for i = 0, 3 do\n"
    //                             "        header = header * 256 + read_u8(i)\n"
    //                             "    end\n"
    //                             "    \n"
    //                             "    bb_set_int('packet_header', header)\n"
    //                             "    set_output('header', string.format('0x%08X', header))\n"
    //                             "    log('数据包头部: 0x' .. string.format('%08X', header), 'info')\n"
    //                             "    \n"
    //                             "    if header == 0x12345678 then\n"
    //                             "        bb_set('packet_valid', 'true', 'string')\n"
    //                             "        set_output('valid', 'true')\n"
    //                             "        set_output('status', 'VALID_PACKET')\n"
    //                             "        log('✅ 有效的数据包', 'info')\n"
    //                             "    else\n"
    //                             "        bb_set('packet_valid', 'false', 'string')\n"
    //                             "        set_output('valid', 'false')\n"
    //                             "        set_output('status', 'INVALID_PACKET')\n"
    //                             "        log('❌ 无效的数据包', 'warn')\n"
    //                             "    end\n"
    //                             "    \n"
    //                             "    -- 添加更多解析信息\n"
    //                             "    set_output('magic', string.format('0x%02X', read_u8(0)))\n"
    //                             "    set_output('version', tostring(read_u8(1)))\n"
    //                             "    if data_length >= 8 then\n"
    //                             "        set_output('payload', slice_hex(4, 4))\n"
    //                             "    end\n"
    //                             "else\n"
    //                             "    bb_set('packet_valid', 'false', 'string')\n"
    //                             "    set_output('valid', 'false')\n"
    //                             "    set_output('status', 'PACKET_TOO_SHORT')\n"
    //                             "    log('❌ 数据包太短', 'error')\n"
    //                             "end\n"
    //                             "\n"
    //                             "-- 添加通用输出信息\n"
    //                             "set_output('length', tostring(data_length))\n"
    //                             "set_output('timestamp', os.date('%Y-%m-%d %H:%M:%S'))\n"
    //                             "\n"
    //                             "log('=== UDP数据解析完成 ===', 'info')\n"
    //                             "return OK";
    // script_port.default_value = "-- UDP数据解析脚本（优化版本）\n"
    //                             "log('=== UDP数据解析测试 ===', 'info')\n"
    //                             "log('接收到数据长度: ' .. data_length .. ' 字节', 'info')\n"
    //                             "log('数据十六进制: ' .. data_hex, 'info')\n"
    //                             "\n"
    //                             "-- 存储到黑板（会自动添加节点名前缀）\n"
    //                             "bb_set('udp_data_received', 'true', 'string')\n"
    //                             "bb_set_int('udp_data_length', data_length)\n"
    //                             "bb_set('udp_data_hex', data_hex, 'string')\n"
    //                             "\n"
    //                             "-- 清空自定义输出\n"
    //                             "clear_output()\n"
    //                             "\n"
    //                             "-- 解析数据包头部（使用新的解析辅助函数）\n"
    //                             "if data_length >= 4 then\n"
    //                             "    local header = 0\n"
    //                             "    for i = 0, 3 do\n"
    //                             "        header = header * 256 + read_u8(i)\n"
    //                             "    end\n"
    //                             "    \n"
    //                             "    bb_set_int('packet_header', header)\n"
    //                             "    set_output('header', string.format('0x%08X', header))\n"
    //                             "    log('数据包头部: 0x' .. string.format('%08X', header), 'info')\n"
    //                             "    \n"
    //                             "    if header == 0x12345678 then\n"
    //                             "        bb_set('packet_valid', 'true', 'string')\n"
    //                             "        set_output('valid', 'true')\n"
    //                             "        set_output('status', 'VALID_PACKET')\n"
    //                             "        log('✅ 有效的数据包', 'info')\n"
    //                             "    else\n"
    //                             "        bb_set('packet_valid', 'false', 'string')\n"
    //                             "        set_output('valid', 'false')\n"
    //                             "        set_output('status', 'INVALID_PACKET')\n"
    //                             "        log('❌ 无效的数据包', 'warn')\n"
    //                             "    end\n"
    //                             "    \n"
    //                             "    -- 添加更多解析信息\n"
    //                             "    set_output('magic', string.format('0x%02X', read_u8(0)))\n"
    //                             "    set_output('version', tostring(read_u8(1)))\n"
    //                             "    if data_length >= 8 then\n"
    //                             "        set_output('payload', slice_hex(4, 4))\n"
    //                             "    end\n"
    //                             "else\n"
    //                             "    bb_set('packet_valid', 'false', 'string')\n"
    //                             "    set_output('valid', 'false')\n"
    //                             "    set_output('status', 'PACKET_TOO_SHORT')\n"
    //                             "    log('❌ 数据包太短', 'error')\n"
    //                             "end\n"
    //                             "\n"
    //                             "-- 添加通用输出信息\n"
    //                             "set_output('length', tostring(data_length))\n"
    //                             "set_output('timestamp', os.date('%Y-%m-%d %H:%M:%S'))\n"
    //                             "\n"
    //                             "log('=== UDP数据解析完成 ===', 'info')\n"
    //                             "return OK";
    // script_port.data_type     = PortDataType::String;
    // script_port.height_factor = 10;  // 大的多行编辑区域
    // ports["Lua解析脚本"]      = std::move(script_port);

    // PortModel script_file_port;
    // script_file_port.type_name     = "parser_script_file";
    // script_file_port.direction     = BT::PortDirection::INPUT;
    // script_file_port.description   = "Lua解析脚本文件路径";
    // script_file_port.default_value = "";
    // script_file_port.data_type     = PortDataType::FilePath;
    // ports["脚本文件路径"]          = std::move(script_file_port);

    // 输出端口
    PortModel parse_result_port;
    parse_result_port.type_name   = "result";
    parse_result_port.direction   = BT::PortDirection::OUTPUT;
    parse_result_port.description = "UDP启动结果";
    parse_result_port.data_type   = PortDataType::String;
    ports["UDP启动结果"]          = std::move(parse_result_port);

    PortModel error_message_port;
    error_message_port.type_name   = "error_message";
    error_message_port.direction   = BT::PortDirection::OUTPUT;
    error_message_port.description = "错误信息";
    error_message_port.data_type   = PortDataType::String;
    ports["错误信息"]              = std::move(error_message_port);

    // PortModel data_length_port;
    // data_length_port.type_name   = "data_length";
    // data_length_port.direction   = BT::PortDirection::OUTPUT;
    // data_length_port.description = "接收到的数据长度";
    // data_length_port.data_type   = PortDataType::Integer;
    // ports["数据长度"]            = std::move(data_length_port);

    // PortModel data_hex_port;
    // data_hex_port.type_name   = "data_hex";
    // data_hex_port.direction   = BT::PortDirection::OUTPUT;
    // data_hex_port.description = "数据的十六进制表示";
    // data_hex_port.data_type   = PortDataType::String;
    // ports["数据十六进制"]     = std::move(data_hex_port);

    PortModel udp_started_port;
    udp_started_port.type_name     = "udp_started";
    udp_started_port.direction     = BT::PortDirection::OUTPUT;
    udp_started_port.description   = "UDP是否已启动";
    udp_started_port.data_type     = PortDataType::Boolean;
    udp_started_port.default_value = "false";
    ports["UDP是否已启动"]         = std::move(udp_started_port);

    PortModel udp_handle_port;
    udp_handle_port.type_name     = "udp_handle";
    udp_handle_port.direction     = BT::PortDirection::OUTPUT;
    udp_handle_port.description   = "UDP接收器句柄";
    udp_handle_port.default_value = "{udp_handle}";
    udp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "UDP接收器句柄", std::move(udp_handle_port) });

    model_ptr->ports                  = std::move(ports);
    tree_node_models_["UdpStartNode"] = model_ptr;
  }

  // UDP数据采集节点配置
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "UdpCollectNode";
    model_ptr->instance_name   = "UdpCollectNode";
    model_ptr->display_name    = "UDP数据采集";
    model_ptr->description     = "从UDP接收器采集和解析数据";
    PortModels ports;

    // UDP句柄输入端口
    PortModel udp_handle_port;
    udp_handle_port.type_name     = "udp_handle";
    udp_handle_port.direction     = BT::PortDirection::INPUT;
    udp_handle_port.description   = "UDP接收器句柄，从UdpStartNode获取";
    udp_handle_port.default_value = "{udp_handle}";
    udp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "UDP句柄", std::move(udp_handle_port) });

    // 数据包长度端口
    PortModel packet_length_port;
    packet_length_port.type_name     = "packet_length";
    packet_length_port.direction     = BT::PortDirection::INPUT;
    packet_length_port.description   = "期望的UDP数据包长度（字节）";
    packet_length_port.default_value = "1024";
    packet_length_port.data_type     = PortDataType::Integer;
    packet_length_port.min_value     = 1;
    packet_length_port.max_value     = 65535;
    ports.insert({ "数据包长度", std::move(packet_length_port) });

    // 超时时间端口
    PortModel timeout_port;
    timeout_port.type_name     = "timeout_ms";
    timeout_port.direction     = BT::PortDirection::INPUT;
    timeout_port.description   = "数据接收超时时间（毫秒）";
    timeout_port.default_value = "2000";
    timeout_port.data_type     = PortDataType::Integer;
    timeout_port.min_value     = 100;
    timeout_port.max_value     = 30000;
    ports.insert({ "超时时间", std::move(timeout_port) });

    // 接收器ID端口
    PortModel receiver_id_port;
    receiver_id_port.type_name     = "receiver_id";
    receiver_id_port.direction     = BT::PortDirection::INPUT;
    receiver_id_port.description   = "接收器唯一标识，用于数据共享";
    receiver_id_port.default_value = "default_receiver";
    receiver_id_port.data_type     = PortDataType::String;
    ports.insert({ "接收器ID", std::move(receiver_id_port) });

    // Lua解析脚本端口
    PortModel parser_script_port;
    parser_script_port.type_name = "parser_script";
    parser_script_port.direction = BT::PortDirection::INPUT;
    parser_script_port.description =
      "Lua数据解析脚本\n可用变量：\n- data_bytes: 字节数组\n- data_hex: 十六进制字符串\n- data_length: "
      "数据长度\n\n可用函数：\n- log(message, level): 记录日志\n- bb_set(key, value, type): 设置黑板值\n- bb_get(key): "
      "获取黑板值\n- set_output(key, value): 设置输出数据";
    parser_script_port.default_value =
      "-- UDP数据解析脚本\nlog('接收到UDP数据，长度: ' .. data_length, 'info')\n\n-- "
      "解析数据并存储到黑板\nbb_set('data_length', tostring(data_length), 'string')\nbb_set('data_hex', data_hex, "
      "'string')\n\n-- 设置输出数据\nset_output('parsed_length', tostring(data_length))\nset_output('parsed_hex', "
      "data_hex)\n\nreturn 'SUCCESS'";
    parser_script_port.data_type     = PortDataType::String;
    parser_script_port.height_factor = 8;  // 设置为8倍高度，方便编辑Lua脚本
    ports.insert({ "解析脚本", std::move(parser_script_port) });

    // 黑板前缀端口
    PortModel bb_prefix_port;
    bb_prefix_port.type_name     = "bb_prefix";
    bb_prefix_port.direction     = BT::PortDirection::INPUT;
    bb_prefix_port.description   = "黑板键前缀，用于避免键名冲突";
    bb_prefix_port.default_value = "udp";
    bb_prefix_port.data_type     = PortDataType::String;
    ports.insert({ "黑板前缀", std::move(bb_prefix_port) });

    // 输出端口
    PortModel result_port;
    result_port.type_name     = "result";
    result_port.direction     = BT::PortDirection::OUTPUT;
    result_port.description   = "解析结果状态";
    result_port.default_value = "{result}";
    result_port.data_type     = PortDataType::String;
    ports.insert({ "解析结果", std::move(result_port) });

    PortModel error_message_port;
    error_message_port.type_name     = "error_message";
    error_message_port.direction     = BT::PortDirection::OUTPUT;
    error_message_port.description   = "错误信息";
    error_message_port.default_value = "{error_message}";
    error_message_port.data_type     = PortDataType::String;
    ports.insert({ "错误信息", std::move(error_message_port) });

    PortModel parsed_data_port;
    parsed_data_port.type_name     = "parsed_data";
    parsed_data_port.direction     = BT::PortDirection::OUTPUT;
    parsed_data_port.description   = "解析后的JSON数据";
    parsed_data_port.default_value = "{parsed_data}";
    parsed_data_port.data_type     = PortDataType::String;
    ports.insert({ "解析数据", std::move(parsed_data_port) });

    PortModel data_length_port;
    data_length_port.type_name     = "data_length";
    data_length_port.direction     = BT::PortDirection::OUTPUT;
    data_length_port.description   = "接收到的数据长度";
    data_length_port.default_value = "{data_length}";
    data_length_port.data_type     = PortDataType::Integer;
    ports.insert({ "数据长度", std::move(data_length_port) });

    PortModel data_available_port;
    data_available_port.type_name     = "data_available";
    data_available_port.direction     = BT::PortDirection::OUTPUT;
    data_available_port.description   = "是否有数据可用";
    data_available_port.default_value = "{data_available}";
    data_available_port.data_type     = PortDataType::Boolean;
    ports.insert({ "数据可用", std::move(data_available_port) });

    model_ptr->ports                    = std::move(ports);
    tree_node_models_["UdpCollectNode"] = model_ptr;
  }

  // UDP停止节点配置
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "UdpStopNode";
    model_ptr->instance_name   = "UdpStopNode";
    model_ptr->display_name    = "UDP停止";
    model_ptr->description     = "停止UDP接收器并清理资源";
    PortModels ports;

    // UDP句柄输入端口
    PortModel udp_handle_port;
    udp_handle_port.type_name     = "udp_handle";
    udp_handle_port.direction     = BT::PortDirection::INPUT;
    udp_handle_port.description   = "要停止的UDP接收器句柄";
    udp_handle_port.default_value = "{udp_handle}";
    udp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "UDP句柄", std::move(udp_handle_port) });

    // 输出端口
    PortModel result_port;
    result_port.type_name     = "result";
    result_port.direction     = BT::PortDirection::OUTPUT;
    result_port.description   = "停止操作结果";
    result_port.default_value = "{result}";
    result_port.data_type     = PortDataType::String;
    ports.insert({ "操作结果", std::move(result_port) });

    PortModel error_message_port;
    error_message_port.type_name     = "error_message";
    error_message_port.direction     = BT::PortDirection::OUTPUT;
    error_message_port.description   = "错误信息";
    error_message_port.default_value = "{error_message}";
    error_message_port.data_type     = PortDataType::String;
    ports.insert({ "错误信息", std::move(error_message_port) });

    PortModel udp_stopped_port;
    udp_stopped_port.type_name     = "udp_stopped";
    udp_stopped_port.direction     = BT::PortDirection::OUTPUT;
    udp_stopped_port.description   = "UDP是否已成功停止";
    udp_stopped_port.default_value = "{udp_stopped}";
    udp_stopped_port.data_type     = PortDataType::Boolean;
    ports.insert({ "已停止", std::move(udp_stopped_port) });

    PortModel stopped_count_port;
    stopped_count_port.type_name     = "stopped_count";
    stopped_count_port.direction     = BT::PortDirection::OUTPUT;
    stopped_count_port.description   = "停止的接收器数量";
    stopped_count_port.default_value = "{stopped_count}";
    stopped_count_port.data_type     = PortDataType::Integer;
    ports.insert({ "停止数量", std::move(stopped_count_port) });

    model_ptr->ports                 = std::move(ports);
    tree_node_models_["UdpStopNode"] = model_ptr;
  }

  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "JsonViewerNode";
    model_ptr->instance_name   = "JsonViewerNode";
    PortModels ports;

    // 输入端口
    PortModel json_data_port;
    json_data_port.type_name     = "json_data";
    json_data_port.direction     = BT::PortDirection::INPUT;
    json_data_port.description   = "输入的JSON字符串数据，支持变量绑定如{output_json}";
    json_data_port.default_value = R"(<![CDATA[{
  "sensor_data": {
    "temperature": 25.6,
    "humidity": 68.2,
    "pressure": 1013.25,
    "voltage": 3.3
  },
  "status": {
    "power_on": true,
    "sensor_ready": true,
    "error_count": 0
  }
}]]>)";
    json_data_port.data_type     = PortDataType::String;
    json_data_port.height_factor = 5;  // 多行编辑区域
    ports["JSON数据"]            = std::move(json_data_port);

    PortModel json_file_port;
    json_file_port.type_name     = "json_file";
    json_file_port.direction     = BT::PortDirection::INPUT;
    json_file_port.description   = "JSON文件路径";
    json_file_port.default_value = "";
    json_file_port.data_type     = PortDataType::FilePath;
    ports["JSON文件"]            = std::move(json_file_port);

    // 输出端口
    PortModel result_port;
    result_port.type_name   = "result";
    result_port.direction   = BT::PortDirection::OUTPUT;
    result_port.description = "处理结果状态";
    result_port.data_type   = PortDataType::String;
    ports["处理结果"]       = std::move(result_port);

    PortModel error_message_port;
    error_message_port.type_name   = "error_message";
    error_message_port.direction   = BT::PortDirection::OUTPUT;
    error_message_port.description = "错误信息";
    error_message_port.data_type   = PortDataType::String;
    ports["错误信息"]              = std::move(error_message_port);

    PortModel json_size_port;
    json_size_port.type_name   = "json_size";
    json_size_port.direction   = BT::PortDirection::OUTPUT;
    json_size_port.description = "JSON数据大小（字节）";
    json_size_port.data_type   = PortDataType::Integer;
    ports["数据大小"]          = std::move(json_size_port);

    // 可视化相关端口
    PortModel enable_visualization_port;
    enable_visualization_port.type_name     = "enable_visualization";
    enable_visualization_port.direction     = BT::PortDirection::INPUT;
    enable_visualization_port.description   = "是否启用可视化显示";
    enable_visualization_port.default_value = "true";
    enable_visualization_port.data_type     = PortDataType::Boolean;
    ports["启用可视化"]                     = std::move(enable_visualization_port);

    PortModel visualization_mode_port;
    visualization_mode_port.type_name     = "visualization_mode";
    visualization_mode_port.direction     = BT::PortDirection::INPUT;
    visualization_mode_port.description   = "可视化模式: auto/numeric/status";
    visualization_mode_port.default_value = "auto";
    visualization_mode_port.data_type     = PortDataType::String;
    ports["可视化模式"]                   = std::move(visualization_mode_port);

    PortModel widget_type_port;
    widget_type_port.type_name     = "widget_type";
    widget_type_port.direction     = BT::PortDirection::INPUT;
    widget_type_port.description   = "可视化组件: NumericTable/NumericGauge";
    widget_type_port.default_value = "NumericTable";
    widget_type_port.data_type     = PortDataType::String;
    ports["组件类型"]              = std::move(widget_type_port);

    PortModel visualization_title_port;
    visualization_title_port.type_name     = "visualization_title";
    visualization_title_port.direction     = BT::PortDirection::INPUT;
    visualization_title_port.description   = "可视化标题";
    visualization_title_port.default_value = "JSON数据可视化";
    visualization_title_port.data_type     = PortDataType::String;
    ports["可视化标题"]                    = std::move(visualization_title_port);

    PortModel visualization_duration_port;
    visualization_duration_port.type_name     = "visualization_duration";
    visualization_duration_port.direction     = BT::PortDirection::INPUT;
    visualization_duration_port.description   = "可视化显示时长(毫秒)";
    visualization_duration_port.default_value = "5000";
    visualization_duration_port.data_type     = PortDataType::Integer;
    ports["显示时长"]                         = std::move(visualization_duration_port);

    PortModel data_path_port;
    data_path_port.type_name     = "data_path";
    data_path_port.direction     = BT::PortDirection::INPUT;
    data_path_port.description   = "JSON中数据的路径，如 'sensor_data'，空表示根级别";
    data_path_port.default_value = "sensor_data";
    data_path_port.data_type     = PortDataType::String;
    ports["数据路径"]            = std::move(data_path_port);

    PortModel min_value_port;
    min_value_port.type_name     = "min_value";
    min_value_port.direction     = BT::PortDirection::INPUT;
    min_value_port.description   = "数值范围最小值";
    min_value_port.default_value = "0.0";
    min_value_port.data_type     = PortDataType::Double;
    ports["最小值"]              = std::move(min_value_port);

    PortModel max_value_port;
    max_value_port.type_name     = "max_value";
    max_value_port.direction     = BT::PortDirection::INPUT;
    max_value_port.description   = "数值范围最大值";
    max_value_port.default_value = "100.0";
    max_value_port.data_type     = PortDataType::Double;
    ports["最大值"]              = std::move(max_value_port);

    PortModel decimal_places_port;
    decimal_places_port.type_name     = "decimal_places";
    decimal_places_port.direction     = BT::PortDirection::INPUT;
    decimal_places_port.description   = "小数位数";
    decimal_places_port.default_value = "2";
    decimal_places_port.data_type     = PortDataType::Integer;
    ports["小数位数"]                 = std::move(decimal_places_port);

    // 可视化输出端口
    PortModel visualization_shown_port;
    visualization_shown_port.type_name   = "visualization_shown";
    visualization_shown_port.direction   = BT::PortDirection::OUTPUT;
    visualization_shown_port.description = "可视化是否已显示";
    visualization_shown_port.data_type   = PortDataType::Boolean;
    ports["可视化已显示"]                = std::move(visualization_shown_port);

    PortModel visualization_data_count_port;
    visualization_data_count_port.type_name   = "visualization_data_count";
    visualization_data_count_port.direction   = BT::PortDirection::OUTPUT;
    visualization_data_count_port.description = "可视化数据项数量";
    visualization_data_count_port.data_type   = PortDataType::Integer;
    ports["数据项数量"]                       = std::move(visualization_data_count_port);

    model_ptr->ports                    = std::move(ports);
    tree_node_models_["JsonViewerNode"] = model_ptr;
  }

  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::CONDITION;
    model_ptr->registration_id = "ScriptCondition";
    model_ptr->instance_name   = "ScriptCondition";
    PortModels ports;
    ports["条件脚本"]                    = { "code", BT::PortDirection::INPUT, "条件脚本", "" };
    model_ptr->ports                     = std::move(ports);
    tree_node_models_["ScriptCondition"] = model_ptr;
  }
  {
    auto model_ptr                    = std::make_shared<NodeModel>();
    model_ptr->type                   = BT::NodeType::DECORATOR;
    model_ptr->registration_id        = "ForceSuccess";
    model_ptr->instance_name          = "ForceSuccess";
    model_ptr->ports                  = PortModels {};
    tree_node_models_["ForceSuccess"] = model_ptr;
  }

  {
    auto model_ptr                = std::make_shared<NodeModel>();
    model_ptr->type               = BT::NodeType::DECORATOR;
    model_ptr->registration_id    = "Inverter";
    model_ptr->instance_name      = "Inverter";
    model_ptr->ports              = PortModels {};
    tree_node_models_["Inverter"] = model_ptr;
  }
  {
    auto model_ptr                            = std::make_shared<NodeModel>();
    model_ptr->type                           = BT::NodeType::DECORATOR;
    model_ptr->registration_id                = "RetryUntilSuccessful";
    model_ptr->instance_name                  = "RetryUntilSuccessful";
    model_ptr->ports                          = PortModels {};
    tree_node_models_["RetryUntilSuccessful"] = model_ptr;
  }
  {
    auto model_ptr                               = std::make_shared<NodeModel>();
    model_ptr->type                              = BT::NodeType::DECORATOR;
    model_ptr->registration_id                   = "KeepRunningUntilFailure";
    model_ptr->instance_name                     = "KeepRunningUntilFailure";
    model_ptr->ports                             = PortModels {};
    tree_node_models_["KeepRunningUntilFailure"] = model_ptr;
  }
  {
    auto model_ptr              = std::make_shared<NodeModel>();
    model_ptr->type             = BT::NodeType::DECORATOR;
    model_ptr->registration_id  = "Repeat";
    model_ptr->instance_name    = "Repeat";
    model_ptr->ports            = PortModels {};
    tree_node_models_["Repeat"] = model_ptr;
  }
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "Timeout";
    model_ptr->instance_name   = "Timeout";
    PortModels ports;
    ports["延时"]                = { "msec", BT::PortDirection::INPUT, "延时", "1000" };
    model_ptr->ports             = std::move(ports);
    tree_node_models_["Timeout"] = model_ptr;
  }
  {
    auto model_ptr             = std::make_shared<NodeModel>();
    model_ptr->type            = BT::NodeType::ACTION;
    model_ptr->registration_id = "Delay";
    model_ptr->instance_name   = "Delay";
    PortModels ports;
    ports["延时"]              = { "msec", BT::PortDirection::INPUT, "延时", "1000" };
    model_ptr->ports           = std::move(ports);
    tree_node_models_["Delay"] = model_ptr;
  }
  {
    auto model_ptr               = std::make_shared<NodeModel>();
    model_ptr->type              = BT::NodeType::DECORATOR;
    model_ptr->registration_id   = "RunOnce";
    model_ptr->instance_name     = "RunOnce";
    model_ptr->ports             = PortModels {};
    tree_node_models_["RunOnce"] = model_ptr;
  }
  {
    auto model_ptr                    = std::make_shared<NodeModel>();
    model_ptr->type                   = BT::NodeType::DECORATOR;
    model_ptr->registration_id        = "ForceSuccess";
    model_ptr->instance_name          = "ForceSuccess";
    model_ptr->ports                  = PortModels {};
    tree_node_models_["ForceSuccess"] = model_ptr;
  }
  {
    auto model_ptr                    = std::make_shared<NodeModel>();
    model_ptr->type                   = BT::NodeType::DECORATOR;
    model_ptr->registration_id        = "ForceFailure";
    model_ptr->instance_name          = "ForceFailure";
    model_ptr->ports                  = PortModels {};
    tree_node_models_["ForceFailure"] = model_ptr;
  }

  for (auto& model : tree_node_models_)
  {
    QString id = QString::fromStdString(model.first);
    node_creator(id, model.second);
    LOG_INFO("加载 node model: {}", model.first);
  }

  return registry;
}

void TestFlowEditor::setFlowView()
{
  if (nullptr != ptr_)
  {
    ptr_.reset();
    this->setLayout(nullptr);
  }

  auto model = std::make_unique<QtNodes::DataFlowGraphModel>(TestFlowEditor::registerDataModels());
  auto scene = std::make_unique<FlowScene>(*model, this);
  DataSerializer::getInstance()->setDataFlow(scene.get(), model.get());
  auto flow_view             = std::make_unique<FlowView>(this);
  auto status_signal_emitter = std::make_unique<StatusSignalEmitter>();
  flow_view->setScene(scene.get());
  connect(status_signal_emitter.get(), &StatusSignalEmitter::statusChanged, scene.get(),
          &FlowScene::slotNodeStatusChanged, Qt::QueuedConnection);

  // 连接节点创建和删除信号
  connect(scene.get(), &FlowScene::nodeCreated, this, &TestFlowEditor::nodeCreated);
  connect(scene.get(), &FlowScene::nodeDeleted, this, &TestFlowEditor::nodeDeleted);

  ptr_ =
    std::make_unique<EditorPtr>(model.release(), scene.release(), flow_view.release(), status_signal_emitter.release());

  auto* layout = new QVBoxLayout(this);
  layout->setContentsMargins(0, 0, 0, 0);
  layout->setSpacing(0);
  layout->addWidget(&ptr_->get<FlowView>());
  this->setLayout(layout);
}

bool TestFlowEditor::saveToFile(const std::string& _file_path)
{
  auto res = DataSerializer::getInstance()->saveToUnifiedFile(QString::fromStdString(_file_path));

  if (!res)
  {
    LOG_ERROR("验证编辑后的节点数据保存失败，不符合规则, 请检测!");
    return false;
  }

  current_file_path_ = _file_path;

  return true;
}

bool TestFlowEditor::loadFromFile(const std::string& _file_path)
{
  return DataSerializer::getInstance()->loadFromUnifiedFile(QString::fromStdString(_file_path));
}

bool TestFlowEditor::startTest()
{
  // clear ui status
  for (const auto& node_id : ptr_->get<NodeGraphModel>().allNodeIds())
  {
    auto* node_model = ptr_->get<NodeGraphModel>().delegateModel<BtNodeModel>(node_id);
    if (node_model == nullptr)
    {
      LOG_ERROR("Failed to cast to BtNodeModel");
      continue;
    }

    node_model->updateNodeInfo(BT::Duration(), "", BT::NodeStatus::IDLE, BT::NodeStatus::IDLE);
  }

  bool res = false;
  std::thread([&]() {
    try
    {
      LOG_INFO("开始测试 ...");
      // BT::BehaviorTreeFactory factory;
      // factory.registerNodeType<SequenceWithFailureCheck>("SequenceCheck");
      // factory.registerNodeType<OpenDevice>("OpenDevice");
      // factory.registerNodeType<ReadValue>("ReadValue");
      // factory.registerNodeType<WriteValue>("WriteValue");
      // factory.registerNodeType<CloseDevice>("CloseDevice");

      auto tree = NodeManager::getInstance().btFactory()->createTree("MainTree");

      QtNodesStatusLogger logger(tree, &ptr_->get<StatusSignalEmitter>());
      logger.setEnabled(true);

      // helper function to print the tree
      BT::printTreeRecursively(tree.rootNode());

      auto json = ExportTreeToJSON(tree);
      tree.tickWhileRunning();
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("XML 解析出错: {}", std::string(e.what()));
      res = false;
      return;
    }
    catch (...)
    {
      LOG_ERROR("XML 解析时发生未知错误！");
      res = false;
      return;
    }
    LOG_INFO("测试结束");
    res = true;
  }).detach();

  return res;
}

void TestFlowEditor::nodeReorder()
{
  {
    const QSignalBlocker BLOCKER(this);
    auto abstract_tree = Utils::buildTreeFromScene(&ptr_->get<NodeGraphModel>(), QtNodes::InvalidNodeId);
    abstract_tree.debugPrint();
    Utils::nodeReorder(&ptr_->get<NodeGraphModel>(), abstract_tree);
  }
}

QtNodes::DataFlowGraphModel* TestFlowEditor::getDataFlowGraphModel()
{
  if (!ptr_)
  {
    return nullptr;
  }
  return &ptr_->get<NodeGraphModel>();
}

FlowScene* TestFlowEditor::getFlowScene()
{
  if (!ptr_)
  {
    return nullptr;
  }
  return &ptr_->get<FlowScene>();
}

FlowView* TestFlowEditor::getFlowView()
{
  if (!ptr_)
  {
    return nullptr;
  }
  return &ptr_->get<FlowView>();
}

}  // namespace robosense::lidar