﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef TEST_FLOW_EDITOR_H
#define TEST_FLOW_EDITOR_H

#include <QtNodes/DataFlowGraphicsScene>
#include <QtNodes/GraphicsView>
#include <QtNodes/NodeData>
#include <QtNodes/NodeDelegateModel>
#include <QtNodes/NodeDelegateModelRegistry>
#include <behaviortree_cpp/bt_factory.h>

#include "QtNodes/internal/Definitions.hpp"
#include "node_models/abs_behavior_tree.h"
#include "node_resp_interface.h"
#include "ptr_container.h"
#include "qt_logger.h"
#include <QDebug>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QPointF>
#include <QString>
#include <memory>
#include <unordered_map>

using NodeGraphModel = QtNodes::DataFlowGraphModel;

namespace robosense::lidar
{

class FlowScene : public QtNodes::DataFlowGraphicsScene
{
  Q_OBJECT

public:
  explicit FlowScene(QtNodes::DataFlowGraphModel& _model, QObject* _parent = nullptr);
  ~FlowScene() override;

  FlowScene(const FlowScene&)            = delete;
  FlowScene& operator=(const FlowScene&) = delete;
  FlowScene(FlowScene&&)                 = delete;
  FlowScene& operator=(FlowScene&&)      = delete;

public Q_SLOTS:
  void slotNodeStatusChanged(BT::Duration _timestamp,
                             const std::string& _name,
                             BT::NodeStatus _prev_status,
                             BT::NodeStatus _status,
                             const QVariantMap& _output_ports);

Q_SIGNALS:
  void nodeDoubleClicked(QtNodes::NodeId _node_id);
  void nodeCopyRequested(QtNodes::NodeId _node_id);
  void nodeDuplicateRequested(QtNodes::NodeId _node_id);
  void nodeCreated(QtNodes::NodeId _node_id);
  void nodeDeleted(QtNodes::NodeId _node_id);

protected Q_SLOTS:
  void slotOnNodeDoubleClicked(QtNodes::NodeId _node_id);
  void slotOnNodeCreate(const QtNodes::NodeId _node_id);
  void slotOnNodeDelete(const QtNodes::NodeId _node_id);
  void slotNodeContextMenu(QtNodes::NodeId const _node_id, QPointF const _scene_pos);

private:
  std::map<QString, QtNodes::NodeId> register_node_id_map_;
};

class FlowView : public QtNodes::GraphicsView
{
  Q_OBJECT

public:
  explicit FlowView(QWidget* _parent = nullptr);
  ~FlowView() override;

  FlowView(const FlowView&)            = delete;
  FlowView& operator=(const FlowView&) = delete;
  FlowView(FlowView&&)                 = delete;
  FlowView& operator=(FlowView&&)      = delete;

protected:
  void dragEnterEvent(QDragEnterEvent* _event) override;
  void dragMoveEvent(QDragMoveEvent* _event) override;
  void dropEvent(QDropEvent* _event) override;
  void mouseReleaseEvent(QMouseEvent* _event) override;

private:
  static bool canAcceptMimeData(const QMimeData* _mime_data);
};

using EditorPtr = comm::PtrContainer<NodeGraphModel, FlowScene, FlowView, StatusSignalEmitter>;
class TestFlowEditor : public QWidget
{
  Q_OBJECT

public:
  using TestFlowEditorPtr = std::unique_ptr<TestFlowEditor>;
  explicit TestFlowEditor(QWidget* _parent = nullptr);
  ~TestFlowEditor() override;

  TestFlowEditor(const TestFlowEditor&)            = delete;
  TestFlowEditor& operator=(const TestFlowEditor&) = delete;
  TestFlowEditor(TestFlowEditor&&)                 = delete;
  TestFlowEditor& operator=(TestFlowEditor&&)      = delete;

  void setFlowView();
  bool saveToFile(const std::string& _file_path);
  bool loadFromFile(const std::string& _file_path);
  bool startTest();
  void nodeReorder();

  // 获取DataFlowGraphModel指针
  QtNodes::DataFlowGraphModel* getDataFlowGraphModel();

  // 获取FlowScene指针
  FlowScene* getFlowScene();

  // 获取FlowView指针
  FlowView* getFlowView();

Q_SIGNALS:
  void nodeSelected(std::size_t _node_id);
  void nodeCreated(QtNodes::NodeId _node_id);
  void nodeDeleted(QtNodes::NodeId _node_id);

private:
  std::shared_ptr<QtNodes::NodeDelegateModelRegistry> registerDataModels();
  std::unique_ptr<EditorPtr> ptr_ { nullptr };
  std::unordered_map<std::string, std::shared_ptr<NodeModel>> tree_node_models_;
  std::string current_file_path_;
};

}  // namespace robosense::lidar

#endif  // TEST_FLOW_EDITOR_H
