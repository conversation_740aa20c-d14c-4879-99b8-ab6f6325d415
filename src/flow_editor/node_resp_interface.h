﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef NODE_RESP_INTERFACE_H
#define NODE_RESP_INTERFACE_H

#include <QMenu>
#include <QtCore/QObject>
#include <QtNodes/DataFlowGraphModel>
#include <QtNodes/DataFlowGraphicsScene>
#include <QtNodes/NodeDelegateModel>
#include <qobject.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

/**
 * @brief 节点响应接口
 * 
 */
class NodeRespInterface : public QObject
{
  Q_OBJECT
public:
  NodeRespInterface()                         = default;
  NodeRespInterface(const NodeRespInterface&) = delete;
  NodeRespInterface& operator=(const NodeRespInterface&) = delete;
  NodeRespInterface(NodeRespInterface&&)                 = delete;
  NodeRespInterface& operator=(NodeRespInterface&&) = delete;
  virtual ~NodeRespInterface()                      = default;

  using NODE_T = std::function<void(QtNodes::NodeGraphicsObject&)>;
  using MENU_T = std::function<void(QtNodes::NodeGraphicsObject&, const QPointF&)>;

  virtual void setNodeCreatedCallback(QtNodes::DataFlowGraphicsScene* _scene, const NODE_T& _cb)       = 0;
  virtual void setNodeDeletedCallback(QtNodes::DataFlowGraphicsScene* _scene, const NODE_T& _cb)       = 0;
  virtual void setNodeDoubleClickedCallback(QtNodes::DataFlowGraphicsScene* _scene, const NODE_T& _cb) = 0;
  virtual void setNodeContextMenuCallback(QtNodes::DataFlowGraphicsScene* _scene, const MENU_T& _cb)   = 0;
  virtual void setConfigChangedCbk(const NODE_T _cb)                                                   = 0;
  virtual void openProperties(QtNodes::NodeGraphicsObject& _node)                                      = 0;
  virtual void showContextMenu(QMenu& _menu, const QPoint& _pos)                                       = 0;
};

class NodeResponder : public NodeRespInterface
{
  Q_OBJECT
public:
  NodeResponder(QtNodes::DataFlowGraphicsScene* _scene, QtNodes::DataFlowGraphModel* model) :
    scene_(_scene), model_(model)
  {
    QObject::connect(model_, &QtNodes::DataFlowGraphModel::nodeCreated, this,
                     [this](QtNodes::NodeId _node_id) { this->onNodeCreated(_node_id); });
    QObject::connect(model_, &QtNodes::DataFlowGraphModel::nodeDeleted, this, [this](QtNodes::NodeId const _node_id) {
      LOG_INFO("nodeDeleted");
      this->onNodeDeleted(_node_id);
    });
  }
  NodeResponder(const NodeResponder&) = delete;
  NodeResponder& operator=(const NodeResponder&) = delete;
  NodeResponder(NodeResponder&&)                 = delete;
  NodeResponder& operator=(NodeResponder&&) = delete;
  ~NodeResponder()                          = default;

  void setNodeCreatedCallback(QtNodes::DataFlowGraphicsScene* _scene, const NODE_T& _cb) override
  {
    nodeCreatedCallback_ = _cb;
  }

  void setNodeDeletedCallback(QtNodes::DataFlowGraphicsScene* _scene, const NODE_T& _cb) override
  {

    nodeDeletedCallback_ = _cb;
  }

  void setNodeDoubleClickedCallback(QtNodes::DataFlowGraphicsScene* _scene, const NODE_T& _cb) override
  {
    nodeDoubleClickedCallback_ = _cb;
    QObject::connect(_scene, &QtNodes::DataFlowGraphicsScene::nodeDoubleClicked, this,
                     [this](QtNodes::NodeId const _node_id) {
                       if (nodeDoubleClickedCallback_)
                       {
                         nodeDoubleClickedCallback_(*scene_->nodeGraphicsObject(_node_id));
                       }
                     });
  }

  void setNodeContextMenuCallback(QtNodes::DataFlowGraphicsScene* _scene, const MENU_T& _cb) override
  {
    nodeContextMenuCallback_ = _cb;
    QObject::connect(_scene, &QtNodes::DataFlowGraphicsScene::nodeContextMenu, this,
                     [this](QtNodes::NodeId const _node_id, const QPointF& _scenePos) {
                       if (nodeContextMenuCallback_)
                       {
                         nodeContextMenuCallback_(*scene_->nodeGraphicsObject(_node_id), _scenePos);
                       }
                     });
  }

  void setConfigChangedCbk(const NODE_T _cb) override { configChangedCallback_ = _cb; }

  void openProperties(QtNodes::NodeGraphicsObject& node) override
  {
    // 实现打开节点属性窗口
  }

  void showContextMenu(QMenu& _menu, const QPoint& _pos) override { _menu.exec(_pos); }

private:
  void onNodeCreated(QtNodes::NodeId _node_id)
  {
    if (nodeCreatedCallback_)
    {
      auto* nodeObj = scene_->nodeGraphicsObject(_node_id);
      if (nodeObj != nullptr)
      {
        nodeCreatedCallback_(*nodeObj);
      }
    }
  }

  void onNodeDeleted(QtNodes::NodeId _node_id)
  {
    if (nodeDeletedCallback_)
    {
      auto* nodeObj = scene_->nodeGraphicsObject(_node_id);
      if (nodeObj != nullptr)
      {
        nodeDeletedCallback_(*nodeObj);
      }
    }
  }

  QtNodes::DataFlowGraphicsScene* scene_;

  QtNodes::DataFlowGraphModel* model_;
  NODE_T nodeCreatedCallback_;
  NODE_T nodeDeletedCallback_;
  NODE_T nodeDoubleClickedCallback_;
  MENU_T nodeContextMenuCallback_;
  NODE_T configChangedCallback_;
};

}  // namespace robosense::lidar

#endif  // NODE_RESP_INTERFACE_H