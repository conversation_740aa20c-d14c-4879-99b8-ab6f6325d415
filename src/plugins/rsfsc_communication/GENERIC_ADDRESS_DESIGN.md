# 通用地址格式扩展设计

## 🎯 **理解您的真实需求**

现在我明白了！您的需求是：

**当前**：只支持数值地址格式（如 `0x83c00104`）
**需求**：支持其他设备可能使用的**非数值地址格式**（如字符串地址）

这不是用符号来代替地址，而是**真正支持不同类型的地址格式**！

## 📋 **实际应用场景**

### 场景1：不同协议的设备
```bash
# 设备A：使用数值地址（当前支持）
READ:0x83c00104→temp_value

# 设备B：使用字符串地址（需要支持）
READ:"TEMP_SENSOR_01"→temp_value

# 设备C：使用路径格式地址（需要支持）
READ:"/system/sensors/temperature"→temp_value

# 设备D：使用层次化地址（需要支持）
READ:"device.channel[0].register.status"→status_value
```

### 场景2：不同通信协议
```bash
# TCP数值协议
READ:0x83c00100→status

# Modbus寄存器地址
READ:"40001"→holding_reg

# OPC UA节点地址
READ:"ns=2;s=Temperature"→opc_temp

# MQTT主题地址
READ:"sensors/room1/temperature"→mqtt_temp
```

## 🚀 **通用地址系统设计**

### 核心概念

```cpp
// 通用地址类型
class GenericAddress {
public:
    enum class Type {
        NUMERIC,        // 0x83c00100
        STRING,         // "TEMP_SENSOR_01"
        PATH,           // "/system/sensors/temp"
        HIERARCHICAL,   // "device.channel[0].status"
        CUSTOM          // 自定义格式
    };
    
    // 构造函数
    GenericAddress(uint32_t numeric_addr);
    GenericAddress(const std::string& string_addr, Type type = Type::STRING);
    
    // 访问方法
    Type getType() const;
    uint32_t getNumeric() const;
    const std::string& getString() const;
    std::string toString() const;
};
```

### 地址解析器接口

```cpp
class AddressResolver {
public:
    // 检查是否能处理此格式
    virtual bool canHandle(const std::string& addr_str) = 0;
    
    // 解析地址
    virtual bool resolve(const std::string& addr_str, GenericAddress& out_addr) = 0;
    
    // 验证地址有效性
    virtual bool isValid(const GenericAddress& addr) = 0;
    
    // 获取解析器信息
    virtual std::string getName() const = 0;
    virtual int getPriority() const = 0;
};
```

## 🔧 **具体解析器实现**

### 1. 数值地址解析器（现有兼容）
```cpp
class NumericAddressResolver : public AddressResolver {
public:
    bool canHandle(const std::string& addr_str) override {
        // 检查是否为数值格式
        return addr_str.find("0x") == 0 || std::isdigit(addr_str[0]);
    }
    
    bool resolve(const std::string& addr_str, GenericAddress& out_addr) override {
        uint32_t value;
        if (strToUint32(addr_str, value)) {
            out_addr = GenericAddress(value);
            return true;
        }
        return false;
    }
    
    int getPriority() const override { return 10; } // 最高优先级
};
```

### 2. 字符串地址解析器（新功能）
```cpp
class StringAddressResolver : public AddressResolver {
public:
    bool canHandle(const std::string& addr_str) override {
        // 检查是否为引号包围的字符串
        return addr_str.front() == '"' && addr_str.back() == '"';
    }
    
    bool resolve(const std::string& addr_str, GenericAddress& out_addr) override {
        // 去除引号
        std::string clean_str = addr_str.substr(1, addr_str.length() - 2);
        out_addr = GenericAddress(clean_str, GenericAddress::Type::STRING);
        return true;
    }
    
    int getPriority() const override { return 20; }
};
```

### 3. 路径地址解析器（新功能）
```cpp
class PathAddressResolver : public AddressResolver {
public:
    bool canHandle(const std::string& addr_str) override {
        // 检查是否为路径格式
        return addr_str.front() == '/' || addr_str.find("::") != std::string::npos;
    }
    
    bool resolve(const std::string& addr_str, GenericAddress& out_addr) override {
        out_addr = GenericAddress(addr_str, GenericAddress::Type::PATH);
        return true;
    }
    
    int getPriority() const override { return 30; }
};
```

### 4. 层次化地址解析器（新功能）
```cpp
class HierarchicalAddressResolver : public AddressResolver {
public:
    bool canHandle(const std::string& addr_str) override {
        // 检查是否包含层次化标识符
        return addr_str.find('.') != std::string::npos && 
               addr_str.find('[') != std::string::npos;
    }
    
    bool resolve(const std::string& addr_str, GenericAddress& out_addr) override {
        out_addr = GenericAddress(addr_str, GenericAddress::Type::HIERARCHICAL);
        return true;
    }
    
    int getPriority() const override { return 40; }
};
```

## 📝 **实际使用示例**

### 多设备混合配置
```bash
# 设备A：传统数值地址
READ:0x83c00100→device_a_status

# 设备B：字符串地址
READ:"TEMP_SENSOR_01"→device_b_temp

# 设备C：路径地址
READ:"/system/sensors/humidity"→device_c_humidity

# 设备D：层次化地址
READ:"motor.channel[0].position"→device_d_position

# 延时
DELAY:100

# 混合写入
WRITE:0x83c00104=${device_a_status}+1
WRITE:"HEATER_CONTROL"=${device_b_temp}>25?1:0
WRITE:"/system/actuators/fan"=${device_c_humidity}>80?1:0
```

### 不同协议的批量操作
```bash
# Modbus设备
READ:"40001","40002","40003"→modbus_data

# OPC UA设备  
READ:"ns=2;s=Temperature","ns=2;s=Pressure"→opcua_data

# 自定义协议设备
READ:"DEV1.CH0.TEMP","DEV1.CH1.TEMP"→custom_data

# 条件处理
COND_WRITE:"ALARM_OUTPUT"=1 if ${modbus_data[0]}>100
```

## 🔄 **向后兼容性保证**

### 完全兼容现有格式
```bash
# 现有配置完全不变
READ:0x83c00100,0x83c00104,0x83c00108→existing_data
WRITE:0x83c00200=${existing_data[0]}+1

# 新格式可选使用
READ:"NEW_SENSOR_01"→new_data
WRITE:"/new/actuator"=${new_data}*2
```

### 解析优先级
1. **数值地址** (优先级 10) - 保证现有行为
2. **字符串地址** (优先级 20) - 引号包围
3. **路径地址** (优先级 30) - 斜杠开头
4. **层次化地址** (优先级 40) - 包含点和方括号
5. **自定义解析器** (优先级 50+) - 用户扩展

## 🚀 **扩展接口**

### 添加自定义地址格式
```cpp
// 用户可以添加自己的地址解析器
class MyCustomAddressResolver : public AddressResolver {
public:
    bool canHandle(const std::string& addr_str) override {
        // 检查自定义格式，例如：DEVICE_ID:REGISTER_NAME
        return addr_str.find(':') != std::string::npos && 
               addr_str.find("DEV") == 0;
    }
    
    bool resolve(const std::string& addr_str, GenericAddress& out_addr) override {
        // 解析 "DEV001:TEMPERATURE" 格式
        // 转换为设备特定的地址表示
        out_addr = GenericAddress(addr_str, GenericAddress::Type::CUSTOM);
        return true;
    }
    
    int getPriority() const override { return 50; }
};

// 注册自定义解析器
AddressResolverManager::getInstance().addResolver(
    std::make_shared<MyCustomAddressResolver>()
);
```

## 💡 **实现优势**

### 1. 真正的格式扩展
- ✅ 支持任意字符串作为地址
- ✅ 支持不同设备的原生地址格式
- ✅ 不需要维护地址映射表

### 2. 完全向后兼容
- ✅ 现有数值地址格式完全不变
- ✅ 现有配置无需修改
- ✅ 渐进式采用新格式

### 3. 高度可扩展
- ✅ 插件式解析器架构
- ✅ 优先级系统避免冲突
- ✅ 支持任意自定义格式

### 4. 类型安全
- ✅ 编译时类型检查
- ✅ 运行时格式验证
- ✅ 详细的错误信息

## 🎯 **总结**

这个设计真正解决了您提出的问题：

**问题**：当前只支持数值地址格式，无法扩展到其他设备的地址格式
**解决**：通用地址系统 + 可扩展解析器架构

**效果**：
- 支持任意格式的地址（数值、字符串、路径、层次化、自定义）
- 完全向后兼容现有代码
- 高度可扩展，支持未来任何新的地址格式
- 类型安全，避免格式混淆

这样您就可以在同一个系统中同时支持传统的数值地址设备和使用字符串地址的新设备了！
