# 批量操作设计方案对比分析

## 问题背景

在TCP通信插件中，需要支持复杂的批量读写操作，特别是**读取的值需要传递给后续的写入操作**。需要在以下几种方案中选择最佳的设计：

1. CSV文件方式
2. JSON内嵌方式  
3. UI可视化批量编辑方式

## 方案详细对比

### 方案1：CSV文件方式

**实现方式：**
```
操作类型,地址,值/变量引用,延时,结果变量,条件,描述
READ,0x83c00100,,0,device_status,,读取设备状态
DELAY,,,,100,,,等待设备稳定
WRITE,0x83c00104,${device_status}+1,0,,,写入新状态
```

**优点：**
- ✅ 数据结构清晰，易于批量编辑
- ✅ 支持Excel等工具编辑
- ✅ 版本控制友好
- ✅ 支持复杂的数据关系

**缺点：**
- ❌ 需要维护两个文件（.rsfsc + .csv）
- ❌ 文件同步问题（CSV修改后需要重新加载）
- ❌ 部署和分发复杂（需要打包多个文件）
- ❌ UI设计者需要理解CSV格式
- ❌ 路径依赖问题

### 方案2：JSON内嵌方式

**实现方式：**
```json
{
  "batch_operations": [
    {
      "type": "READ",
      "addresses": ["0x83c00100"],
      "result_variable": "device_status",
      "description": "读取设备状态"
    },
    {
      "type": "DELAY",
      "delay_ms": 100
    },
    {
      "type": "WRITE", 
      "addresses": ["0x83c00104"],
      "values": ["${device_status}+1"],
      "description": "写入新状态"
    }
  ]
}
```

**优点：**
- ✅ 单文件维护
- ✅ 数据一致性好
- ✅ 支持复杂的嵌套结构
- ✅ 类型安全

**缺点：**
- ❌ JSON编辑复杂，容易出错
- ❌ UI设计者需要理解JSON语法
- ❌ 大型配置难以维护
- ❌ 缺乏语法高亮和验证

### 方案3：UI可视化批量编辑方式（推荐）

**实现方式：**
- 表格形式的可视化编辑器
- 内置模板和向导
- 实时验证和错误提示
- 支持变量引用的智能提示

**界面设计：**
```
┌─────────────────────────────────────────────────────────────────┐
│ 批量操作编辑器                                    [模板▼] [导入] [导出] │
├─────────────────────────────────────────────────────────────────┤
│ 操作类型 │ 地址列表      │ 值/变量引用    │ 延时 │ 结果变量 │ 条件 │ 描述     │
├─────────────────────────────────────────────────────────────────┤
│ READ    │ 0x83c00100   │               │  0   │ status   │      │ 读取状态  │
│ DELAY   │              │               │ 100  │          │      │ 等待稳定  │
│ WRITE   │ 0x83c00104   │ ${status}+1   │  0   │          │      │ 写入状态  │
└─────────────────────────────────────────────────────────────────┘
[添加] [删除] [上移] [下移]
```

**优点：**
- ✅ **用户体验最佳**：直观的表格编辑
- ✅ **零学习成本**：UI设计者无需学习特殊格式
- ✅ **实时验证**：输入时即时检查错误
- ✅ **智能提示**：变量引用自动补全
- ✅ **模板支持**：预设常用操作模板
- ✅ **单文件维护**：配置保存在.rsfsc中
- ✅ **可视化调试**：支持单步执行和断点
- ✅ **导入导出**：支持与CSV/JSON互转

**缺点：**
- ❌ 开发复杂度较高
- ❌ 需要额外的UI组件

## 变量引用和数据传递设计

### 核心需求
读取操作的结果需要传递给后续的写入操作，支持以下场景：

1. **直接引用**：`${variable_name}`
2. **数组索引**：`${read_result[0]}`，`${read_result[1]}`
3. **表达式计算**：`${status} + 1`，`${value} * 2`
4. **条件判断**：`if ${status} == 1 then 10 else 20`

### 实现方案

**变量存储：**
```cpp
std::map<std::string, std::vector<int32_t>> operation_results_;
```

**引用解析：**
```cpp
// 支持的引用格式：
// ${variable_name}      - 引用整个数组的第一个值
// ${variable_name[0]}   - 引用数组的第0个元素  
// ${variable_name[1]}   - 引用数组的第1个元素
// ${variable_name} + 1  - 简单表达式计算
int32_t resolveValueReference(const std::string& value_ref);
```

**条件表达式：**
```cpp
// 支持的条件格式：
// ${status} == 1
// ${value} > 100
// ${result[0]} != 0
bool evaluateCondition(const std::string& condition);
```

## 推荐方案：UI可视化批量编辑

### 选择理由

1. **用户体验优先**：UI设计者是主要用户，应该提供最佳的用户体验
2. **降低学习成本**：表格编辑是通用技能，无需学习特殊格式
3. **减少错误**：实时验证和智能提示大大减少配置错误
4. **提高效率**：模板和向导功能提高配置效率
5. **便于维护**：单文件维护，避免文件同步问题

### 实现策略

**阶段1：基础表格编辑器**
- 实现基本的表格编辑功能
- 支持添加、删除、移动操作
- 基础的数据验证

**阶段2：高级功能**
- 变量引用智能提示
- 表达式语法高亮
- 预设模板库

**阶段3：调试和优化**
- 可视化执行流程
- 单步调试功能
- 性能优化

### 数据存储格式

最终在.rsfsc文件中存储为JSON格式：
```json
{
  "batch_config": {
    "operations": [...],
    "variables": {...},
    "templates": {...}
  }
}
```

但用户通过可视化界面编辑，无需直接接触JSON。

## 结论

**推荐采用方案3：UI可视化批量编辑方式**

这种方案在用户体验、维护性和功能性之间达到了最佳平衡，特别适合UI设计者这一主要用户群体的需求。虽然开发复杂度较高，但长期来看能够显著提升用户效率和满意度。

### 实施建议

1. **优先实现核心功能**：基础的表格编辑和变量引用
2. **渐进式增强**：逐步添加高级功能
3. **用户反馈驱动**：根据实际使用反馈优化界面
4. **保持兼容性**：支持导入导出，便于与其他工具集成
