# TCP通信插件智能批量操作使用指南

## 概述

`TcpBatchSequence`（智能批量操作）节点是TCP通信插件的核心功能，支持复杂的批量读写操作、变量引用、条件逻辑和多种地址格式。本指南详细介绍如何使用这个强大的功能。

## 基本语法

### 操作类型

| 操作类型 | 语法格式 | 说明 |
|---------|---------|------|
| **READ** | `READ:地址→变量名` | 读取寄存器并存储到变量 |
| **WRITE** | `WRITE:地址=值` | 写入寄存器 |
| **DELAY** | `DELAY:毫秒数` | 延时操作 |
| **COND_WRITE** | `COND_WRITE:地址=值 if 条件` | 条件写入 |

### 配置格式
- 每行一个操作
- 支持 `#` 注释
- 自动去除前后空白

## 地址格式详解

### 1. 单个地址
```
READ:0x83c00100→status
WRITE:0x83c00104=1
```

### 2. 逗号分隔列表
```
# 读取多个独立寄存器
READ:0x83c00100,0x83c00104,0x83c00108→xyz_data

# 写入多个寄存器
WRITE:0x83c00200,0x83c00204,0x83c00208=10,20,30
```

### 3. 范围表示
```
# 读取连续地址范围（自动4字节对齐）
READ:0x83c00100-0x83c00120→sensor_array

# 写入连续地址
WRITE:0x83c00100-0x83c00110=100,200,300,400,500
```

**范围说明：**
- `0x83c00100-0x83c00120` 生成：`[0x83c00100, 0x83c00104, 0x83c00108, 0x83c0010c, 0x83c00110, 0x83c00114, 0x83c00118, 0x83c0011c, 0x83c00120]`
- 自动按4字节递增（地址对齐）
- 包含起始和结束地址

### 4. 混合模式
```
# 复杂地址组合
READ:0x83c00100,0x83c00104-0x83c00110,0x83c00120→complex_data

# 对应地址：[0x83c00100, 0x83c00104, 0x83c00108, 0x83c0010c, 0x83c00110, 0x83c00120]
```

## 变量引用系统

### 基本引用
```
READ:0x83c00100→device_status
WRITE:0x83c00104=${device_status}
```

### 数组索引引用
```
READ:0x83c00100-0x83c00108→xyz_data
WRITE:0x83c00200=${xyz_data[0]}    # X轴数据
WRITE:0x83c00204=${xyz_data[1]}    # Y轴数据
WRITE:0x83c00208=${xyz_data[2]}    # Z轴数据
```

### 表达式计算
```
READ:0x83c00100→current_value
WRITE:0x83c00104=${current_value}+1
WRITE:0x83c00108=${current_value}*2
WRITE:0x83c0010c=${current_value}-10
```

### 条件表达式
```
READ:0x83c00100→status
COND_WRITE:0x83c00104=1 if ${status}==0
COND_WRITE:0x83c00108=2 if ${status}>100
COND_WRITE:0x83c0010c=3 if ${status}!=255
```

## 实际应用示例

### 示例1：设备初始化流程
```
# 设备初始化标准流程
READ:0x83c00000→device_id           # 读取设备ID
READ:0x83c00004→firmware_version    # 读取固件版本
DELAY:100                           # 等待设备稳定

# 根据设备ID配置参数
COND_WRITE:0x83c00100=1 if ${device_id}==0x1234
COND_WRITE:0x83c00100=2 if ${device_id}==0x5678

# 写入固件兼容性标志
WRITE:0x83c00104=${firmware_version}+1000

# 验证配置
READ:0x83c00100→config_status
COND_WRITE:0x83c00200=1 if ${config_status}!=0
```

### 示例2：传感器校准流程
```
# 三轴传感器校准流程
READ:0x83c01000-0x83c01008→pre_calib_xyz    # 读取校准前XYZ数据
WRITE:0x83c01020=1                          # 启动校准命令
DELAY:2000                                  # 等待校准完成

READ:0x83c01000-0x83c01008→post_calib_xyz   # 读取校准后数据

# 计算并写入校准偏移量
WRITE:0x83c01030=${post_calib_xyz[0]}-${pre_calib_xyz[0]}  # X轴偏移
WRITE:0x83c01034=${post_calib_xyz[1]}-${pre_calib_xyz[1]}  # Y轴偏移
WRITE:0x83c01038=${post_calib_xyz[2]}-${pre_calib_xyz[2]}  # Z轴偏移

# 设置校准完成标志
WRITE:0x83c01040=1
```

### 示例3：批量参数配置
```
# 读取当前参数并备份
READ:0x83c02000,0x83c02004,0x83c02008,0x83c0200c→current_params
WRITE:0x83c02100,0x83c02104,0x83c02108,0x83c0210c=${current_params[0]},${current_params[1]},${current_params[2]},${current_params[3]}

# 逐个写入新参数（每次写入后延时）
WRITE:0x83c02000=1000
DELAY:50
WRITE:0x83c02004=2000
DELAY:50
WRITE:0x83c02008=3000
DELAY:50
WRITE:0x83c0200c=4000
DELAY:100

# 验证所有参数
READ:0x83c02000,0x83c02004,0x83c02008,0x83c0200c→new_params
COND_WRITE:0x83c02010=1 if ${new_params[0]}==1000 && ${new_params[1]}==2000
```

### 示例4：复杂设备通信
```
# 复杂设备通信示例
READ:0x83c00000→device_type                 # 读取设备类型

# 根据设备类型读取不同的状态寄存器
COND_WRITE:0x83c00010=0x100 if ${device_type}==1    # 类型1设备的状态地址
COND_WRITE:0x83c00010=0x200 if ${device_type}==2    # 类型2设备的状态地址

READ:0x83c00010→status_addr                 # 读取状态地址
# 注意：这里需要动态地址读取，当前版本暂不支持，需要预设地址

# 多设备状态检查
READ:0x83c00100-0x83c00110,0x83c00200-0x83c00210→multi_device_status

# 批量状态处理
WRITE:0x83c00300=${multi_device_status[0]}|0x8000    # 设置状态位
WRITE:0x83c00304=${multi_device_status[1]}|0x8000
```

## 高级特性

### 注释支持
```
# 这是注释行，会被忽略
READ:0x83c00100→status    # 行尾注释也支持

# 多行注释示例：
# 以下是设备初始化流程
# 包含状态检查和参数配置
READ:0x83c00000→device_id
```

### 错误处理
```
# 配置错误处理策略
# 在节点属性中设置 "错误时停止" = true/false

READ:0x83c00100→status
COND_WRITE:0x83c00104=1 if ${status}!=0    # 如果状态异常则写入错误码
```

### 地址格式灵活性
```
# 支持十六进制和十进制混合
READ:0x83c00100,2214592772→mixed_format

# 支持不同的十六进制格式
READ:0x83C00100,0X83C00104→upper_case
```

## 最佳实践

### 1. 变量命名规范
```
# 推荐使用描述性的变量名
READ:0x83c00100→device_status        # ✅ 好
READ:0x83c00100→status              # ✅ 可以
READ:0x83c00100→s                   # ❌ 不推荐
```

### 2. 操作分组
```
# 将相关操作分组，使用注释分隔
# === 设备初始化 ===
READ:0x83c00000→device_id
WRITE:0x83c00100=1

# === 参数配置 ===
WRITE:0x83c00200=1000
WRITE:0x83c00204=2000

# === 状态验证 ===
READ:0x83c00300→final_status
```

### 3. 延时策略
```
# 在关键操作后添加适当延时
WRITE:0x83c00100=1    # 启动命令
DELAY:100             # 等待命令生效

READ:0x83c00104→status # 读取状态
```

## 故障排除

### 常见错误

1. **地址格式错误**
   ```
   # ❌ 错误：地址未对齐
   READ:0x83c00101→data
   
   # ✅ 正确：4字节对齐
   READ:0x83c00100→data
   ```

2. **变量引用错误**
   ```
   # ❌ 错误：变量未定义
   WRITE:0x83c00100=${undefined_var}
   
   # ✅ 正确：先定义变量
   READ:0x83c00000→defined_var
   WRITE:0x83c00100=${defined_var}
   ```

3. **语法错误**
   ```
   # ❌ 错误：缺少等号
   WRITE:0x83c00100 1
   
   # ✅ 正确：使用等号
   WRITE:0x83c00100=1
   ```

### 调试技巧

1. **使用注释标记调试点**
2. **分步验证复杂操作**
3. **检查执行日志输出**
4. **使用简单配置测试连接**

## 总结

智能批量操作功能提供了强大而灵活的设备通信能力，通过合理使用地址格式、变量引用和条件逻辑，可以大大简化复杂的设备操作流程。建议从简单的示例开始，逐步掌握高级特性。
