﻿
/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef BT_NODE_TCP_H
#define BT_NODE_TCP_H

#include "../../common/common_types.h"
#include "mems_tcp.h"
#include <behaviortree_cpp/behavior_tree.h>
#include <behaviortree_cpp/utils/strcat.hpp>
#include <cstdint>
#include <string>
#include <vector>

namespace robosense::lidar
{
// 辅助函数声明
bool strToUint32(const std::string& _str, uint32_t& _out_value);

/**
 * @brief 解析地址字符串，支持多种格式和扩展解析器
 *
 * 支持的格式:
 * 1. 逗号分隔列表: "0x83c00100,0x83c00104,REG_STATUS"
 * 2. 范围表示: "0x83c00100-0x83c00120", "REG_BASE-REG_END"
 * 3. 混合模式: "0x83c00100,REG_STATUS,BASE+0x100"
 * 4. 符号地址: "REG_STATUS", "DEVICE_ID", "FIRMWARE_VERSION"
 * 5. 表达式: "BASE+0x100", "REG_OFFSET*2+4"
 *
 * @param _addr_str 地址字符串
 * @param _out_addresses 解析后的地址列表
 * @param _resolver_manager 地址解析管理器（可选，使用默认实例）
 * @return bool 解析是否成功
 */
bool parseAddressString(const std::string& _addr_str, std::vector<uint32_t>& _out_addresses);

// SequenceWithFailureCheck 节点
class SequenceWithFailureCheck : public BT::ControlNode
{
public:
  SequenceWithFailureCheck(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
};

// TCP连接节点
class TcpConnect : public BT::CoroActionNode
{
public:
  TcpConnect(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::future<bool> connection_future_;  // 跟踪异步连接
};

// TCP断开连接节点
class TcpDisconnect : public BT::StatefulActionNode
{
public:
  TcpDisconnect(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus onStart() override;
  BT::NodeStatus onRunning() override;
  void onHalted() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::future<bool> disconnect_future_;
};

// TCP读寄存器节点
class TcpReadReg : public BT::CoroActionNode
{
public:
  TcpReadReg(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  using ReadResultFuture = std::future<std::pair<bool, std::vector<int32_t>>>;
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  ReadResultFuture read_future_;
};

// TCP写寄存器节点
class TcpWriteReg : public BT::CoroActionNode
{
public:
  TcpWriteReg(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::future<bool> write_future_;
};

// 批量操作项结构
struct BatchOperationItem
{
  enum class OperationType : uint8_t
  {
    READ,
    WRITE,
    DELAY,
    CONDITIONAL_WRITE  // 基于读取结果的条件写入
  };

  OperationType operation_type;
  std::vector<uint32_t> addresses;
  std::vector<int32_t> values;          // 用于写操作的值
  std::vector<std::string> value_refs;  // 引用之前读取结果的变量名
  uint32_t delay_ms { 0 };              // 仅用于延时操作
  uint32_t timeout_ms { 100 };          // 操作超时时间
  std::string description;              // 操作描述
  std::string result_variable;          // 存储读取结果的变量名
  std::string condition_expression;     // 条件表达式（用于条件写入）
};

// 批量操作序列节点
class TcpBatchSequence : public BT::CoroActionNode
{
public:
  TcpBatchSequence(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::vector<BatchOperationItem> batch_operations_;
  std::map<std::string, std::vector<int32_t>> operation_results_;  // 存储操作结果
  size_t current_operation_index_ { 0 };
  std::future<std::pair<bool, std::vector<int32_t>>> operation_future_;
  std::chrono::steady_clock::time_point delay_start_time_;
  bool is_delay_operation_ { false };

  // 解析批量操作配置
  bool parseBatchConfiguration(const std::string& _config_str);

  // 解析JSON格式的批量配置
  bool parseJsonBatchConfiguration(const std::string& _json_str);

  // 执行当前操作
  void executeCurrentOperation();

  // 检查操作是否完成
  bool isCurrentOperationComplete();

  // 获取当前操作结果
  BT::NodeStatus getCurrentOperationResult();

  // 解析值引用（如 ${read_result_1[0]} ）
  int32_t resolveValueReference(const std::string& _value_ref);

  // 评估条件表达式
  bool evaluateCondition(const std::string& _condition);
};

// 可视化批量编辑器节点（支持UI表格编辑）
class TcpBatchEditor : public BT::CoroActionNode
{
public:
  TcpBatchEditor(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::vector<BatchOperationItem> batch_operations_;
  std::map<std::string, std::vector<int32_t>> operation_results_;
  size_t current_operation_index_ { 0 };
  std::future<std::pair<bool, std::vector<int32_t>>> operation_future_;
  std::chrono::steady_clock::time_point delay_start_time_;
  bool is_delay_operation_ { false };

  // 从UI表格数据解析批量操作
  bool parseTableData(const std::string& _table_json);

  // 执行操作的核心逻辑（与TcpBatchSequence共享）
  void executeCurrentOperation();
  bool isCurrentOperationComplete();
  BT::NodeStatus getCurrentOperationResult();
  int32_t resolveValueReference(const std::string& _value_ref);
  bool evaluateCondition(const std::string& _condition);
};

}  // namespace robosense::lidar

#endif  // BT_NODE_TCP_H