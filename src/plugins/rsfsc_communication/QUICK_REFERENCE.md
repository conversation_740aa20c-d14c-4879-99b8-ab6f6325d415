# TCP智能批量操作 - 快速参考

## 🚀 基本语法

| 操作 | 格式 | 示例 |
|------|------|------|
| **读取** | `READ:地址→变量名` | `READ:0x83c00100→status` |
| **写入** | `WRITE:地址=值` | `WRITE:0x83c00104=1` |
| **延时** | `DELAY:毫秒数` | `DELAY:100` |
| **条件写入** | `COND_WRITE:地址=值 if 条件` | `COND_WRITE:0x83c00108=1 if ${status}==0` |

## 📍 地址格式（支持扩展）

| 格式类型 | 示例 | 说明 |
|----------|------|------|
| **数值地址** | `0x83c00100`, `2214592768` | 十六进制、十进制、八进制 |
| **符号地址** | `REG_STATUS`, `DEVICE_ID` | 预定义符号名称 |
| **表达式** | `BASE+0x100`, `OFFSET*2+4` | 简单算术表达式 |
| **逗号分隔** | `0x83c00100,REG_STATUS,BASE+0x100` | 混合格式组合 |
| **范围表示** | `0x83c00100-0x83c00120`, `REG_BASE-REG_END` | 连续地址范围 |
| **混合模式** | `0x83c00100,REG_STATUS,BASE+0x100` | 所有格式组合 |

## 🔗 变量引用

| 类型 | 格式 | 示例 |
|------|------|------|
| **基本引用** | `${变量名}` | `${status}` |
| **数组索引** | `${变量名[索引]}` | `${data[0]}`, `${data[1]}` |
| **表达式** | `${变量名}+值` | `${status}+1`, `${value}*2` |

## ⚡ 快速示例

### 数值地址（传统方式）

```bash
READ:0x83c00100→status
DELAY:100
WRITE:0x83c00104=${status}+1
```

### 符号地址（推荐方式）

```bash
READ:REG_STATUS→status
DELAY:100
WRITE:CONFIG_BASE=${status}+1
```

### 表达式地址

```bash
READ:BASE+STATUS_OFFSET→status
READ:CHANNEL_BASE+CHANNEL_ID*16→channel_data
WRITE:CONFIG_BASE+PARAM_ID*4=${param_value}
```

### 混合格式

```bash
READ:0x83c00100,REG_STATUS,BASE+0x100→mixed_data
READ:REG_BASE-REG_BASE+0x20→register_dump
WRITE:0x83c00200,CONFIG_BASE,PARAM_BASE+4=1,2,3
```

### 传感器校准

```bash
READ:SENSOR_BASE-SENSOR_BASE+0x8→pre_data
WRITE:CALIB_CMD=1
DELAY:2000
READ:SENSOR_BASE-SENSOR_BASE+0x8→post_data
WRITE:OFFSET_BASE=${post_data[0]}-${pre_data[0]}
```

### 条件逻辑

```bash
READ:DEVICE_TYPE→device_type
COND_WRITE:CONFIG_MODE=1 if ${device_type}==1
COND_WRITE:CONFIG_MODE=2 if ${device_type}==2
```

## 💡 实用技巧

- **注释**：使用 `#` 添加注释
- **对齐**：地址必须4字节对齐
- **格式**：每行一个操作
- **空白**：自动去除前后空格
- **进制**：支持十六进制（0x）和十进制

## ⚠️ 常见错误

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| `地址未对齐` | 地址不是4的倍数 | 使用4字节对齐地址 |
| `变量未定义` | 引用了不存在的变量 | 先READ定义变量 |
| `语法错误` | 格式不正确 | 检查冒号、等号、箭头 |

## 🎯 最佳实践

1. **使用描述性变量名**：`device_status` 而不是 `s`
2. **添加注释说明**：解释复杂操作的目的
3. **合理使用延时**：在关键操作后添加延时
4. **分组相关操作**：使用注释分隔不同功能块
5. **从简单开始**：先测试基本功能再添加复杂逻辑
