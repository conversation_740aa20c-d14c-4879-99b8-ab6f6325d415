# TCP通信插件批量操作使用示例

## 概述

本文档展示了如何使用优化后的TCP通信插件进行复杂的批量操作，特别是如何实现读取值传递给后续写入操作的功能。

## 使用方案对比

### 方案1：传统方式（多个单独节点）

```
TcpConnect → TcpReadReg → TcpDelay → TcpWriteReg → TcpDisconnect
```

**缺点：**
- 需要多个节点
- 数据传递复杂
- 配置繁琐
- 难以维护

### 方案2：文本配置批量操作

使用 `TcpBatchSequence` 节点：

**配置示例：**
```
READ:0x83c00100→device_status;DELAY:100;WRITE:0x83c00104=${device_status}+1
```

**优点：**
- 单个节点完成复杂操作
- 支持变量引用

**缺点：**
- 需要学习特殊语法
- 容易出错
- 调试困难

### 方案3：可视化批量编辑器（推荐）

使用 `TcpBatchEditor` 节点：

**界面示例：**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 批量操作编辑器                                      [设备初始化模板▼] [导入] [导出] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作类型 │ 地址列表        │ 值/变量引用      │ 延时 │ 结果变量    │ 条件    │ 描述       │
├─────────────────────────────────────────────────────────────────────────────┤
│ READ    │ 0x83c00100     │                 │  0   │ device_status│        │ 读取设备状态 │
│ DELAY   │                │                 │ 100  │             │        │ 等待设备稳定 │
│ WRITE   │ 0x83c00104     │ ${device_status}+1│  0   │             │        │ 写入新状态  │
│ READ    │ 0x83c00104     │                 │  0   │ new_status  │        │ 验证写入结果 │
│ COND_WRITE│ 0x83c00108   │ 1               │  0   │             │${new_status}==2│ 条件写入 │
└─────────────────────────────────────────────────────────────────────────────┘
[添加] [删除] [上移] [下移] [复制] [粘贴]
```

## 详细使用示例

### 示例1：设备初始化序列

**需求：**
1. 读取设备当前状态
2. 等待100ms让设备稳定
3. 根据当前状态写入新的配置值
4. 验证配置是否写入成功

**可视化配置：**

| 操作类型 | 地址列表 | 值/变量引用 | 延时 | 结果变量 | 条件 | 描述 |
|---------|---------|------------|------|---------|------|------|
| READ | 0x83c00100 | | 0 | current_status | | 读取当前状态 |
| DELAY | | | 100 | | | 等待设备稳定 |
| WRITE | 0x83c00104 | ${current_status}+1 | 0 | | | 写入新状态 |
| READ | 0x83c00104 | | 0 | verify_status | | 验证写入结果 |
| COND_WRITE | 0x83c00108 | 1 | 0 | | ${verify_status}==${current_status}+1 | 成功标志 |

**生成的JSON配置：**
```json
{
  "operations": [
    {
      "type": "READ",
      "addresses": ["0x83c00100"],
      "result_variable": "current_status",
      "description": "读取当前状态"
    },
    {
      "type": "DELAY",
      "delay_ms": 100,
      "description": "等待设备稳定"
    },
    {
      "type": "WRITE",
      "addresses": ["0x83c00104"],
      "values": ["${current_status}+1"],
      "description": "写入新状态"
    },
    {
      "type": "READ",
      "addresses": ["0x83c00104"],
      "result_variable": "verify_status",
      "description": "验证写入结果"
    },
    {
      "type": "CONDITIONAL_WRITE",
      "addresses": ["0x83c00108"],
      "values": ["1"],
      "condition": "${verify_status}==${current_status}+1",
      "description": "成功标志"
    }
  ]
}
```

### 示例2：传感器校准流程

**需求：**
1. 读取校准前的传感器数据
2. 发送校准命令
3. 等待2秒校准完成
4. 读取校准后的数据
5. 计算校准差值并写入补偿寄存器

**可视化配置：**

| 操作类型 | 地址列表 | 值/变量引用 | 延时 | 结果变量 | 条件 | 描述 |
|---------|---------|------------|------|---------|------|------|
| READ | 0x83c01000-0x83c01008 | | 0 | pre_calib | | 读取校准前数据 |
| WRITE | 0x83c01020 | 1 | 0 | | | 发送校准命令 |
| DELAY | | | 2000 | | | 等待校准完成 |
| READ | 0x83c01000-0x83c01008 | | 0 | post_calib | | 读取校准后数据 |
| WRITE | 0x83c01030 | ${post_calib[0]}-${pre_calib[0]} | 0 | | | 写入X轴补偿 |
| WRITE | 0x83c01034 | ${post_calib[1]}-${pre_calib[1]} | 0 | | | 写入Y轴补偿 |
| WRITE | 0x83c01038 | ${post_calib[2]}-${pre_calib[2]} | 0 | | | 写入Z轴补偿 |

### 示例3：批量参数配置

**需求：**
1. 读取当前所有参数
2. 备份到备份寄存器
3. 逐个写入新参数（每次写入后延时）
4. 验证所有参数是否正确写入

**可视化配置：**

| 操作类型 | 地址列表 | 值/变量引用 | 延时 | 结果变量 | 条件 | 描述 |
|---------|---------|------------|------|---------|------|------|
| READ | 0x83c02000,0x83c02004,0x83c02008 | | 0 | old_params | | 读取当前参数 |
| WRITE | 0x83c02100,0x83c02104,0x83c02108 | ${old_params[0]},${old_params[1]},${old_params[2]} | 0 | | | 备份参数 |
| WRITE | 0x83c02000 | 100 | 50 | | | 写入参数1 |
| WRITE | 0x83c02004 | 200 | 50 | | | 写入参数2 |
| WRITE | 0x83c02008 | 300 | 50 | | | 写入参数3 |
| READ | 0x83c02000,0x83c02004,0x83c02008 | | 0 | new_params | | 验证新参数 |
| COND_WRITE | 0x83c02010 | 1 | 0 | | ${new_params[0]}==100 && ${new_params[1]}==200 && ${new_params[2]}==300 | 配置成功标志 |

## 高级功能

### 变量引用语法

**基本引用：**
- `${variable_name}` - 引用变量的第一个值
- `${variable_name[0]}` - 引用数组的第0个元素
- `${variable_name[1]}` - 引用数组的第1个元素

**表达式计算：**
- `${value} + 1` - 加法运算
- `${value} * 2` - 乘法运算
- `${value1} - ${value2}` - 变量间运算

**条件表达式：**
- `${status} == 1` - 等于判断
- `${value} > 100` - 大于判断
- `${result[0]} != 0` - 不等于判断
- `${a} == 1 && ${b} == 2` - 逻辑与
- `${a} == 1 || ${b} == 2` - 逻辑或

### 预设模板

**设备初始化模板：**
```json
{
  "name": "设备初始化",
  "description": "标准的设备初始化流程",
  "operations": [...]
}
```

**传感器校准模板：**
```json
{
  "name": "传感器校准",
  "description": "三轴传感器校准流程",
  "operations": [...]
}
```

**参数配置模板：**
```json
{
  "name": "批量参数配置",
  "description": "批量配置设备参数",
  "operations": [...]
}
```

## 调试和监控

### 执行状态可视化

在执行过程中，表格会实时显示：
- **当前执行的操作**：高亮显示
- **已完成的操作**：绿色背景
- **失败的操作**：红色背景
- **等待执行的操作**：灰色背景

### 变量监控

在执行过程中可以查看所有变量的当前值：
```json
{
  "current_status": [1],
  "verify_status": [2],
  "pre_calib": [100, 200, 300],
  "post_calib": [105, 198, 302]
}
```

### 执行日志

详细的执行日志包括：
```
[2025-01-21 10:30:01] 开始执行批量操作
[2025-01-21 10:30:01] 操作1: READ 0x83c00100 → current_status = [1]
[2025-01-21 10:30:01] 操作2: DELAY 100ms
[2025-01-21 10:30:01] 操作3: WRITE 0x83c00104 = 2 (${current_status}+1)
[2025-01-21 10:30:01] 操作4: READ 0x83c00104 → verify_status = [2]
[2025-01-21 10:30:01] 操作5: 条件检查 ${verify_status}==${current_status}+1 → true
[2025-01-21 10:30:01] 操作5: WRITE 0x83c00108 = 1
[2025-01-21 10:30:01] 批量操作执行完成，共5个操作，全部成功
```

## 总结

通过使用可视化批量编辑器，UI设计者可以：

1. **零学习成本**：使用熟悉的表格界面
2. **实时验证**：输入时即时检查错误
3. **智能提示**：变量引用自动补全
4. **模板支持**：快速应用常用配置
5. **可视化调试**：实时监控执行状态

这种方案完美解决了读取值传递给写入操作的需求，同时提供了最佳的用户体验。
