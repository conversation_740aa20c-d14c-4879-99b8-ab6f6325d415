# TCP通信插件 - 智能批量操作

## 🚀 概述

TCP通信插件提供了强大的智能批量操作功能，支持复杂的读写操作序列、变量引用、条件逻辑和多种地址格式。通过单个节点即可完成原本需要多个节点才能实现的复杂设备通信任务。

## 📚 文档导航

| 文档 | 说明 |
|------|------|
| **[详细使用指南](BATCH_OPERATION_GUIDE.md)** | 完整的功能说明和示例 |
| **[快速参考](QUICK_REFERENCE.md)** | 语法速查表 |
| **[使用示例](USAGE_EXAMPLES.md)** | 实际应用场景 |
| **[设计方案](FINAL_SOLUTION.md)** | 技术架构说明 |

## 🎯 核心功能

### ✅ 解决的核心问题
- **读取值传递给写入操作** - 通过变量引用系统实现
- **复杂地址格式支持** - 单个、逗号分隔、范围、混合模式
- **条件逻辑控制** - 基于读取结果的条件写入
- **单文件维护** - 配置保存在.rsfsc中，无需额外文件

### 🔧 主要节点

#### TcpBatchSequence（智能批量操作）
- **显示名称**：智能批量操作
- **功能**：支持变量引用、条件逻辑、多种地址格式的批量操作
- **配置**：大的多行文本编辑器（8倍高度）

## ⚡ 快速开始

### 基本语法

```bash
READ:地址→变量名          # 读取并存储到变量
WRITE:地址=值            # 写入寄存器  
DELAY:毫秒数             # 延时操作
COND_WRITE:地址=值 if 条件 # 条件写入
```

### 地址格式

```bash
# 单个地址
READ:0x83c00100→status

# 逗号分隔列表
READ:0x83c00100,0x83c00104,0x83c00108→xyz_data

# 范围表示（自动4字节对齐）
READ:0x83c00100-0x83c00120→range_data

# 混合模式
READ:0x83c00100,0x83c00104-0x83c00110→mixed_data
```

### 变量引用

```bash
# 基本引用
READ:0x83c00100→device_status
WRITE:0x83c00104=${device_status}

# 数组索引
READ:0x83c00100-0x83c00108→xyz_data
WRITE:0x83c00200=${xyz_data[0]}    # X轴
WRITE:0x83c00204=${xyz_data[1]}    # Y轴

# 表达式计算
WRITE:0x83c00300=${device_status}+1

# 条件逻辑
COND_WRITE:0x83c00400=1 if ${device_status}==0
```

## 📋 典型应用

### 设备初始化

```bash
# 读取设备信息
READ:0x83c00000→device_id
READ:0x83c00004→firmware_version
DELAY:100

# 根据设备类型配置
COND_WRITE:0x83c00100=1 if ${device_id}==0x1234
COND_WRITE:0x83c00100=2 if ${device_id}==0x5678

# 写入版本标志
WRITE:0x83c00104=${firmware_version}+1000
```

### 传感器校准

```bash
# 校准前数据
READ:0x83c01000-0x83c01008→pre_calib
WRITE:0x83c01020=1                    # 启动校准
DELAY:2000                            # 等待完成

# 校准后数据和偏移计算
READ:0x83c01000-0x83c01008→post_calib
WRITE:0x83c01030=${post_calib[0]}-${pre_calib[0]}  # X偏移
WRITE:0x83c01034=${post_calib[1]}-${pre_calib[1]}  # Y偏移
WRITE:0x83c01038=${post_calib[2]}-${pre_calib[2]}  # Z偏移
```

### 批量参数配置

```bash
# 备份当前参数
READ:0x83c02000,0x83c02004,0x83c02008→current_params
WRITE:0x83c02100,0x83c02104,0x83c02108=${current_params[0]},${current_params[1]},${current_params[2]}

# 写入新参数
WRITE:0x83c02000=1000
DELAY:50
WRITE:0x83c02004=2000  
DELAY:50
WRITE:0x83c02008=3000

# 验证配置
READ:0x83c02000,0x83c02004,0x83c02008→new_params
COND_WRITE:0x83c02010=1 if ${new_params[0]}==1000 && ${new_params[1]}==2000
```

## 🎨 使用流程

### 传统方式（多个节点）
```
TcpConnect → TcpReadReg → TcpDelay → TcpWriteReg → TcpDisconnect
```

### 新方式（单个节点）
```
TcpConnect → TcpBatchSequence → TcpDisconnect
```

## 💡 最佳实践

1. **使用描述性变量名**：`device_status` 而不是 `s`
2. **添加注释说明**：使用 `#` 解释复杂操作
3. **合理使用延时**：在关键操作后添加延时
4. **分组相关操作**：用注释分隔不同功能块
5. **从简单开始**：先测试基本功能再添加复杂逻辑

## ⚠️ 注意事项

- 地址必须4字节对齐
- 变量必须先定义（READ）后使用
- 范围地址自动按4字节递增
- 支持十六进制（0x）和十进制格式
- 每行一个操作，支持注释

## 🔗 相关节点

| 节点 | 功能 | 使用场景 |
|------|------|----------|
| **TcpConnect** | TCP连接 | 建立设备连接 |
| **TcpBatchSequence** | 智能批量操作 | 复杂操作序列 |
| **TcpReadReg** | 单个读取 | 简单读取操作 |
| **TcpWriteReg** | 单个写入 | 简单写入操作 |
| **TcpDelay** | 延时 | 独立延时需求 |
| **TcpDisconnect** | 断开连接 | 释放连接资源 |

## 🚀 开始使用

1. 在行为树中添加 `TcpBatchSequence` 节点
2. 配置 `tcp_handle` 输入端口
3. 在 `批量配置` 中编写操作序列
4. 设置错误处理策略
5. 运行并查看执行日志

更多详细信息请参考 [详细使用指南](BATCH_OPERATION_GUIDE.md)。
