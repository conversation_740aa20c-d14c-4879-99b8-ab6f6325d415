# 扩展地址格式测试示例

## ✅ 实现完成状态

我已经完成了扩展地址解析系统的实现：

### 🔧 **已实现的组件**

1. **AddressResolver接口** - 地址解析器基类
2. **NumericAddressResolver** - 数值地址解析器（十六进制、十进制、八进制）
3. **SymbolicAddressResolver** - 符号地址解析器（预定义符号映射）
4. **ExpressionAddressResolver** - 表达式地址解析器（算术表达式）
5. **AddressResolverManager** - 解析器管理器（优先级系统）

### 📋 **预定义符号映射**

系统已内置以下符号：

```cpp
symbol_map_["REG_STATUS"]       = 0x83c00100;
symbol_map_["DEVICE_ID"]        = 0x83c00000;
symbol_map_["FIRMWARE_VERSION"] = 0x83c00004;
symbol_map_["CONFIG_BASE"]      = 0x83c00200;
symbol_map_["CHANNEL_BASE"]     = 0x83c01000;
symbol_map_["SENSOR_BASE"]      = 0x83c02000;
symbol_map_["CALIB_CMD"]        = 0x83c01020;
symbol_map_["OFFSET_BASE"]      = 0x83c01030;
symbol_map_["CONFIG_MODE"]      = 0x83c00204;
symbol_map_["DEVICE_TYPE"]      = 0x83c00008;
```

### 🚀 **支持的地址格式**

#### 1. 数值地址（完全兼容现有格式）
```bash
# 十六进制
READ:0x83c00100→status
READ:0X83C00100→status

# 十进制
READ:2214592768→status

# 八进制
READ:020340000400→status
```

#### 2. 符号地址（新功能）
```bash
# 单个符号
READ:REG_STATUS→status
READ:DEVICE_ID→device_id

# 符号范围
READ:CONFIG_BASE-CONFIG_BASE+0x20→config_block

# 符号组合
READ:REG_STATUS,DEVICE_ID,FIRMWARE_VERSION→device_info
```

#### 3. 表达式地址（新功能）
```bash
# 加法表达式
READ:CONFIG_BASE+0x100→extended_config
READ:SENSOR_BASE+CHANNEL_ID*16→channel_data

# 减法表达式
READ:SENSOR_BASE-4→previous_sensor

# 乘法表达式
READ:CHANNEL_BASE+CHANNEL_ID*0x10→channel_register
```

#### 4. 混合格式（新功能）
```bash
# 数值+符号+表达式混合
READ:0x83c00100,REG_STATUS,CONFIG_BASE+0x100→mixed_data

# 范围表达式
READ:REG_STATUS-REG_STATUS+0x20→status_block
READ:CONFIG_BASE-CONFIG_BASE+0x40→config_range
```

### 🎯 **实际使用示例**

#### 示例1：设备初始化（使用符号地址）
```bash
# 使用符号地址，更易读易维护
READ:DEVICE_ID→device_id
READ:FIRMWARE_VERSION→fw_version
DELAY:100

# 根据设备类型配置
COND_WRITE:CONFIG_MODE=1 if ${device_id}==0x1234
COND_WRITE:CONFIG_MODE=2 if ${device_id}==0x5678

# 写入版本标志
WRITE:CONFIG_BASE+4=${fw_version}+1000
```

#### 示例2：多通道传感器（使用表达式）
```bash
# 动态计算通道地址
READ:CHANNEL_BASE+0*16→ch0_data
READ:CHANNEL_BASE+1*16→ch1_data
READ:CHANNEL_BASE+2*16→ch2_data

# 或者使用范围
READ:CHANNEL_BASE-CHANNEL_BASE+0x30→all_channels
```

#### 示例3：传感器校准（混合格式）
```bash
# 校准前数据（使用符号+表达式）
READ:SENSOR_BASE-SENSOR_BASE+0x8→pre_calib
WRITE:CALIB_CMD=1
DELAY:2000

# 校准后数据
READ:SENSOR_BASE-SENSOR_BASE+0x8→post_calib

# 计算偏移（表达式计算）
WRITE:OFFSET_BASE=${post_calib[0]}-${pre_calib[0]}
WRITE:OFFSET_BASE+4=${post_calib[1]}-${pre_calib[1]}
WRITE:OFFSET_BASE+8=${post_calib[2]}-${pre_calib[2]}
```

### 🔄 **向后兼容性**

- ✅ **完全兼容**：所有现有的数值地址格式继续正常工作
- ✅ **无缝升级**：现有配置无需修改
- ✅ **优先级保证**：数值解析器优先级最高，确保现有行为不变

### 🎨 **解析器优先级**

1. **NumericAddressResolver** (优先级 10) - 最高优先级
   - 处理：`0x83c00100`, `2214592768`, `020340000400`

2. **SymbolicAddressResolver** (优先级 20) - 中等优先级
   - 处理：`REG_STATUS`, `DEVICE_ID`, `CONFIG_BASE`

3. **ExpressionAddressResolver** (优先级 30) - 最低优先级
   - 处理：`BASE+0x100`, `OFFSET*2+4`, `REG_BASE-4`

### 🔧 **扩展方法**

#### 添加新符号
```cpp
// 在代码中添加
auto& manager = AddressResolverManager::getInstance();
auto symbolic_resolver = std::make_shared<SymbolicAddressResolver>();
symbolic_resolver->addSymbol("NEW_REGISTER", 0x83c00300);
manager.addResolver(symbolic_resolver, 20);
```

#### 创建自定义解析器
```cpp
class CustomAddressResolver : public AddressResolver {
public:
    bool resolveAddress(const std::string& addr_str, uint32_t& out_address) override {
        // 自定义解析逻辑
        if (addr_str.find("CUSTOM_") == 0) {
            // 处理自定义格式
            return parseCustomFormat(addr_str, out_address);
        }
        return false;
    }
    
    bool isValidAddress(uint32_t address) override {
        return true; // 或自定义验证逻辑
    }
    
    std::string getName() const override {
        return "CustomAddressResolver";
    }
};
```

### 🎯 **使用建议**

1. **新项目**：推荐使用符号地址，提高可读性和维护性
2. **现有项目**：可以渐进式迁移，先在新功能中使用符号地址
3. **复杂项目**：使用表达式地址处理动态地址计算
4. **团队协作**：建立统一的符号命名规范

### 📝 **配置示例对比**

#### 传统方式（纯数值）
```bash
READ:0x83c00100,0x83c00104,0x83c00108→device_info
DELAY:100
WRITE:0x83c00200=1
WRITE:0x83c00204=${device_info[0]}+1
```

#### 新方式（符号+表达式）
```bash
READ:REG_STATUS,DEVICE_ID,FIRMWARE_VERSION→device_info
DELAY:100
WRITE:CONFIG_MODE=1
WRITE:CONFIG_BASE+4=${device_info[0]}+1
```

### 🚀 **总结**

通过这个扩展实现，TCP通信插件现在支持：

- ✅ **数值地址**：十六进制、十进制、八进制（完全兼容）
- ✅ **符号地址**：预定义符号映射（提高可读性）
- ✅ **表达式地址**：算术表达式计算（动态地址）
- ✅ **混合格式**：所有格式的组合使用
- ✅ **扩展接口**：支持自定义解析器

这个实现既解决了您提出的地址格式限制问题，又保持了完全的向后兼容性，为未来的扩展需求提供了强大的基础架构！
