﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef COMMON_TYPES_H
#define COMMON_TYPES_H

#include <QtCore/QObject>
#include <behaviortree_cpp/tree_node.h>
#include <rsfsc_log/rsfsc_log_macro.h>
#include <string>
#include <string_view>

namespace robosense::lidar
{

constexpr static const char* G_MIME_TYPE = "robosense/test_flow_node";

struct NodeInfo
{
  std::string node_type;     // 节点类型标识符
  std::string display_name;  // 显示名称
  std::string description;   // 描述信息
  std::string category;      // 节点类别

  // 默认构造函数
  NodeInfo() = default;

  // 完整构造函数 - 使用string_view减少拷贝
  NodeInfo(std::string_view _node_type,
           std::string_view _display_name,
           std::string_view _description,
           std::string_view _category) :
    node_type(_node_type), display_name(_display_name), description(_description), category(_category)
  {}

  // 简化构造函数 - 不需要描述
  NodeInfo(std::string_view _node_type, std::string_view _display_name, std::string_view _category) :
    node_type(_node_type), display_name(_display_name), category(_category)
  {}

  // 移动构造函数 - 使用std::move避免拷贝
  NodeInfo(std::string&& _node_type, std::string&& _display_name, std::string&& _description, std::string&& _category) :
    node_type(std::move(_node_type)),
    display_name(std::move(_display_name)),
    description(std::move(_description)),
    category(std::move(_category))
  {}

  // 移动构造函数 - 简化版本
  NodeInfo(std::string&& _node_type, std::string&& _display_name, std::string&& _category) :
    node_type(std::move(_node_type)), display_name(std::move(_display_name)), category(std::move(_category))
  {}
};
struct NodeInfos
{
  std::string plugin_name;
  std::vector<NodeInfo> nodes;
};

struct Vector4D
{
  double w { 0.0 };
  double x { 0.0 };
  double y { 0.0 };
  double z { 0.0 };
};

inline void toJson(nlohmann::json& _dest, const Vector4D& _pose)
{
  _dest["w"] = _pose.w;
  _dest["x"] = _pose.x;
  _dest["y"] = _pose.y;
  _dest["z"] = _pose.z;
}

}  // namespace robosense::lidar
namespace BT
{

template <>
inline robosense::lidar::Vector4D convertFromString(BT::StringView _str)
{
  const auto parts = BT::splitString(_str, ',');
  if (parts.size() != 4)
  {
    throw BT::RuntimeError("invalid input)");
  }

  robosense::lidar::Vector4D output {};
  output.w = BT::convertFromString<double>(parts[0]);
  output.x = BT::convertFromString<double>(parts[1]);
  output.y = BT::convertFromString<double>(parts[2]);
  output.z = BT::convertFromString<double>(parts[3]);
  return output;
}

// 添加对单个 uint32_t 的十六进制支持
template <>
inline uint32_t convertFromString(BT::StringView _str)
{
  std::string str_value(_str.data(), _str.size());
  LOG_DEBUG("convertFromString<uint32_t> input: [{}]", str_value);

  try
  {
    // 直接使用 std::stoul 处理，base=0 会自动检测进制
    // 如果字符串以 0x 开头，它会被解析为十六进制
    uint32_t value = std::stoul(str_value, nullptr, 0);
    LOG_DEBUG("Converted string [{}] to uint32_t: {:#x}", str_value, value);
    return value;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("Failed to convert string [{}]: {}", str_value, e.what());
    throw BT::RuntimeError(BT::StrCat("Can't convert string [", _str, "] to uint32_t: ", e.what()));
  }
}

template <>
inline std::vector<uint32_t> convertFromString(BT::StringView _str)
{
  std::vector<uint32_t> output;
  LOG_DEBUG("convertFromString<std::vector<uint32_t>> input: [{}]", std::string(_str.data(), _str.size()));

  // 处理空字符串的情况
  if (_str.empty())
  {
    LOG_DEBUG("Empty input string, returning empty vector");
    return output;
  }

  // 检查是否有逗号，如果没有，则视为单个值
  if (_str.find(',') == BT::StringView::npos)
  {
    try
    {
      // 直接使用 std::stoul 处理单个值
      std::string str_value(_str.data(), _str.size());
      uint32_t value = std::stoul(str_value, nullptr, 0);
      output.push_back(value);
      LOG_DEBUG("Single value converted: [{}] to {:#x}", str_value, value);
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("Failed to convert single value [{}]: {}", std::string(_str.data(), _str.size()), e.what());
    }
    return output;
  }

  // 多个值的情况，使用逗号分隔
  const auto PARTS = BT::splitString(_str, ',');
  LOG_DEBUG("Split into {} parts: {}", PARTS.size(), fmt::join(PARTS, ", "));

  // 预先分配足够的空间以避免多次重新分配内存
  output.reserve(PARTS.size());

  for (size_t i = 0; i < PARTS.size(); ++i)
  {
    const auto& part = PARTS[i];
    // 跳过空字符串
    if (part.empty())
    {
      LOG_DEBUG("Skipping empty part at index {}", i);
      continue;
    }

    try
    {
      // 直接使用 std::stoul 处理
      std::string part_str(part.data(), part.size());
      uint32_t value = std::stoul(part_str, nullptr, 0);
      output.push_back(value);
      LOG_DEBUG("Part {} converted: [{}] to {:#x}", i, part_str, value);
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("Failed to convert part {} [{}]: {}", i, std::string(part.data(), part.size()), e.what());
    }
  }

  LOG_DEBUG("Final output vector size: {}, values: {}", output.size(), fmt::join(output, ", "));
  return output;
}

}  // namespace BT

Q_DECLARE_METATYPE(robosense::lidar::NodeInfos);

#endif  // COMMON_TYPES_H
