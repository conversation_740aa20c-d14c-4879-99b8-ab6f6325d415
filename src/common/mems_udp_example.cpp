/**
 * @file   mems_udp_example.cpp
 * @brief  MEMSUDP使用示例，展示如何设置特定长度和超时的接收功能
 * <AUTHOR> Assistant
 * @date   2025-07-22
 */

#include "mems_udp.h"
#include <chrono>
#include <iostream>
#include <thread>

using namespace robosense::lidar;

class MemsUdpExample
{
public:
  MemsUdpExample()  = default;
  ~MemsUdpExample() = default;

  /**
     * @brief 示例1：动态调整包长度和超时时间的持续接收
     */
  void exampleDynamicReceiving()
  {
    std::cout << "=== 示例1：动态调整包长度和超时时间的持续接收 ===" << std::endl;

    // 创建MEMSUDP实例，初始长度为128字节
    auto udp_receiver = std::make_unique<MEMSUDP>(128);

    // 注册接收回调 - 这个回调会持续被调用
    udp_receiver->regRecvCallback([](const char* _data) {
      std::cout << "接收到数据包，数据前4字节: ";
      for (int index = 0; index < 4; ++index)
      {
        printf("%02X ", static_cast<unsigned char>(_data[index]));
      }
      std::cout << std::endl;
    });

    // 注册超时回调
    udp_receiver->regTimeoutCallback([]() { std::cout << "接收超时，等待下一个数据包..." << std::endl; });

    // 启动UDP接收
    if (udp_receiver->start("0.0.0.0", 8080))
    {
      std::cout << "UDP接收器在端口8080启动成功，开始持续接收数据" << std::endl;

      // 阶段1：接收128字节的包，超时1秒
      std::cout << "\n阶段1：接收128字节包，超时1秒" << std::endl;
      udp_receiver->setPacketLength(128);
      udp_receiver->setTimeoutMS(1000);
      std::this_thread::sleep_for(std::chrono::seconds(3));

      // 阶段2：动态调整为512字节的包，超时2秒
      std::cout << "\n阶段2：调整为512字节包，超时2秒" << std::endl;
      udp_receiver->setPacketLength(512);
      udp_receiver->setTimeoutMS(2000);
      std::this_thread::sleep_for(std::chrono::seconds(3));

      // 阶段3：再次调整为1024字节的包，超时3秒
      std::cout << "\n阶段3：调整为1024字节包，超时3秒" << std::endl;
      udp_receiver->setPacketLength(1024);
      udp_receiver->setTimeoutMS(3000);
      std::this_thread::sleep_for(std::chrono::seconds(3));

      udp_receiver->stop();
      std::cout << "\nUDP接收器已停止" << std::endl;
    }
    else
    {
      std::cout << "UDP接收器启动失败" << std::endl;
    }
  }

  /**
     * @brief 示例2：超时自动退出功能
     */
  void exampleAutoStopOnTimeout()
  {
    std::cout << "\n=== 示例2：超时自动退出功能 ===" << std::endl;

    // 创建MEMSUDP实例，包长度为512字节，超时时间为2秒
    auto udp_receiver = std::make_unique<MEMSUDP>(512, 2000);

    std::cout << "初始包长度: " << udp_receiver->getPacketLength() << " 字节" << std::endl;
    std::cout << "初始超时时间: " << udp_receiver->getTimeoutMS() << " 毫秒" << std::endl;

    // 启用超时自动退出，连续超时3次后自动停止
    udp_receiver->enableAutoStopOnTimeout(3);
    std::cout << "已启用超时自动退出，最大连续超时次数: 3" << std::endl;

    // 注册回调函数
    udp_receiver->regRecvCallback([](const char* _data) { std::cout << "收到512字节数据包" << std::endl; });

    std::atomic<int> timeout_count { 0 };
    udp_receiver->regTimeoutCallback([&timeout_count]() {
      int count = timeout_count.fetch_add(1) + 1;
      std::cout << "第 " << count << " 次超时触发" << std::endl;
    });

    // 启动接收
    if (udp_receiver->start("0.0.0.0", 8081))
    {
      std::cout << "UDP接收器在端口8081启动成功，等待数据或自动退出..." << std::endl;

      // 等待足够长的时间让超时自动退出生效
      std::this_thread::sleep_for(std::chrono::seconds(10));

      if (udp_receiver->isTimeout())
      {
        std::cout << "检测到超时状态，接收器可能已自动停止" << std::endl;
      }

      udp_receiver->stop();
      std::cout << "UDP接收器已停止" << std::endl;
    }
  }

  /**
     * @brief 示例3：使用阻塞式接收方法
     */
  void exampleBlockingReceive()
  {
    std::cout << "\n=== 示例3：阻塞式接收方法 ===" << std::endl;

    auto udp_receiver = std::make_unique<MEMSUDP>(1024);

    if (udp_receiver->start("0.0.0.0", 8082))
    {
      std::cout << "UDP接收器在端口8082启动成功" << std::endl;

      // 使用阻塞式接收，等待特定长度的数据，超时时间为2秒
      std::vector<char> receive_buffer;
      const std::size_t expected_length      = 256;
      const std::size_t timeout_milliseconds = 2000;

      std::cout << "等待接收 " << expected_length << " 字节数据，超时时间 " << timeout_milliseconds << " 毫秒..."
                << std::endl;

      bool success = udp_receiver->receiveWithTimeout(receive_buffer, expected_length, timeout_milliseconds);

      if (success)
      {
        std::cout << "成功接收到 " << receive_buffer.size() << " 字节数据" << std::endl;

        // 显示前16个字节的数据（如果有的话）
        std::cout << "数据内容（前16字节）: ";
        for (std::size_t index = 0; index < std::min(receive_buffer.size(), static_cast<std::size_t>(16)); ++index)
        {
          printf("%02X ", static_cast<unsigned char>(receive_buffer[index]));
        }
        std::cout << std::endl;
      }
      else
      {
        std::cout << "接收数据失败或超时" << std::endl;
      }

      udp_receiver->stop();
    }
    else
    {
      std::cout << "UDP接收器启动失败" << std::endl;
    }
  }

  /**
     * @brief 示例4：动态调整参数的高级用法
     */
  void exampleAdvancedUsage()
  {
    std::cout << "\n=== 示例4：动态调整参数的高级用法 ===" << std::endl;

    auto udp_receiver = std::make_unique<MEMSUDP>(100);

    // 模拟不同阶段需要不同的包长度和超时设置
    struct ReceivePhase
    {
      std::size_t packet_length_;
      std::size_t timeout_ms_;
      std::string description_;
    };

    std::vector<ReceivePhase> phases = { { 128, 1000, "阶段1：接收小包" },
                                         { 512, 2000, "阶段2：接收中等包" },
                                         { 1024, 3000, "阶段3：接收大包" } };

    if (udp_receiver->start("0.0.0.0", 8083))
    {
      std::cout << "UDP接收器在端口8083启动成功" << std::endl;

      for (const auto& phase : phases)
      {
        std::cout << "\n" << phase.description_ << std::endl;

        // 动态调整参数
        udp_receiver->setPacketLength(phase.packet_length_);
        udp_receiver->setTimeoutMS(phase.timeout_ms_);

        std::cout << "设置包长度: " << udp_receiver->getPacketLength() << " 字节" << std::endl;
        std::cout << "设置超时时间: " << udp_receiver->getTimeoutMS() << " 毫秒" << std::endl;

        // 等待一段时间观察效果
        std::this_thread::sleep_for(std::chrono::seconds(2));
      }

      udp_receiver->stop();
      std::cout << "\nUDP接收器已停止" << std::endl;
    }
  }

  /**
     * @brief 运行所有示例
     */
  void runAllExamples()
  {
    exampleDynamicReceiving();
    exampleAutoStopOnTimeout();
    exampleBlockingReceive();
    exampleAdvancedUsage();
  }
};

int main()
{
  std::cout << "MEMSUDP 增强功能使用示例" << std::endl;
  std::cout << "=========================" << std::endl;

  MemsUdpExample example_runner;
  example_runner.runAllExamples();

  std::cout << "\n所有示例运行完成！" << std::endl;
  return 0;
}
