﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "data_serializer.h"

#include <unordered_map>
#include <unordered_set>
#include <vector>

#include <boost/algorithm/cxx11/all_of.hpp>

#include <QApplication>
#include <QBuffer>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QStringList>
#include <QVariantMap>
#include <QXmlStreamWriter>

#include "QtNodes/internal/Definitions.hpp"
#include "behaviortree_cpp/basic_types.h"
#include "behaviortree_cpp/contrib/json.hpp"
#include "behaviortree_cpp/xml_parsing.h"
#include "node_models/abs_behavior_tree.h"
#include "node_models/bt_node_model.h"
#include "node_models/node_manager.h"
#include "node_utils.h"
#include "rs_expected.h"

#include <boost/algorithm/string.hpp>
#include <boost/range/algorithm/sort.hpp>
#include <rsfsc_log/fmt/magic_enum.hpp>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

// PIMPL 实现类
class DataSerializer::Impl
{
public:
  Impl() = default;

  QtNodes::DataFlowGraphicsScene* scene_impl { nullptr };
  QtNodes::DataFlowGraphModel* graph_model { nullptr };
  PropertyView* property_view { nullptr };
  std::unordered_set<QtNodes::NodeId> visited_nodes;
  std::unordered_set<QtNodes::NodeId> all_node_id_in_model;

  static constexpr const char* UNIFIED_VERSION = "1.0";

  struct LoadResult
  {
    bool xml_valid { false };
    bool layout_valid { false };
    QString xml_content;
    nlohmann::ordered_json layout_data;
    nlohmann::ordered_json parameters;
  };

  Expected<std::string, std::string> validateAndGenerateXml(const QString& _path);
  bool saveToUnifiedFile(const QString& _file_path);
  bool loadFromUnifiedFile(const QString& _file_path);
  static bool validateXmlData(const QString& _xml_content);
  static bool validateLayoutData(const nlohmann::ordered_json& _layout_data);
  static std::optional<LoadResult> parseAndValidateFile(const QString& _file_path);
  static Expected<void, std::string> loadXmlData(const QString& _xml_content);
  Expected<void, std::string> loadLayoutData(const nlohmann::ordered_json& _layout_data) const;
  Expected<void, std::string> loadParameters(const nlohmann::ordered_json& _parameters) const;
  Expected<void, std::string> recursiveTraversal(QtNodes::NodeId _node_id,
                                                 QXmlStreamWriter& _writer,
                                                 std::unordered_map<std::string, BT::NodeType>& _all_nodes);
  std::vector<QtNodes::NodeId> getSortFromOutPort(const std::unordered_set<QtNodes::ConnectionId>& _out_ports);
  static bool checkNodeTypeOutPort(const BT::NodeType _type, std::size_t _out_ports);
};

// DataSerializer 类的实现
DataSerializer::DataSerializer() : pimpl_(new Impl()) {}

DataSerializer::~DataSerializer() { delete pimpl_; }

void DataSerializer::setDataFlow(QtNodes::DataFlowGraphicsScene* _scene_impl, QtNodes::DataFlowGraphModel* _graph_model)
{
  pimpl_->scene_impl  = _scene_impl;
  pimpl_->graph_model = _graph_model;
}

void DataSerializer::setProperty(PropertyView* _property_view) { pimpl_->property_view = _property_view; }

Expected<std::string, std::string> DataSerializer::validateAndGenerateXml(const QString& _path)
{
  return pimpl_->validateAndGenerateXml(_path);
}

bool DataSerializer::saveToUnifiedFile(const QString& _file_path) { return pimpl_->saveToUnifiedFile(_file_path); }

bool DataSerializer::loadFromUnifiedFile(const QString& _file_path) { return pimpl_->loadFromUnifiedFile(_file_path); }

Expected<std::string, std::string> DataSerializer::saveProperty()
{
  if (pimpl_->property_view == nullptr)
  {
    return makeError("property_view is null");
  }

  std::string json_str;
  if (!pimpl_->property_view->saveToOrderedJson(json_str))
  {
    return makeError("Failed to save property to JSON");
  }

  return json_str;
}

// 主要功能：验证并生成XML (DFS)
Expected<std::string, std::string> DataSerializer::Impl::validateAndGenerateXml(const QString& _path)
{
  visited_nodes.clear();
  all_node_id_in_model = graph_model->allNodeIds();  // 获取所有 NodeId

  // 1. 查找根节点 (现在返回 NodeId)
  auto root_node_id = Utils::findRoot(graph_model);
  if (!root_node_id.has_value())
  {
    return makeError(root_node_id.error());
  }

  // 2. 初始化 XML 写入器
  QByteArray byte_array;
  QBuffer buffer(&byte_array);
  buffer.open(QIODevice::WriteOnly);  // 以写入模式打开缓冲区

  QXmlStreamWriter xml_writer(&buffer);
  xml_writer.setAutoFormatting(true);  // 让 XML 具有格式化缩进
  xml_writer.writeStartElement("root BTCPP_format=\"4\"");
  xml_writer.writeStartElement("BehaviorTree ID=\"MainTree\"");
  std::unordered_map<std::string, BT::NodeType> register_node;

  // 3. 递归遍历和验证 (从root开始)
  auto success = recursiveTraversal(root_node_id.value(), xml_writer, register_node);

  // 4. 结束 XML 写入
  xml_writer.writeEndElement();
  xml_writer.writeEndDocument();

  if (!success.has_value())
  {
    return makeError(fmt::format("递归过程中发生错误, {}", success.error()));
  }

  // 5. 检查是否有未访问的节点 (游离节点)
  if (visited_nodes.size() != all_node_id_in_model.size())
  {
    QStringList dangling_nodes;
    for (QtNodes::NodeId _node_id : all_node_id_in_model)
    {
      if (visited_nodes.find(_node_id) == visited_nodes.end())
      {
        dangling_nodes << QString("ID: %1").arg(_node_id);
      }
    }
    return makeError(
      fmt::format("Validation Error: Found dangling nodes (not connected to the root): {}", dangling_nodes.join(", ")));
  }

  auto xml_output = std::string(byte_array.data());
  LOG_INFO("节点模型解析XML : {}", xml_output);

  try
  {
    BT::VerifyXML(xml_output, register_node);
  }
  catch (const std::exception& e)
  {
    return makeError(fmt::format("XML 解析出错: {}", std::string(e.what())));
  }
  catch (...)
  {
    LOG_ERROR("XML 解析时发生未知错误！");
    throw;
  }

  // 只有当路径不为空时才保存文件
  if (!_path.isEmpty())
  {
    QFile file(_path);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
      file.write(byte_array);
      file.close();
    }
    else
    {
      LOG_WARN("无法写入XML文件: {}", _path);
    }
  }

  return xml_output;
}

bool DataSerializer::Impl::saveToUnifiedFile(const QString& _file_path)
{
  if (_file_path.isEmpty())
  {
    LOG_ERROR("保存文件路径为空");
    return false;
  }

  // 1. 创建统一的JSON对象
  nlohmann::ordered_json unified_json;
  unified_json["version"] = UNIFIED_VERSION;  // 更新版本号

  // 2. 获取XML数据 (即使为空也保存)
  // 传递空字符串，避免在validateAndGenerateXml中写入文件
  auto bt_xml = validateAndGenerateXml("");
  if (!bt_xml.has_value())
  {
    LOG_WARN("警告: 目前视图生成的树结构bt_xml未完成, 将保存的是未完成的树结构, {}", bt_xml.error());
    unified_json["bt_xml"] = "";
  }
  else
  {
    unified_json["bt_xml"] = bt_xml.value();
  }

  // 3. 获取QtNodes的JSON数据
  QJsonObject nodes_json = graph_model->save();
  if (nodes_json.isEmpty())
  {
    LOG_ERROR("警告: 节点布局数据为空");
    return false;
  }

  QJsonDocument nodes_doc(nodes_json);
  std::string nodes_str = nodes_doc.toJson(QJsonDocument::Compact).toStdString();
  try
  {
    unified_json["nodes_layout"] = nlohmann::ordered_json::parse(nodes_str);
  }
  catch (const nlohmann::json::exception& e)
  {
    LOG_ERROR("解析节点布局数据失败: {}", e.what());
    return false;
  }

  // 4. 预留参数配置字段
  std::string param_data;
  if (!property_view->saveToOrderedJson(param_data))
  {
    return false;
  }

  try
  {
    unified_json["parameters"] = nlohmann::ordered_json::parse(param_data);
  }
  catch (const nlohmann::json::exception& e)
  {
    LOG_ERROR("解析参数配置数据失败: {}", e.what());
    return false;
  }

  // 5. 将JSON写入文件
  QFile file(_file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("无法写入文件: {}", _file_path);
    return false;
  }

  std::string json_str = unified_json.dump(2);  // 使用2个空格缩进，使JSON更易读
  file.write(json_str.c_str(), static_cast<qint64>(json_str.size()));
  file.close();

  LOG_INFO("工程文件已保存到:{}", _file_path);
  return true;
}

bool DataSerializer::Impl::validateXmlData(const QString& _xml_content)
{
  if (_xml_content.isEmpty())
  {
    LOG_INFO("XML数据为空，将加载未完成的行为树");
    return true;
  }

  try
  {
    auto tree = NodeManager::getInstance().btFactory()->createTreeFromText(_xml_content.toStdString());
    LOG_INFO("行为树XML数据验证成功");
    return true;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("XML数据验证失败: {}", std::string(e.what()));
    return false;
  }
}

bool DataSerializer::Impl::validateLayoutData(const nlohmann::ordered_json& /*_layout_data*/)
{
  // TODO
  return true;
}

std::optional<DataSerializer::Impl::LoadResult> DataSerializer::Impl::parseAndValidateFile(const QString& _file_path)
{
  // 读取文件
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("无法打开文件:{}", _file_path);
    return std::nullopt;
  }

  QByteArray file_data = file.readAll();
  file.close();

  try
  {
    nlohmann::ordered_json unified_json = nlohmann::ordered_json::parse(file_data.toStdString());

    // 验证版本
    if (!unified_json.contains("version"))
    {
      LOG_ERROR("文件格式不正确，缺少版本字段");
      return std::nullopt;
    }
    LOG_INFO("加载工程文件版本: {}", unified_json["version"].get<std::string>());

    LoadResult result;

    // 验证XML数据
    if (unified_json.contains("bt_xml"))
    {
      QString xml        = QString::fromStdString(unified_json["bt_xml"].get<std::string>());
      result.xml_valid   = validateXmlData(xml);
      result.xml_content = xml;
    }

    // 验证布局数据
    if (unified_json.contains("nodes_layout"))
    {
      result.layout_valid = validateLayoutData(unified_json["nodes_layout"]);
      result.layout_data  = unified_json["nodes_layout"];
    }

    // 获取参数配置
    if (unified_json.contains("parameters"))
    {
      result.parameters = unified_json["parameters"];
    }

    if (!result.xml_valid && !result.layout_valid)
    {
      LOG_ERROR("验证行为树数据和节点布局数据异常");
      return std::nullopt;
    }

    return result;
  }
  catch (const nlohmann::json::exception& e)
  {
    LOG_ERROR("解析JSON失败: {}", e.what());
    return std::nullopt;
  }
}

Expected<void, std::string> DataSerializer::Impl::loadXmlData(const QString& _xml_content)
{
  if (_xml_content.isEmpty())
  {
    return {};  // 返回成功
  }

  try
  {
    NodeManager::getInstance().btFactory()->registerBehaviorTreeFromText(_xml_content.toStdString());
    // TODO: 添加转换行为树的逻辑
    // convertFromBehaviorTree(tree);
    LOG_INFO("成功加载行为树XML数据 : {}", _xml_content);
    return {};  // 返回成功
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("从XML加载行为树失败: {}", std::string(e.what()));
    return makeError(fmt::format("从XML加载行为树失败: {}", std::string(e.what())));
  }
}

Expected<void, std::string> DataSerializer::Impl::loadLayoutData(const nlohmann::ordered_json& _layout_data) const
{
  if (_layout_data.empty())
  {
    LOG_WARN("加载节点布局数据:为空");
    return {};  // 返回成功
  }

  // 将nlohmann::ordered_json转换为QJsonObject (因为graph_model->load需要QJsonObject)
  std::string layout_str = _layout_data.dump();
  LOG_DEBUG("加载节点布局数据: {}", layout_str);

  QJsonDocument doc      = QJsonDocument::fromJson(QByteArray::fromStdString(layout_str));
  QJsonObject layout_obj = doc.object();

  try
  {
    graph_model->load(layout_obj);

    LOG_INFO("成功加载节点布局数据");
    return {};  // 返回成功
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("从JSON加载节点布局失败: {}", std::string(e.what()));

    // 打印所有已注册的模型名称
    auto registry                 = graph_model->dataModelRegistry();
    auto creators                 = registry->registeredModelCreators();
    std::string registered_models = "已注册的模型: ";
    for (const auto& pair : creators)
    {
      registered_models += pair.first.toStdString() + ", ";
    }

    if (!creators.empty())
    {
      registered_models.pop_back();  // 移除最后的逗号和空格
      registered_models.pop_back();
    }

    LOG_ERROR("{}", registered_models);
    return makeError(fmt::format("从JSON加载节点布局失败: {}", std::string(e.what())));
  }
}

Expected<void, std::string> DataSerializer::Impl::loadParameters(const nlohmann::ordered_json& _parameters) const
{
  if (_parameters.empty())
  {
    return makeError("加载参数配置数据失败, _parameters is empty");
  }

  if (property_view == nullptr)
  {
    return makeError("加载参数配置数据失败, property_view is null");
  }

  std::string json_str = _parameters.dump(2);
  LOG_INFO("DataSerializer::loadParameters: 加载参数配置数据: {}", json_str);

  try
  {
    // 创建一个包含完整结构的JSON对象
    nlohmann::ordered_json full_json;
    full_json["parameters"] = _parameters;  // 确保parameters字段存在

    std::string ordered_json_str = full_json.dump(2);

    if (!property_view->loadFromOrderedJson(ordered_json_str))
    {
      return makeError("加载参数配置数据失败");
    }
  }
  catch (const nlohmann::json::exception& e)
  {
    LOG_ERROR("解析参数配置数据失败: {}", e.what());
    return makeError(fmt::format("解析参数配置数据失败: {}", e.what()));
  }

  // 强制刷新UI
  if (property_view->model() != nullptr)
  {
    LOG_INFO("DataSerializer::loadParameters: 强制刷新UI");
    property_view->refreshUi();

    // 强制发出模型变化的信号
    Q_EMIT property_view->model()->signalModelChanged();

    // 强制更新
    QApplication::processEvents();
  }

  return {};  // 返回成功
}

bool DataSerializer::Impl::loadFromUnifiedFile(const QString& _file_path)
{
  try
  {
    auto load_result = parseAndValidateFile(_file_path);
    if (!load_result)
    {
      return false;
    }

    scene_impl->clearScene();

    Executor<std::string> executor;

    // 添加参数加载操作
    executor.addTask([&]() { return loadParameters(load_result->parameters); })
      .addTaskIf(load_result->xml_valid, [&]() { return loadXmlData(load_result->xml_content); })
      .addTaskIf(load_result->layout_valid, [&]() { return loadLayoutData(load_result->layout_data); });

    // 执行所有操作
    executor.execute();

    // 检查所有操作是否都成功
    const auto& operations = executor.getOperations();
    bool all_success       = boost::algorithm::all_of(operations, [](const auto& operation) {
      if (!operation)
      {
        LOG_ERROR("加载错误: {}", operation.error());
      }
      return operation.has_value();
          });

    // 如果有操作失败，返回false
    if (!all_success)
    {
      LOG_ERROR("加载过程中有操作失败，返回false");
      return false;
    }

    // 如果PropertyView存在，发出模型变化的信号
    if (property_view != nullptr && property_view->model() != nullptr)
    {
      LOG_INFO("DataSerializer::loadFromUnifiedFile: 发出模型变化信号");
      Q_EMIT property_view->model()->signalModelChanged();
    }

    return true;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("加载过程中发生错误: {}", std::string(e.what()));
    return false;
  }
  catch (...)
  {
    LOG_ERROR("加载过程中发生未知错误");
    return false;
  }
}

Expected<void, std::string> DataSerializer::Impl::recursiveTraversal(
  QtNodes::NodeId _node_id,
  QXmlStreamWriter& _writer,
  std::unordered_map<std::string, BT::NodeType>& _all_nodes)
{
  if (_node_id == QtNodes::InvalidNodeId)
  {
    return makeError(fmt::format("recursiveTraversal : 无效 node id : ", _node_id));
  }

  // 检查循环 (使用 NodeId)
  if (visited_nodes.count(_node_id) != 0U)
  {
    return makeError(fmt::format("Validation Error: Cycle detected involving node ID {}.", _node_id));
  }
  visited_nodes.insert(_node_id);

  // 获取节点模型和类型
  auto* bt_node = graph_model->delegateModel<BtNodeModel>(_node_id);

  if (bt_node == nullptr)
  {
    return makeError(fmt::format("Validation Error: Node ID {} has an incompatible or null data model.", _node_id));
  }

  std::shared_ptr<NodeModel> model = bt_node->model();  // 显式指定类型
  if (!model || model->registration_id != AbsBehaviorTree::getRootId())
  {
    _all_nodes[model->registration_id.toStdString()] = bt_node->nodeType();
    // 开始写入当前节点的 XML
    _writer.writeStartElement(model->registration_id);

    // 添加节点 ID 和其他通用属性
    auto port_mapping = bt_node->getCurrentPortMapping();
    const auto& items = model->property_model->properties();
    for (const auto& item : items)
    {
      std::vector<std::string> parts;

      boost::split(parts, item.first, boost::is_any_of("."));
      if (!parts.empty() &&
          (parts.front() == "Input Ports" || parts.front() == "Output Ports" || parts.front() == "唯一名称"))
      {
        _writer.writeAttribute(QString::fromStdString(parts.back()), item.second->value().toString());
      }
    }
  }

  // 获取并验证子节点
  std::vector<QtNodes::NodeId> children;
  unsigned int num_output_ports = bt_node->nPorts(QtNodes::PortType::Out);  // 使用模型定义的方法获取端口数

  LOG_WARN("num_output_ports : {}, registration_id : {}, nodeType : {}, name : {}", num_output_ports,
           model->registration_id, magic_enum::enum_name(bt_node->nodeType()), bt_node->name());

  for (unsigned int i = 0; i < num_output_ports; ++i)
  {
    // 获取从这个输出端口出发的所有 ConnectionId
    auto out_connections = graph_model->connections(_node_id, QtNodes::PortType::Out, i);

    // 验证out端口数量规则
    if (!checkNodeTypeOutPort(bt_node->nodeType(), out_connections.size()))
    {
      _writer.writeEndElement();  // 关闭当前标签以保持 XML 结构
      return makeError(fmt::format(
        "Validation Error: Node {} type {}, output port {} has multiple out_connections ({}), not allowed in "
        "standard BT.",
        _node_id, bt_node->nodeType(), i, out_connections.size()));
    }

    auto sort_id = getSortFromOutPort(out_connections);
    children.reserve(children.size() + sort_id.size());
    children.insert(children.end(), sort_id.begin(), sort_id.end());
  }

  // 递归访问子节点
  for (QtNodes::NodeId child_node_id : children)
  {
    // 递归调用使用 NodeId
    auto res = recursiveTraversal(child_node_id, _writer, _all_nodes);
    if (!res.has_value())
    {
      _writer.writeEndElement();  // 确保当前标签关闭
      return makeError(res.error());
    }
  }

  // 结束当前节点的 XML
  _writer.writeEndElement();
  return {};
}

bool DataSerializer::Impl::checkNodeTypeOutPort(const BT::NodeType _type, std::size_t _out_ports)
{
  switch (_type)
  {
  case BT::NodeType::ACTION:
  case BT::NodeType::CONDITION: return _out_ports == 0;
  case BT::NodeType::CONTROL: return _out_ports > 0;
  case BT::NodeType::SUBTREE:
  case BT::NodeType::DECORATOR: return _out_ports == 1;
  default: return false;
  }
}

std::vector<QtNodes::NodeId> DataSerializer::Impl::getSortFromOutPort(
  const std::unordered_set<QtNodes::ConnectionId>& _out_ports)
{
  std::vector<QtNodes::NodeId> node_ids;
  for (const auto& conn_id : _out_ports)
  {
    // 从 ConnectionId 结构体中获取 inNodeId
    QtNodes::NodeId input_node_id = conn_id.inNodeId;
    node_ids.push_back(input_node_id);
  }

  boost::range::sort(node_ids, [this](QtNodes::NodeId _param_a, QtNodes::NodeId _param_b) {
    auto pos_a = graph_model->nodeData(_param_a, QtNodes::NodeRole::Position);
    auto pos_b = graph_model->nodeData(_param_b, QtNodes::NodeRole::Position);
    // return pos_a.toPointF().x() < pos_b.toPointF().x();  // X 坐标小的排在前面
    return pos_a.toPointF().y() < pos_b.toPointF().y();  // Y 坐标小的排在前面
  });

  return node_ids;
}

}  // namespace robosense::lidar
