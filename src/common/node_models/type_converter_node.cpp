// 简单的 TypeConverter 节点实现
#include "type_converter_node.h"

namespace robosense::lidar
{

// 简单的实现，避免编译错误
BT::NodeStatus TypeConverterNode::tick()
{
    return BT::NodeStatus::SUCCESS;
}

BT::PortsList TypeConverterNode::providedPorts()
{
    return {
        BT::InputPort<std::string>("input", "输入值"),
        BT::InputPort<std::string>("source_type", "auto", "源数据类型"),
        BT::InputPort<std::string>("target_type", "", "目标数据类型"),
        BT::InputPort<std::string>("format_spec", "", "格式化规格"),
        BT::OutputPort<std::string>("output", "输出值")
    };
}

}  // namespace robosense::lidar
