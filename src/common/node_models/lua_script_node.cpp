#include "lua_script_node.h"
#include <fstream>
#include <iostream>
#include <sstream>

// 包含 Sol2 头文件
#define SOL_ALL_SAFETIES_ON 1
#include "rsfsc_log/rsfsc_log_macro.h"
#include <sol/sol.hpp>

namespace robosense::lidar
{

//=============================================================================
// LuaScriptNode 实现
//=============================================================================

LuaScriptNode::LuaScriptNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config), blackboard_(_config.blackboard), lua_initialized_(false)
{
  initializeLuaEnvironment();
}

BT::PortsList LuaScriptNode::providedPorts()
{
  return { BT::InputPort<std::string>("script", "要执行的Lua脚本代码"),
           BT::InputPort<std::string>("script_file", "要执行的Lua脚本文件路径（可选）"),
           BT::OutputPort<std::string>("result", "脚本执行结果"),
           BT::OutputPort<std::string>("error_message", "错误信息（如果有）") };
}

BT::NodeStatus LuaScriptNode::tick()
{
  std::string script_content;
  std::string script_file;

  // 优先使用script端口，如果没有则尝试script_file
  auto script_result = getInput<std::string>("script");
  if (script_result)
  {
    script_content = script_result.value();
    // 直接执行脚本内容
    return executeLuaScript(script_content);
  }

  auto script_file_result = getInput<std::string>("script_file");
  if (script_file_result)
  {
    script_file = script_file_result.value();
    // 从文件读取脚本
    try
    {
      auto result = lua_state_.script_file(script_file);
      setOutput("result", std::string("Script file executed successfully"));
      return BT::NodeStatus::SUCCESS;
    }
    catch (const std::exception& e)
    {
      std::string error_msg = "Lua script file error: " + std::string(e.what());
      setOutput("error_message", error_msg);
      logMessage(error_msg, "error");
      return BT::NodeStatus::FAILURE;
    }
  }

  std::string error_msg = "No script or script_file provided";
  setOutput("error_message", error_msg);
  logMessage(error_msg, "error");
  return BT::NodeStatus::FAILURE;
}

void LuaScriptNode::initializeLuaEnvironment()
{
  if (lua_initialized_)
  {
    return;
  }

  try
  {
    // 打开Lua标准库
    lua_state_.open_libraries(sol::lib::base, sol::lib::string, sol::lib::math, sol::lib::table, sol::lib::io,
                              sol::lib::os);

    // 设置黑板访问函数
    setupBlackboardAccess();

    lua_initialized_ = true;
    logMessage("Lua environment initialized successfully", "info");
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "Failed to initialize Lua environment: " + std::string(e.what());
    logMessage(error_msg, "error");
    lua_initialized_ = false;
  }
}

void LuaScriptNode::setupBlackboardAccess()
{
  // 绑定黑板访问函数到Lua
  lua_state_["bb_get"] = [this](const std::string& key) -> std::string { return getBlackboardValue(key); };

  lua_state_["bb_set"] = [this](const std::string& key, const std::string& value, const std::string& type) {
    setBlackboardValue(key, value, type);
  };

  lua_state_["bb_has"] = [this](const std::string& key) -> bool { return hasBlackboardKey(key); };

  lua_state_["log"] = [this](const std::string& message, const std::string& level) { logMessage(message, level); };

  // 添加便捷的类型特化函数
  lua_state_["bb_set_int"] = [this](const std::string& key, int value) {
    setBlackboardValue(key, std::to_string(value), "int");
  };

  lua_state_["bb_set_double"] = [this](const std::string& key, double value) {
    setBlackboardValue(key, std::to_string(value), "double");
  };

  lua_state_["bb_set_bool"] = [this](const std::string& key, bool value) {
    setBlackboardValue(key, value ? "true" : "false", "bool");
  };

  lua_state_["bb_get_int"] = [this](const std::string& key) -> int {
    if (!hasBlackboardKey(key))
    {
      return 0;
    }
    try
    {
      return blackboard_->get<int>(key);
    }
    catch (...)
    {
      return 0;
    }
  };

  lua_state_["bb_get_double"] = [this](const std::string& key) -> double {
    if (!hasBlackboardKey(key))
    {
      return 0.0;
    }
    try
    {
      return blackboard_->get<double>(key);
    }
    catch (...)
    {
      return 0.0;
    }
  };

  lua_state_["bb_get_bool"] = [this](const std::string& key) -> bool {
    if (!hasBlackboardKey(key))
    {
      return false;
    }
    try
    {
      return blackboard_->get<bool>(key);
    }
    catch (...)
    {
      return false;
    }
  };

  // 添加BT节点状态常量
  lua_state_["OK"]      = static_cast<int>(BT::NodeStatus::SUCCESS);
  lua_state_["NG"]      = static_cast<int>(BT::NodeStatus::FAILURE);
  lua_state_["RUNNING"] = static_cast<int>(BT::NodeStatus::RUNNING);
}

BT::NodeStatus LuaScriptNode::executeLuaScript(const std::string& _script)
{
  if (!lua_initialized_)
  {
    std::string error_msg = "Lua environment not initialized";
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }

  try
  {
    // 执行Lua脚本
    auto result = lua_state_.script(_script);

    // 检查脚本是否返回了状态值
    if (result.valid())
    {
      sol::object return_value = result;
      if (return_value.is<int>())
      {
        int status_code = return_value.as<int>();
        if (status_code == static_cast<int>(BT::NodeStatus::SUCCESS) ||
            status_code == static_cast<int>(BT::NodeStatus::FAILURE) ||
            status_code == static_cast<int>(BT::NodeStatus::RUNNING))
        {
          setOutput("result", "Script executed with return status: " + std::to_string(status_code));
          return static_cast<BT::NodeStatus>(status_code);
        }
      }
      else if (return_value.is<std::string>())
      {
        std::string result_str = return_value.as<std::string>();
        setOutput("result", result_str);
      }
    }

    setOutput("result", "Script executed successfully");
    return BT::NodeStatus::SUCCESS;
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "Lua script execution error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }
}

std::string LuaScriptNode::getBlackboardValue(const std::string& _key)
{
  if (!blackboard_ || !hasBlackboardKey(_key))
  {
    return "";
  }

  try
  {
    auto any_ptr = blackboard_->getAnyLocked(_key);
    if (any_ptr)
    {
      const auto& any_value = any_ptr.get();

      // 尝试不同的类型转换
      if (any_value->isType<std::string>())
      {
        return any_value->cast<std::string>();
      }
      if (any_value->isType<int>())
      {
        return std::to_string(any_value->cast<int>());
      }
      if (any_value->isType<double>())
      {
        return std::to_string(any_value->cast<double>());
      }
      if (any_value->isType<bool>())
      {
        return any_value->cast<bool>() ? "true" : "false";
      }

      // 对于其他类型，尝试使用字符串转换器
      auto entry = blackboard_->getEntry(_key);
      if (entry && entry->string_converter)
      {
        // 使用 BT::toStr 进行类型转换
        return BT::toStr(*any_value);
      }
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error getting blackboard value for key '" + _key + "': " + e.what(), "error");
  }

  return "";
}

void LuaScriptNode::setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type)
{
  if (!blackboard_)
  {
    logMessage("Blackboard not available", "error");
    return;
  }

  try
  {
    if (_type == "int")
    {
      int int_value = std::stoi(_value);
      blackboard_->set(_key, int_value);
    }
    else if (_type == "double")
    {
      double double_value = std::stod(_value);
      blackboard_->set(_key, double_value);
    }
    else if (_type == "bool")
    {
      bool bool_value = (_value == "true" || _value == "1");
      blackboard_->set(_key, bool_value);
    }
    else  // 默认为字符串
    {
      blackboard_->set(_key, _value);
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error setting blackboard value for key '" + _key + "': " + e.what(), "error");
  }
}

bool LuaScriptNode::hasBlackboardKey(const std::string& _key)
{
  if (!blackboard_)
  {
    return false;
  }

  auto entry = blackboard_->getEntry(_key);
  return entry != nullptr;
}

void LuaScriptNode::logMessage(const std::string& _message, const std::string& _level)
{
  if (_level == "error")
  {
    LOG_ERROR("[LuaScriptNode:{}] : {}", name(), _message);
  }
  else if (_level == "warn")
  {
    LOG_WARN("[LuaScriptNode:{}] : {}", name(), _message);
  }
  else
  {
    LOG_INFO("[LuaScriptNode:{}] : {}", name(), _message);
  }
}

//=============================================================================
// LuaAsyncScriptNode 实现
//=============================================================================

LuaAsyncScriptNode::LuaAsyncScriptNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::StatefulActionNode(_name, _config),
  blackboard_(_config.blackboard),
  lua_initialized_(false),
  script_running_(false)
{
  initializeLuaEnvironment();
}

BT::PortsList LuaAsyncScriptNode::providedPorts()
{
  return { BT::InputPort<std::string>("script", "要执行的Lua脚本代码"),
           BT::InputPort<std::string>("script_file", "要执行的Lua脚本文件路径（可选）"),
           BT::OutputPort<std::string>("result", "脚本执行结果"),
           BT::OutputPort<std::string>("error_message", "错误信息（如果有）") };
}

BT::NodeStatus LuaAsyncScriptNode::onStart()
{
  script_running_ = false;
  current_script_.clear();

  std::string script_content;
  std::string script_file;

  // 获取要执行的脚本
  if (getInput<std::string>("script", script_content))
  {
    current_script_ = script_content;
  }
  else if (getInput<std::string>("script_file", script_file))
  {
    // 从文件读取脚本内容
    try
    {
      std::ifstream file(script_file);
      if (!file.is_open())
      {
        std::string error_msg = "Cannot open script file: " + script_file;
        setOutput("error_message", error_msg);
        logMessage(error_msg, "error");
        return BT::NodeStatus::FAILURE;
      }

      std::stringstream buffer;
      buffer << file.rdbuf();
      current_script_ = buffer.str();
    }
    catch (const std::exception& e)
    {
      std::string error_msg = "Error reading script file: " + std::string(e.what());
      setOutput("error_message", error_msg);
      logMessage(error_msg, "error");
      return BT::NodeStatus::FAILURE;
    }
  }
  else
  {
    std::string error_msg = "No script or script_file provided";
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }

  script_running_ = true;
  logMessage("Starting async Lua script execution", "info");
  return BT::NodeStatus::RUNNING;
}

BT::NodeStatus LuaAsyncScriptNode::onRunning()
{
  if (!script_running_ || current_script_.empty())
  {
    return BT::NodeStatus::FAILURE;
  }

  if (!lua_initialized_)
  {
    std::string error_msg = "Lua environment not initialized";
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }

  try
  {
    // 执行Lua脚本
    auto result = lua_state_.script(current_script_);

    // 检查脚本返回值
    if (result.valid())
    {
      sol::object return_value = result;
      if (return_value.is<int>())
      {
        int status_code = return_value.as<int>();
        if (status_code == static_cast<int>(BT::NodeStatus::SUCCESS) ||
            status_code == static_cast<int>(BT::NodeStatus::FAILURE) ||
            status_code == static_cast<int>(BT::NodeStatus::RUNNING))
        {
          setOutput("result", "Async script executed with return status: " + std::to_string(status_code));
          script_running_ = false;
          return static_cast<BT::NodeStatus>(status_code);
        }
      }
      else if (return_value.is<std::string>())
      {
        std::string result_str = return_value.as<std::string>();
        setOutput("result", result_str);
      }
    }

    setOutput("result", "Async script executed successfully");
    script_running_ = false;
    return BT::NodeStatus::SUCCESS;
  }
  catch (const sol::error& e)
  {
    std::string error_msg = "Lua async script execution error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    script_running_ = false;
    return BT::NodeStatus::FAILURE;
  }
}

void LuaAsyncScriptNode::onHalted()
{
  script_running_ = false;
  current_script_.clear();
  logMessage("Async Lua script execution halted", "info");
}

void LuaAsyncScriptNode::initializeLuaEnvironment()
{
  if (lua_initialized_)
  {
    return;
  }

  try
  {
    // 打开Lua标准库
    lua_state_.open_libraries(sol::lib::base, sol::lib::string, sol::lib::math, sol::lib::table, sol::lib::io,
                              sol::lib::os);

    // 设置黑板访问函数
    setupBlackboardAccess();

    lua_initialized_ = true;
    logMessage("Async Lua environment initialized successfully", "info");
  }
  catch (const sol::error& e)
  {
    std::string error_msg = "Failed to initialize async Lua environment: " + std::string(e.what());
    logMessage(error_msg, "error");
    lua_initialized_ = false;
  }
}

void LuaAsyncScriptNode::setupBlackboardAccess()
{
  // 与同步版本相同的设置
  lua_state_["bb_get"] = [this](const std::string& key) -> std::string { return getBlackboardValue(key); };

  lua_state_["bb_set"] = [this](const std::string& key, const std::string& value, const std::string& type) {
    setBlackboardValue(key, value, type);
  };

  lua_state_["bb_has"] = [this](const std::string& key) -> bool { return hasBlackboardKey(key); };

  lua_state_["log"] = [this](const std::string& message, const std::string& level) { logMessage(message, level); };

  // 类型特化函数
  lua_state_["bb_set_int"] = [this](const std::string& key, int value) {
    setBlackboardValue(key, std::to_string(value), "int");
  };

  lua_state_["bb_set_double"] = [this](const std::string& key, double value) {
    setBlackboardValue(key, std::to_string(value), "double");
  };

  lua_state_["bb_set_bool"] = [this](const std::string& key, bool value) {
    setBlackboardValue(key, value ? "true" : "false", "bool");
  };

  lua_state_["bb_get_int"] = [this](const std::string& key) -> int {
    if (!hasBlackboardKey(key))
    {
      return 0;
    }
    try
    {
      return blackboard_->get<int>(key);
    }
    catch (...)
    {
      return 0;
    }
  };

  lua_state_["bb_get_double"] = [this](const std::string& key) -> double {
    if (!hasBlackboardKey(key))
    {
      return 0.0;
    }
    try
    {
      return blackboard_->get<double>(key);
    }
    catch (...)
    {
      return 0.0;
    }
  };

  lua_state_["bb_get_bool"] = [this](const std::string& key) -> bool {
    if (!hasBlackboardKey(key))
    {
      return false;
    }
    try
    {
      return blackboard_->get<bool>(key);
    }
    catch (...)
    {
      return false;
    }
  };

  // BT节点状态常量
  lua_state_["OK"]      = static_cast<int>(BT::NodeStatus::SUCCESS);
  lua_state_["NG"]      = static_cast<int>(BT::NodeStatus::FAILURE);
  lua_state_["RUNNING"] = static_cast<int>(BT::NodeStatus::RUNNING);
}

// 复用同步版本的辅助函数
std::string LuaAsyncScriptNode::getBlackboardValue(const std::string& _key)
{
  if (!blackboard_ || !hasBlackboardKey(_key))
  {
    return "";
  }

  try
  {
    auto any_ptr = blackboard_->getAnyLocked(_key);
    if (any_ptr)
    {
      const auto& any_value = any_ptr.get();

      if (any_value->isType<std::string>())
      {
        return any_value->cast<std::string>();
      }
      if (any_value->isType<int>())
      {
        return std::to_string(any_value->cast<int>());
      }
      if (any_value->isType<double>())
      {
        return std::to_string(any_value->cast<double>());
      }
      if (any_value->isType<bool>())
      {
        return any_value->cast<bool>() ? "true" : "false";
      }

      auto entry = blackboard_->getEntry(_key);
      if (entry && entry->string_converter)
      {
        // 使用 BT::toStr 进行类型转换
        return BT::toStr(*any_value);
      }
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error getting blackboard value for key '" + _key + "': " + e.what(), "error");
  }

  return "";
}

void LuaAsyncScriptNode::setBlackboardValue(const std::string& _key,
                                            const std::string& _value,
                                            const std::string& _type)
{
  if (!blackboard_)
  {
    logMessage("Blackboard not available", "error");
    return;
  }

  try
  {
    if (_type == "int")
    {
      int int_value = std::stoi(_value);
      blackboard_->set(_key, int_value);
    }
    else if (_type == "double")
    {
      double double_value = std::stod(_value);
      blackboard_->set(_key, double_value);
    }
    else if (_type == "bool")
    {
      bool bool_value = (_value == "true" || _value == "1");
      blackboard_->set(_key, bool_value);
    }
    else
    {
      blackboard_->set(_key, _value);
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error setting blackboard value for key '" + _key + "': " + e.what(), "error");
  }
}

bool LuaAsyncScriptNode::hasBlackboardKey(const std::string& _key)
{
  if (!blackboard_)
  {
    return false;
  }

  auto entry = blackboard_->getEntry(_key);
  return entry != nullptr;
}

void LuaAsyncScriptNode::logMessage(const std::string& _message, const std::string& _level)
{
  if (_level == "error")
  {
    LOG_ERROR("[LuaAsyncScriptNode:{}] : {}", name(), _message);
  }
  else if (_level == "warn")
  {
    LOG_WARN("[LuaAsyncScriptNode:{}] : {}", name(), _message);
  }
  else
  {
    LOG_INFO("[LuaAsyncScriptNode:{}] : {}", name(), _message);
  }
}

}  // namespace robosense::lidar
