#pragma once

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/blackboard.h"
#include <memory>
#include <string>

// 直接包含 Sol2 头文件
#define SOL_ALL_SAFETIES_ON 1
#include <sol/sol.hpp>

namespace robosense::lidar
{

/**
 * @brief Lua脚本节点，支持执行Lua脚本并与BT黑板交互
 * 
 * 这个节点允许用户编写Lua脚本来实现自定义逻辑，
 * 脚本可以访问和修改BehaviorTree的黑板数据。
 */
class LuaScriptNode : public BT::SyncActionNode
{
public:
  /**
   * @brief 构造函数
   * @param _name 节点名称
   * @param _config 节点配置
   */
  LuaScriptNode(const std::string& _name, const BT::NodeConfig& _config);

  LuaScriptNode(const LuaScriptNode&)            = delete;
  LuaScriptNode& operator=(const LuaScriptNode&) = delete;
  LuaScriptNode(LuaScriptNode&&)                 = delete;
  LuaScriptNode& operator=(LuaScriptNode&&)      = delete;

  /**
   * @brief 析构函数
   */
  ~LuaScriptNode() override = default;

  /**
   * @brief 提供端口列表
   * @return 端口列表
   */
  static BT::PortsList providedPorts();

  /**
   * @brief 执行节点逻辑
   * @return 节点状态
   */
  BT::NodeStatus tick() override;

private:
  /**
   * @brief 初始化Lua环境
   */
  void initializeLuaEnvironment();

  /**
   * @brief 设置黑板访问函数
   */
  void setupBlackboardAccess();

  /**
   * @brief 执行Lua脚本
   * @param _script 要执行的脚本内容
   * @return 执行结果（SUCCESS/FAILURE/RUNNING）
   */
  BT::NodeStatus executeLuaScript(const std::string& _script);

  /**
   * @brief 从黑板获取值的Lua函数
   * @param _key 黑板键名
   * @return 值（作为字符串返回）
   */
  std::string getBlackboardValue(const std::string& _key);

  /**
   * @brief 向黑板设置值的Lua函数
   * @param _key 黑板键名
   * @param _value 要设置的值
   * @param _type 值的类型（"int", "double", "string", "bool"）
   */
  void setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type = "string");

  /**
   * @brief 检查黑板中是否存在指定键的Lua函数
   * @param _key 黑板键名
   * @return 是否存在
   */
  bool hasBlackboardKey(const std::string& _key);

  /**
   * @brief 日志输出函数
   * @param _message 日志消息
   * @param _level 日志级别（"info", "warn", "error"）
   */
  void logMessage(const std::string& _message, const std::string& _level = "info");

private:
  sol::state lua_state_;                        // Lua状态机
  std::shared_ptr<BT::Blackboard> blackboard_;  // 黑板引用
  bool lua_initialized_;                        // Lua环境是否已初始化
};

/**
 * @brief Lua脚本异步节点，支持长时间运行的脚本
 */
class LuaAsyncScriptNode : public BT::StatefulActionNode
{
public:
  LuaAsyncScriptNode(const std::string& _name, const BT::NodeConfig& _config);
  ~LuaAsyncScriptNode() override = default;

  LuaAsyncScriptNode(const LuaAsyncScriptNode&)            = delete;
  LuaAsyncScriptNode& operator=(const LuaAsyncScriptNode&) = delete;
  LuaAsyncScriptNode(LuaAsyncScriptNode&&)                 = delete;
  LuaAsyncScriptNode& operator=(LuaAsyncScriptNode&&)      = delete;

  static BT::PortsList providedPorts();

  BT::NodeStatus onStart() override;
  BT::NodeStatus onRunning() override;
  void onHalted() override;

private:
  void initializeLuaEnvironment();
  void setupBlackboardAccess();

  // 与同步版本相同的辅助函数
  std::string getBlackboardValue(const std::string& _key);
  void setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type = "string");
  bool hasBlackboardKey(const std::string& _key);
  void logMessage(const std::string& _message, const std::string& _level = "info");

private:
  sol::state lua_state_;
  std::shared_ptr<BT::Blackboard> blackboard_;
  bool lua_initialized_;
  bool script_running_;
  std::string current_script_;
};

}  // namespace robosense::lidar
