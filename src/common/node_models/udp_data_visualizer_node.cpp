#include "udp_data_visualizer_node.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QApplication>
#include <QDateTime>
#include <QThread>

namespace robosense::lidar {

UdpDataVisualizerNode::UdpDataVisualizerNode(const std::string& _name, const BT::NodeConfig& _config)
  : BT::SyncActionNode(_name, _config)
  , visualization_helper_(std::make_unique<NodeVisualizationHelper>(QString::fromStdString(_name)))
  , data_type_("numeric")
  , widget_type_("NumericTable")
  , visualization_title_("UDP数据可视化")
  , display_duration_(5000)
  , update_interval_(1000)
  , enable_auto_close_(true)
  , enable_data_logging_(false)
  , expected_data_size_(0)
  , data_format_("float32")
  , image_width_(640)
  , image_height_(480)
  , image_format_("grayscale")
  , min_value_(0.0)
  , max_value_(100.0)
  , decimal_places_(2)
  , update_timer_(nullptr)
  , last_update_time_(0)
  , update_counter_(0)
{
  setupVisualizationConfig();
  LOG_INFO("[UdpDataVisualizerNode] 节点初始化完成: {}", _name);
}

UdpDataVisualizerNode::~UdpDataVisualizerNode()
{
  if (update_timer_ != nullptr)
  {
    update_timer_->stop();
    update_timer_->deleteLater();
  }
}

BT::PortsList UdpDataVisualizerNode::providedPorts()
{
  return {
    // 输入端口
    BT::InputPort<std::string>("udp_data", "", "UDP数据（Base64编码或十六进制字符串）"),
    BT::InputPort<std::string>("data_type", "numeric", "数据类型: numeric, image, status"),
    BT::InputPort<std::string>("widget_type", "NumericTable", "可视化组件: NumericTable, NumericGauge"),
    BT::InputPort<std::string>("visualization_title", "UDP数据可视化", "可视化标题"),
    BT::InputPort<int>("display_duration", 5000, "显示时长(毫秒)，0表示持续显示"),
    BT::InputPort<bool>("enable_auto_close", true, "是否自动关闭"),
    
    // 数据解析参数
    BT::InputPort<std::string>("data_format", "float32", "数据格式: float32, int16, uint8"),
    BT::InputPort<double>("min_value", 0.0, "数值最小值"),
    BT::InputPort<double>("max_value", 100.0, "数值最大值"),
    BT::InputPort<int>("decimal_places", 2, "小数位数"),
    
    // 输出端口
    BT::OutputPort<std::string>("result", "执行结果: SUCCESS, FAILURE, INVALID_DATA"),
    BT::OutputPort<std::string>("error_message", "错误信息"),
    BT::OutputPort<int>("processed_data_size", "处理的数据大小"),
    BT::OutputPort<int>("update_count", "更新次数")
  };
}

BT::NodeStatus UdpDataVisualizerNode::tick()
{
  try {
    // 读取输入参数
    auto udp_data_opt = getInput<std::string>("udp_data");
    if (!udp_data_opt || udp_data_opt.value().empty())
    {
      setOutput("result", "FAILURE");
      setOutput("error_message", "UDP数据为空");
      LOG_ERROR("[UdpDataVisualizerNode] UDP数据为空");
      return BT::NodeStatus::FAILURE;
    }

    // 更新配置参数
    setupVisualizationConfig();

    // 转换UDP数据
    QByteArray udp_data = QByteArray::fromStdString(udp_data_opt.value());
    
    // 验证数据
    if (!validateUdpData(udp_data))
    {
      setOutput("result", "INVALID_DATA");
      setOutput("error_message", "UDP数据格式无效");
      LOG_ERROR("[UdpDataVisualizerNode] UDP数据格式无效，大小: {}", udp_data.size());
      return BT::NodeStatus::FAILURE;
    }

    // 根据数据类型进行解析和可视化
    if (data_type_ == "numeric")
    {
      parseNumericData(udp_data);
    }
    else if (data_type_ == "status")
    {
      parseStatusData(udp_data);
    }
    else
    {
      setOutput("result", "FAILURE");
      setOutput("error_message", QString("不支持的数据类型: %1").arg(data_type_).toStdString());
      LOG_ERROR("[UdpDataVisualizerNode] 不支持的数据类型: {}", data_type_.toStdString());
      return BT::NodeStatus::FAILURE;
    }

    // 更新统计信息
    update_counter_++;
    last_update_time_ = QDateTime::currentMSecsSinceEpoch();

    // 设置输出
    setOutput("result", "SUCCESS");
    setOutput("error_message", "");
    setOutput("processed_data_size", udp_data.size());
    setOutput("update_count", update_counter_);

    LOG_DEBUG("[UdpDataVisualizerNode] 数据可视化成功，类型: {}, 大小: {}", 
              data_type_.toStdString(), udp_data.size());

    return BT::NodeStatus::SUCCESS;

  } catch (const std::exception& e) {
    setOutput("result", "FAILURE");
    setOutput("error_message", e.what());
    LOG_ERROR("[UdpDataVisualizerNode] 执行异常: {}", e.what());
    return BT::NodeStatus::FAILURE;
  }
}

void UdpDataVisualizerNode::parseNumericData(const QByteArray& _udp_data)
{
  if (!isValidNumericData(_udp_data))
  {
    throw std::runtime_error("数值数据格式无效");
  }

  QVariantMap numeric_data;

  if (data_format_ == "float32")
  {
    // 解析32位浮点数数组
    const float* float_array = reinterpret_cast<const float*>(_udp_data.constData());
    int float_count = static_cast<int>(_udp_data.size() / sizeof(float));

    for (int i = 0; i < float_count && i < 10; ++i)  // 最多显示10个数值
    {
      QString name = QString("数值%1").arg(i + 1);
      numeric_data[name] = static_cast<double>(float_array[i]);
    }
  }
  else if (data_format_ == "int16")
  {
    // 解析16位整数数组
    const qint16* int_array = reinterpret_cast<const qint16*>(_udp_data.constData());
    int int_count = static_cast<int>(_udp_data.size() / sizeof(qint16));

    for (int i = 0; i < int_count && i < 10; ++i)
    {
      QString name = QString("数值%1").arg(i + 1);
      numeric_data[name] = static_cast<double>(int_array[i]);
    }
  }
  else
  {
    // 默认按字节解析
    for (int i = 0; i < _udp_data.size() && i < 10; ++i)
    {
      QString name = QString("字节%1").arg(i + 1);
      numeric_data[name] = static_cast<double>(static_cast<quint8>(_udp_data[i]));
    }
  }

  // 创建可视化配置
  QVariantMap config = createVisualizationConfig();
  config["min_value"] = min_value_;
  config["max_value"] = max_value_;
  config["decimal_places"] = decimal_places_;

  // 显示数值可视化
  visualization_helper_->showNumericData(numeric_data, config);

  LOG_INFO("[UdpDataVisualizerNode] 显示数值数据: {} 个数值", numeric_data.size());
}

void UdpDataVisualizerNode::parseImageData(const QByteArray& _udp_data)
{
  // 创建可视化配置
  QVariantMap config = createVisualizationConfig();
  config["enable_zoom"] = true;
  config["enable_save"] = true;

  // 显示图像可视化
  visualization_helper_->showImageData(_udp_data, image_width_, image_height_, image_format_, config);

  LOG_INFO("[UdpDataVisualizerNode] 显示图像数据: {}x{}, 格式: {}", 
           image_width_, image_height_, image_format_.toStdString());
}

void UdpDataVisualizerNode::parseStatusData(const QByteArray& _udp_data)
{
  // 将状态数据转换为数值数据进行显示
  QVariantMap status_data;

  // 简单的状态解析示例
  if (_udp_data.size() >= 4)
  {
    const quint8* data = reinterpret_cast<const quint8*>(_udp_data.constData());
    status_data["连接状态"] = data[0] ? 1.0 : 0.0;
    status_data["设备状态"] = data[1] ? 1.0 : 0.0;
    status_data["数据质量"] = static_cast<double>(data[2]);
    status_data["信号强度"] = static_cast<double>(data[3]);
  }

  // 创建可视化配置
  QVariantMap config = createVisualizationConfig();
  config["widget_type"] = "NumericTable";  // 状态数据使用表格显示

  // 显示状态可视化
  visualization_helper_->showNumericData(status_data, config);

  LOG_INFO("[UdpDataVisualizerNode] 显示状态数据: {} 个状态", status_data.size());
}

void UdpDataVisualizerNode::setupVisualizationConfig()
{
  // 读取配置参数
  auto data_type_opt = getInput<std::string>("data_type");
  data_type_ = data_type_opt ? QString::fromStdString(data_type_opt.value()) : "numeric";

  auto widget_type_opt = getInput<std::string>("widget_type");
  widget_type_ = widget_type_opt ? QString::fromStdString(widget_type_opt.value()) : "NumericTable";

  auto title_opt = getInput<std::string>("visualization_title");
  visualization_title_ = title_opt ? QString::fromStdString(title_opt.value()) : "UDP数据可视化";

  auto duration_opt = getInput<int>("display_duration");
  display_duration_ = duration_opt ? duration_opt.value() : 5000;

  auto auto_close_opt = getInput<bool>("enable_auto_close");
  enable_auto_close_ = auto_close_opt ? auto_close_opt.value() : true;

  auto data_format_opt = getInput<std::string>("data_format");
  data_format_ = data_format_opt ? QString::fromStdString(data_format_opt.value()) : "float32";

  auto min_opt = getInput<double>("min_value");
  min_value_ = min_opt ? min_opt.value() : 0.0;

  auto max_opt = getInput<double>("max_value");
  max_value_ = max_opt ? max_opt.value() : 100.0;

  auto decimal_opt = getInput<int>("decimal_places");
  decimal_places_ = decimal_opt ? decimal_opt.value() : 2;
}

QVariantMap UdpDataVisualizerNode::createVisualizationConfig() const
{
  QVariantMap config;
  config["widget_type"] = widget_type_;
  config["title"] = visualization_title_;
  
  if (enable_auto_close_ && display_duration_ > 0)
  {
    config["auto_close_duration"] = display_duration_;
  }

  return config;
}

bool UdpDataVisualizerNode::validateUdpData(const QByteArray& _data) const
{
  return !_data.isEmpty();
}

bool UdpDataVisualizerNode::isValidNumericData(const QByteArray& _data) const
{
  if (data_format_ == "float32")
  {
    return _data.size() % sizeof(float) == 0;
  }
  else if (data_format_ == "int16")
  {
    return _data.size() % sizeof(qint16) == 0;
  }
  
  return true;  // 字节数据总是有效的
}

bool UdpDataVisualizerNode::isValidImageData(const QByteArray& _data) const
{
  int expected_size = image_width_ * image_height_;
  
  if (image_format_ == "rgb" || image_format_ == "bgr")
  {
    expected_size *= 3;  // RGB需要3个字节
  }
  
  return _data.size() >= expected_size;
}

}  // namespace robosense::lidar
