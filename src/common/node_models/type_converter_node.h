#pragma once

#include <behaviortree_cpp/action_node.h>
#include <string>

namespace robosense::lidar
{

class TypeConverterNode : public BT::SyncActionNode
{
public:
    TypeConverterNode(const std::string& _name, const BT::NodeConfig& _config) :
        BT::SyncActionNode(_name, _config)
    {}

    static BT::PortsList providedPorts();

    BT::NodeStatus tick() override;
};

}  // namespace robosense::lidar
