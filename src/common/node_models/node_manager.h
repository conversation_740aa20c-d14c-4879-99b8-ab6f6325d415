﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef NODE_MANAGER_H
#define NODE_MANAGER_H

#include "abs_behavior_tree.h"
#include <QtNodes/NodeDelegateModel>
#include <QtNodes/NodeDelegateModelRegistry>
#include <behaviortree_cpp/bt_factory.h>
#include <functional>
#include <memory>
#include <unordered_map>

namespace robosense::lidar
{

using DataModelBuilder = std::function<std::unique_ptr<QtNodes::NodeDelegateModel>(NodeModel*)>;

struct NodeRegistration
{
  BT::NodeBuilder bt_builder;           // BT TreeNode 构造器
  DataModelBuilder data_model_builder;  // Qt NodeDelegateModel 构造器
  NodeModel node_model;                 // 节点配置信息
};

/**
 * @brief BT TreeNode + NodeDelegateModel 工厂
 *
 */
class NodeManager
{
public:
  using QtNodeRegisterPtr = std::shared_ptr<QtNodes::NodeDelegateModelRegistry>;
  using BTFactoryPtr      = std::shared_ptr<BT::BehaviorTreeFactory>;
  static NodeManager& getInstance();
  NodeManager(const NodeManager&) = delete;
  NodeManager& operator=(const NodeManager&) = delete;
  NodeManager(NodeManager&&)                 = delete;
  NodeManager& operator=(NodeManager&&) = delete;

  QtNodeRegisterPtr qtNodeRegistry();
  BTFactoryPtr btFactory();
  void setNodeModel(const std::string& _id, std::shared_ptr<NodeModel> _model);
  std::shared_ptr<NodeModel> nodeModel(const std::string& _id);

private:
  NodeManager();
  ~NodeManager() = default;

  QtNodeRegisterPtr qt_node_registry_ { nullptr };
  BTFactoryPtr bt_factory_ { nullptr };
  std::unordered_map<std::string, std::shared_ptr<NodeModel>> node_models_;
};

template <typename T>
constexpr void registerQtNodeModel(const std::string& _name, T& _registry)
{
  _registry.registerModel([] { return std::make_unique<T>(); }, _name);
}

// void registerMyPluginNode()
// {
//   NodeModel model;
//   model.registration_id = "MyAction";
//   model.type = NodeType::Action;
//   model.ports = { BT::InputPort<std::string>("target") };

//   BT::NodeBuilder builder = [](const std::string& name, const BT::NodeConfig& config) {
//     return std::make_unique<MyBTNode>(name, config);
//   };

//   BehaviorTreeDataModelFactory ui_factory = [](const NodeModel& model) {
//     return std::make_unique<MyDataModel>(model);  // 你自己的 DataModel 派生类
//   };

//   NodeManager::getInstance().registerNodeType("MyAction", model, builder, ui_factory);
// }

}  // namespace robosense::lidar

#endif  // NODE_MANAGER_H