﻿
/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DEVICE_BT_NODE_H
#define DEVICE_BT_NODE_H

#include "behaviortree_cpp/basic_types.h"
#include "behaviortree_cpp/behavior_tree.h"
#include "mems_tcp.h"
#include "node_plugin_base.h"
#include <behaviortree_cpp/utils/strcat.hpp>
#include <cstdint>
#include <rsfsc_log/rsfsc_log_macro.h>
#include <string>
#include <vector>

using namespace BT;

namespace robosense::lidar
{
const std::string DEVICE_HANDLE_KEY = "handle";

static bool hexStrToUint32(const std::string& _hex_str, uint32_t& _out_value)
{
  try
  {
    _out_value = std::stoul(_hex_str, nullptr, 16);
    return true;
  }
  catch (const std::invalid_argument& e)
  {
    LOG_ERROR("Invalid input: not a hex string: {}", std::string(e.what()));
  }
  catch (const std::out_of_range& e)
  {
    LOG_ERROR("Out of range for uint32_t: {}", std::string(e.what()));
  }
  return false;
}
static bool strToUint32(const std::string& _str, uint32_t& _out_value)
{
  try
  {
    _out_value = std::stoul(_str, nullptr, 10);
    return true;
  }
  catch (const std::invalid_argument& e)
  {
    LOG_ERROR("Invalid input: not a hex string: {}", std::string(e.what()));
  }
  catch (const std::out_of_range& e)
  {
    LOG_ERROR("Out of range for uint32_t: {}", std::string(e.what()));
  }
  return false;
}

class SequenceWithFailureCheck : public BT::ControlNode
{
public:
  SequenceWithFailureCheck(const std::string& name, const BT::NodeConfig& config) : BT::ControlNode(name, config) {}

  static BT::PortsList providedPorts() { return {}; }

  BT::NodeStatus tick() override
  {
    bool any_failed = false;

    for (size_t index = 0; index < childrenCount(); ++index)
    {
      TreeNode* child_node = children_nodes_[index];

      NodeStatus child_status = child_node->executeTick();

      if (child_status == NodeStatus::RUNNING)
      {
        // 当前有子节点在运行，返回 RUNNING（保持行为树一致性）
        return NodeStatus::RUNNING;
      }
      if (child_status == NodeStatus::FAILURE)
      {
        any_failed = true;
      }
    }

    return any_failed ? NodeStatus::FAILURE : NodeStatus::SUCCESS;
  }
};
// --- 打开设备节点 ---
class OpenDevice : public SyncActionNode
{
public:
  OpenDevice(const std::string& name, const NodeConfig& config) : SyncActionNode(name, config) {}

  // 定义输入端口 (设备参数)
  static PortsList providedPorts()
  {
    return { InputPort<std::string>("ip", "ip"), InputPort<uint16_t>("port", "port"),
             OutputPort<std::shared_ptr<MEMSTCP>>("dev_handle", "dev_handle") };
  }

  NodeStatus tick() override
  {
    // 1. 从输入端口获取参数
    // Expected<std::string> params = getInput<std::string>("device_params");
    // if (!params)
    // {
    //   std::cerr << "OpenDevice Error: Missing parameter 'device_params'." << std::endl;
    //   return NodeStatus::FAILURE;
    // }

    // 2. 检查 Blackboard 是否已有句柄 (避免重复打开，或者根据需要决定是否重新打开)
    // auto currentHandleOpt = config().blackboard->get<std::shared_ptr<MEMSTCP>>(DEVICE_HANDLE_KEY);
    // if (currentHandleOpt == nullptr)
    // {
    //   std::cout << "OpenDevice: Device handle already exists and is valid." << std::endl;
    //   // 可选：如果参数不同，可能需要关闭旧的，打开新的
    //   // return NodeStatus::SUCCESS; // 或者根据逻辑决定
    // }

    try
    {
      auto device_handle = std::make_shared<MEMSTCP>();

      Expected<std::string> ip_str = getInput<std::string>("ip");
      Expected<uint16_t> port      = getInput<uint16_t>("port");
      if (!ip_str.has_value() || !port.has_value())
      {
        LOG_ERROR("OpenDevice Error: Missing parameter 'ip' or 'port'.");
        return NodeStatus::FAILURE;
      }

      // 4. 连接设备
      if (!device_handle->connect(ip_str.value(), port.value()))
      {
        LOG_ERROR("OpenDevice Error: Failed to connect to device.");
        return NodeStatus::FAILURE;
      }

      LOG_INFO("OpenDevice : device_handle = {}", static_cast<void*>(device_handle.get()));

      // 5. 将句柄存入 Blackboard
      config().blackboard->set("dev_handle", device_handle);
      LOG_INFO("OpenDevice: Successfully connected and stored handle in blackboard.");
      return NodeStatus::SUCCESS;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("OpenDevice Exception: {}", e.what());
      return NodeStatus::FAILURE;
    }
  }
};

// --- 读取值节点 ---
class ReadValue : public SyncActionNode
{
public:
  ReadValue(const std::string& name, const NodeConfig& config) : SyncActionNode(name, config) {}

  // 输入端口：要读取的寄存器名
  // 输出端口：读取到的值
  static PortsList providedPorts()
  {
    return { InputPort<std::string>("reg_addr", "reg_addr"),
             InputPort<std::shared_ptr<MEMSTCP>>("dev_handle", "dev_handle"),
             BT::OutputPort<std::string>("read_value") };
  }

  NodeStatus tick() override
  {
    Expected<std::shared_ptr<MEMSTCP>> handle_opt = getInput<std::shared_ptr<MEMSTCP>>("dev_handle");
    Expected<std::string> reg_addr_str            = getInput<std::string>("reg_addr");
    if (!handle_opt.has_value() || !reg_addr_str.has_value())
    {
      LOG_ERROR("ReadValue Error: Missing parameter 'dev_handle' or 'reg_addr'.");
      return NodeStatus::FAILURE;
    }
    const std::shared_ptr<MEMSTCP>& device_handle = handle_opt.value();
    LOG_INFO("ReadValue : device_handle = {}", static_cast<void*>(device_handle.get()));

    // Expected<std::string> regName = getInput<std::string>("register");
    // if (!regName)
    // {
    //   std::cerr << "ReadValue Error: Missing parameter 'register'." << std::endl;
    //   return NodeStatus::FAILURE;
    // }

    uint32_t reg_addr { 0x0 };
    if (!hexStrToUint32(reg_addr_str.value(), reg_addr))
    {
      LOG_ERROR("ReadValue Error: Invalid register address.");
      return NodeStatus::FAILURE;
    }

    try
    {
      std::vector<int32_t> data;
      if (!device_handle->readRegData(std::vector<uint32_t> { reg_addr }, data))
      {
        RSFSCLog::getInstance()->error("readRegData error");
        return NodeStatus::FAILURE;
      }

      setOutput<std::string>("read_value", std::to_string(data.at(0)));
      auto read_value = config().blackboard->get<std::string>("read_value");
      LOG_DEBUG("read_value ========================= {}", read_value);

      return NodeStatus::SUCCESS;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("ReadValue Exception: {}", e.what());
      return NodeStatus::FAILURE;
    }
  }
};

// --- 写入值节点 ---
class WriteValue : public SyncActionNode
{
public:
  WriteValue(const std::string& name, const NodeConfig& config) : SyncActionNode(name, config) {}

  // 输入端口：寄存器名，要写入的值
  static PortsList providedPorts()
  {
    return { InputPort<std::shared_ptr<MEMSTCP>>("dev_handle", "dev_handle"),
             InputPort<std::string>("reg_addr", "reg_addr"),
             InputPort<std::string>("reg_write_value", "reg_write_value") };
  }

  NodeStatus tick() override
  {
    // 1. 获取设备句柄 (同 ReadValue)
    Expected<std::shared_ptr<MEMSTCP>> handle_opt = getInput<std::shared_ptr<MEMSTCP>>("dev_handle");
    Expected<std::string> reg_addr_str            = getInput<std::string>("reg_addr");
    Expected<std::string> reg_value_str           = getInput<std::string>("reg_write_value");
    LOG_INFO("get input");
    if (!handle_opt.has_value() || !reg_addr_str.has_value() || !reg_value_str.has_value())
    {
      LOG_ERROR("WriteValue Error: Missing parameter 'dev_handle' or 'reg_addr' or 'reg_write_value'.");
      return NodeStatus::FAILURE;
    }
    const std::shared_ptr<MEMSTCP>& device_handle = handle_opt.value();

    uint32_t reg_addr { 0x0 };
    uint32_t reg_value { 0x0 };
    if (!hexStrToUint32(reg_addr_str.value(), reg_addr) || !strToUint32(reg_value_str.value(), reg_value))
    {
      LOG_ERROR("WriteValue Error: Invalid register address.");
      return NodeStatus::FAILURE;
    }

    try
    {
      // 3. 执行写入操作
      bool success = device_handle->writeRegData(std::vector<uint32_t> { reg_addr },
                                                 std::vector<int32_t> { static_cast<int32_t>(reg_value) });

      // 4. 检查设备驱动返回的错误
      if (!success)
      {
        LOG_ERROR("WriteValue Device Error: Failed to write value to device.");
        return NodeStatus::FAILURE;
      }

      LOG_DEBUG("写入充能值: ------------------> {}", 0x10);
      return NodeStatus::SUCCESS;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("WriteValue Exception: {}", std::string(e.what()));
      return NodeStatus::FAILURE;
    }
  }
};

// 关闭设备节点
class CloseDevice : public SyncActionNode
{
public:
  CloseDevice(const std::string& name, const NodeConfig& config) : SyncActionNode(name, config) {}

  static PortsList providedPorts() { return { InputPort<std::shared_ptr<MEMSTCP>>("dev_handle", "dev_handle") }; }

  NodeStatus tick() override
  {
    LOG_INFO("CloseDevice: Attempting to close device and remove handle.");
    // 从 Blackboard 获取句柄
    Expected<std::shared_ptr<MEMSTCP>> handle_opt = getInput<std::shared_ptr<MEMSTCP>>("dev_handle");

    if (handle_opt && handle_opt.value())
    {
      try
      {
        // 显式断开连接 (虽然析构函数也会做，但显式调用更清晰)
        handle_opt.value()->disconnect();
      }
      catch (const std::exception& e)
      {
        LOG_ERROR("CloseDevice Exception during disconnect: {}", std::string(e.what()));
        return NodeStatus::FAILURE;
      }
    }
    // 从 Blackboard 移除句柄，即使它无效或不存在 (无害操作)
    config().blackboard->unset("dev_handle");
    LOG_INFO("CloseDevice: Successfully closed device and removed handle.");

    return NodeStatus::SUCCESS;  // 通常关闭操作认为是成功的
  }
};

}  // namespace robosense::lidar

#endif  // DEVICE_BT_NODE_H