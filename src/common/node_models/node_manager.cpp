﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "node_manager.h"

#include "json_viewer_node.h"
#include "lua_script_node.h"
#include "node_models/udp_collect_node.h"
#include "node_models/udp_start_node.h"
#include "node_models/udp_stop_node.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "udp_data_parser_node.h"
#include <utility>

namespace robosense::lidar
{

NodeManager& NodeManager::getInstance()
{
  static NodeManager instance;
  return instance;
}

NodeManager::NodeManager()
{
  qt_node_registry_ = std::make_shared<QtNodes::NodeDelegateModelRegistry>();
  bt_factory_       = std::make_shared<BT::BehaviorTreeFactory>();

  // 注册自定义节点到BehaviorTree工厂
  bt_factory_->registerNodeType<LuaScriptNode>("LuaScriptNode");
  bt_factory_->registerNodeType<LuaAsyncScriptNode>("LuaAsyncScriptNode");

  // 注册UDP数据解析节点
  bt_factory_->registerNodeType<UdpCollectNode>("UdpCollectNode");
  bt_factory_->registerNodeType<UdpStartNode>("UdpStartNode");
  bt_factory_->registerNodeType<UdpStopNode>("UdpStopNode");

  bt_factory_->registerNodeType<JsonViewerNode>("JsonViewerNode");
}

NodeManager::QtNodeRegisterPtr NodeManager::qtNodeRegistry() { return qt_node_registry_; }
NodeManager::BTFactoryPtr NodeManager::btFactory() { return bt_factory_; }
void NodeManager::setNodeModel(const std::string& _id, std::shared_ptr<NodeModel> _model)
{
  node_models_[_id] = std::move(_model);
}
std::shared_ptr<NodeModel> NodeManager::nodeModel(const std::string& _id)
{
  auto iter = node_models_.find(_id);
  if (iter == node_models_.end())
  {
    LOG_ERROR("NodeManager::nodeModel: node type not found: " + _id);
    throw std::invalid_argument("NodeManager::nodeModel: node type not found: " + _id);
  }
  return iter->second;
}
}  // namespace robosense::lidar