#include "udp_stop_node.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <iostream>

namespace robosense::lidar
{

UdpStopNode::UdpStopNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config), blackboard_(_config.blackboard)
{
  LOG_INFO("[UdpStopNode:{}] 节点初始化完成", name());
}

BT::PortsList UdpStopNode::providedPorts()
{
  return {
    // 输入端口 - UDP配置
    BT::InputPort<std::shared_ptr<MEMSUDP>>("udp_handle", "UDP接收器句柄"),
    BT::InputPort<bool>("stop_all", false, "是否停止所有UDP接收器"),

    // 输出端口 - 状态信息
    BT::OutputPort<std::string>("result", "UDP停止结果"), BT::OutputPort<std::string>("error_message", "错误信息"),
    BT::OutputPort<bool>("udp_stopped", "UDP是否已停止"), BT::OutputPort<int>("stopped_count", "停止的接收器数量")
  };
}

BT::NodeStatus UdpStopNode::tick()
{
  try
  {
    // 读取配置参数
    auto udp_handle_opt = getInput<std::shared_ptr<MEMSUDP>>("udp_handle");

    // 获取UDP接收器句柄
    if (!udp_handle_opt || !udp_handle_opt.value())
    {
      LOG_ERROR("[UdpStopNode:{}] 未提供UDP接收器句柄", name());
      setOutput("result", "FAILED");
      setOutput("error_message", "未提供UDP接收器句柄");
      setOutput("udp_stopped", false);
      setOutput("stopped_count", 0);
      return BT::NodeStatus::FAILURE;
    }

    auto udp_receiver = udp_handle_opt.value();
    LOG_INFO("[UdpStopNode:{}] 停止UDP接收器", name());

    stopUdpReceiver(udp_receiver);
    int stopped_count = 1;
    LOG_INFO("[UdpStopNode:{}] 成功停止UDP接收器", name());

    // 清理黑板中的UDP状态
    if (blackboard_)
    {
      blackboard_->set("udp_started", false);
    }

    // 设置输出
    setOutput("result", "SUCCESS");
    setOutput("error_message", "");
    setOutput("udp_stopped", true);
    setOutput("stopped_count", stopped_count);

    LOG_INFO("[UdpStopNode:{}] UDP停止成功，停止数量: {}", name(), stopped_count);
    return BT::NodeStatus::SUCCESS;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("[UdpStopNode:{}] 执行异常: {}", name(), e.what());
    setOutput("result", "ERROR");
    setOutput("error_message", e.what());
    setOutput("udp_stopped", false);
    setOutput("stopped_count", 0);
    return BT::NodeStatus::FAILURE;
  }
}

void UdpStopNode::stopUdpReceiver(std::shared_ptr<MEMSUDP> _receiver)
{
  if (_receiver)
  {
    try
    {
      _receiver->stop();
      LOG_DEBUG("[UdpStopNode] UDP接收器已停止");
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("[UdpStopNode] 停止UDP接收器异常: {}", e.what());
    }
  }
}

}  // namespace robosense::lidar
