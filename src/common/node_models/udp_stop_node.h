#pragma once

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/blackboard.h"
#include <memory>
#include <string>

// 包含 MEMSUDP - 使用本地增强版本
#include "../mems_udp.h"

namespace robosense::lidar
{

/**
 * @brief UDP停止节点
 * 
 * 负责停止UDP接收器，清理资源，结束UDP数据采集。
 * 这个节点只负责停止UDP服务，不进行数据处理。
 */
class UdpStopNode : public BT::SyncActionNode
{
public:
  UdpStopNode(const std::string& _name, const BT::NodeConfig& _config);

  // 禁用拷贝和移动
  UdpStopNode(const UdpStopNode&)            = delete;
  UdpStopNode& operator=(const UdpStopNode&) = delete;
  UdpStopNode(UdpStopNode&&)                 = delete;
  UdpStopNode& operator=(UdpStopNode&&)      = delete;

  ~UdpStopNode() override = default;

  /**
   * @brief 提供节点端口配置
   */
  static BT::PortsList providedPorts();

  /**
   * @brief 节点执行函数
   */
  BT::NodeStatus tick() override;

private:
  /**
   * @brief 停止UDP接收器
   */
  void stopUdpReceiver(std::shared_ptr<MEMSUDP> _receiver);

private:
  std::shared_ptr<BT::Blackboard> blackboard_;  // 黑板引用
};

}  // namespace robosense::lidar
