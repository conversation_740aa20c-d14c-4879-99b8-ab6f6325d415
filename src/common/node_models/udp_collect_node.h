#pragma once

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/blackboard.h"
#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

// 直接包含 Sol2 头文件
#define SOL_ALL_SAFETIES_ON 1
#include <sol/sol.hpp>

// 包含 MEMSUDP - 使用本地增强版本
#include "../mems_udp.h"

namespace robosense::lidar
{

/**
 * @brief UDP数据采集节点
 * 
 * 负责从已启动的UDP接收器中采集数据，并通过Lua脚本进行解析。
 * 这个节点专注于数据采集和解析，不负责UDP连接的管理。
 */
class UdpCollectNode : public BT::SyncActionNode
{
public:
  UdpCollectNode(const std::string& _name, const BT::NodeConfig& _config);

  // 禁用拷贝和移动
  UdpCollectNode(const UdpCollectNode&)            = delete;
  UdpCollectNode& operator=(const UdpCollectNode&) = delete;
  UdpCollectNode(UdpCollectNode&&)                 = delete;
  UdpCollectNode& operator=(UdpCollectNode&&)      = delete;

  ~UdpCollectNode() override;

  /**
   * @brief 提供节点端口配置
   */
  static BT::PortsList providedPorts();

  /**
   * @brief 节点执行函数
   */
  BT::NodeStatus tick() override;

private:
  struct ParsedField
  {
    std::string name_;
    std::string value_;
    std::string type_;
    int offset_ { 0 };
    int length_ { 0 };
    std::string desc_;
  };

  void clearOutputData();
  void setOutputData(const std::string& _key, const std::string& _value);
  std::string buildOutputJson() const;

  /**
   * @brief 初始化Lua环境
   */
  void initializeLuaEnvironment();

  /**
   * @brief 设置黑板访问函数
   */
  void setupBlackboardAccess();

  /**
   * @brief 获取共享的UDP接收器实例
   */
  static std::shared_ptr<MEMSUDP> getSharedUdpReceiver(const std::string& _key);

  /**
   * @brief UDP数据接收回调
   */
  void onUdpDataReceived(const char* _data);

  /**
   * @brief UDP超时回调
   */
  void onUdpTimeout();

  /**
   * @brief 执行Lua解析脚本
   */
  BT::NodeStatus executeLuaParser(const std::vector<char>& _data);

  /**
   * @brief 获取黑板值
   */
  std::string getBlackboardValue(const std::string& _key);

  /**
   * @brief 设置黑板值
   */
  void setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type);

  /**
   * @brief 检查黑板键是否存在
   */
  bool hasBlackboardKey(const std::string& _key);

  /**
   * @brief 记录日志
   */
  void logMessage(const std::string& _message, const std::string& _level);

  /**
   * @brief 将字节数据转换为Lua表
   */
  sol::table bytesToLuaTable(const std::vector<char>& _data);

  /**
   * @brief 将字节数据转换为十六进制字符串
   */
  std::string bytesToHexString(const std::vector<char>& _data);

private:
  sol::state lua_state_;                        // Lua状态机
  std::shared_ptr<BT::Blackboard> blackboard_;  // 黑板引用
  bool lua_initialized_;                        // Lua环境是否已初始化

  // UDP相关
  std::shared_ptr<MEMSUDP> udp_receiver_;  // UDP接收器（共享）
  std::string receiver_key_;               // 接收器共享键
  std::string receiver_id_;                // 接收器ID（用于共享数据键）
  std::size_t packet_length_;              // 数据包长度
  std::size_t timeout_ms_;                 // 超时时间(毫秒)

  // 数据缓存
  std::mutex data_mutex_;               // 数据互斥锁
  std::vector<char> latest_data_;       // 最新接收的数据
  std::atomic<bool> data_available_;    // 是否有新数据可用
  std::atomic<bool> timeout_occurred_;  // 是否发生超时

  // 解析结果
  std::string parse_result_;   // 解析结果
  std::string error_message_;  // 错误信息

  // 黑板前缀与事务
  std::string bb_prefix_;
  std::unordered_map<std::string, std::pair<std::string, std::string>> pending_bb_;
  bool in_scope_ { false };

  // 脚本期间可读的数据拷贝
  std::vector<char> script_data_;

  // 自定义输出数据缓存
  std::unordered_map<std::string, std::string> output_data_;

  // 静态共享UDP接收器管理
  static std::mutex shared_receivers_mutex_;
  static std::unordered_map<std::string, std::shared_ptr<MEMSUDP>> shared_receivers_;
};

}  // namespace robosense::lidar
