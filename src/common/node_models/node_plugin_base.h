﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef NODE_PLUGIN_BASE_H
#define NODE_PLUGIN_BASE_H

#include "abs_behavior_tree.h"
#include "node_manager.h"
#include "node_plugin_interface.h"
#include <rsfsc_log/rsfsc_log_macro.h>
#include <unordered_map>

namespace robosense::lidar
{
/**
 * @brief 行为树插件基类，提供注册BT节点和Qt节点的功能
 */
class NodePluginBase : public NodePluginInterface
{
public:
  NodePluginBase()                      = default;
  NodePluginBase(const NodePluginBase&) = delete;
  NodePluginBase& operator=(const NodePluginBase&) = delete;
  NodePluginBase(NodePluginBase&&)                 = delete;
  NodePluginBase& operator=(NodePluginBase&&) = delete;
  ~NodePluginBase() override                  = default;

  /**
   * @brief 初始化插件，注册所有节点
   * @return 初始化是否成功
   */
  bool initialize(NodeManager* _node_manager) override
  {
    node_manager_ = _node_manager;
    return registerNodes();
  }

  /**
   * @brief 注册所有节点
   * @return 注册是否成功
   */
  virtual bool registerNodes() = 0;

  template <typename BT_NODE_T, typename QT_NODE_T>
  bool registerNodeTypeModel(const std::string& _id, const std::string& _category, const NodeModel& _model)
  {
    try
    {
      if (nullptr == node_manager_)
      {
        LOG_ERROR("NodeManager is null");
        return false;
      }

      // 创建一个新的NodeModel的共享指针
      auto model_ptr = std::make_shared<NodeModel>();
      // 复制必要的属性
      model_ptr->type            = _model.type;
      model_ptr->registration_id = _model.registration_id;
      model_ptr->instance_name   = _model.instance_name;
      model_ptr->display_name    = _model.display_name;
      model_ptr->description     = _model.description;
      // 复制端口信息
      model_ptr->ports = _model.ports;
      // 共享PropertyModel
      model_ptr->property_model = _model.property_model;

      node_manager_->setNodeModel(_id, model_ptr);

      // Qt节点模型注册
      QString category = QString::fromStdString(BT::toStr(node_manager_->nodeModel(_id)->type));
      auto builder     = [this, _id]() {
        auto model_ptr = node_manager_->nodeModel(_id);
        return std::make_unique<QT_NODE_T>(model_ptr.get());
      };
      node_manager_->qtNodeRegistry()->registerModel<QT_NODE_T>(std::move(builder), category);

      // BT节点注册
      // node_manager_.btFactory()->registerBuilder(BT::CreateManifest<BT_NODE_T>(_id), BT_NODE_T::CreateNodeBuilder());
      node_manager_->btFactory()->registerNodeType<BT_NODE_T>(_id);

      // 记录节点类型
      node_infos_.nodes.emplace_back(_model.getNodeInfo(_category));
      node_categories_.emplace(_id, _category);

      return true;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("Failed to register node type: {} Error: {}", _id, e.what());
      return false;
    }
  }

  /**
   * @brief 获取插件提供的自定义节点类型
   * @return 节点类型列表
   */
  [[nodiscard]] NodeInfos nodeInfos() const override { return node_infos_; }

  /**
   * @brief 获取节点类别
   * @param node_type 节点类型
   * @return 节点类别
   */
  [[nodiscard]] std::string nodeCategory(const std::string& _node_type) const
  {
    auto iter = node_categories_.find(_node_type);
    if (iter != node_categories_.end())
    {
      return iter->second;
    }
    return "Custom";  // 默认类别
  }

private:
  NodeInfos node_infos_;                                          // 插件提供的节点类型
  std::unordered_map<std::string, std::string> node_categories_;  // 节点类型到类别的映射
  NodeManager* node_manager_ { nullptr };
};
}  // namespace robosense::lidar

#endif  // NODE_PLUGIN_BASE_H