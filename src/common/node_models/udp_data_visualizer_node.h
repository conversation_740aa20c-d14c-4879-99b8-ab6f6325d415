/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the RoboSense License
 * Agreement. Please see the LICENSE file for details.
 *****************************************************************************/

#ifndef UDP_DATA_VISUALIZER_NODE_H
#define UDP_DATA_VISUALIZER_NODE_H

#include "behaviortree_cpp/action_node.h"
#include "visualization/visualization_manager.h"
#include <QByteArray>
#include <QTimer>
#include <memory>

namespace robosense::lidar
{

/**
 * @brief UDP数据可视化节点
 * 
 * 该节点用于接收UDP数据并进行可视化显示，支持多种数据类型：
 * 1. 数值数据（电压、电流、温度等）
 * 2. 图像数据（灰度图、RGB图等）
 * 3. 状态数据（设备状态、连接状态等）
 * 
 * 特性：
 * - 支持多种可视化组件类型
 * - 自动数据解析和格式转换
 * - 可配置的显示时长和更新频率
 * - 线程安全的UI操作
 */
class UdpDataVisualizerNode : public BT::SyncActionNode
{
public:
  UdpDataVisualizerNode(const std::string& _name, const BT::NodeConfig& _config);
  ~UdpDataVisualizerNode() override;

  UdpDataVisualizerNode(const UdpDataVisualizerNode&)            = delete;
  UdpDataVisualizerNode& operator=(const UdpDataVisualizerNode&) = delete;
  UdpDataVisualizerNode(UdpDataVisualizerNode&&)                 = delete;
  UdpDataVisualizerNode& operator=(UdpDataVisualizerNode&&)      = delete;

  static BT::PortsList providedPorts();

protected:
  BT::NodeStatus tick() override;

private:
  // 数据解析方法
  void parseNumericData(const QByteArray& _udp_data);
  void parseImageData(const QByteArray& _udp_data);
  void parseStatusData(const QByteArray& _udp_data);

  // 可视化配置
  void setupVisualizationConfig();
  QVariantMap createVisualizationConfig() const;

  // 数据验证
  bool validateUdpData(const QByteArray& _data) const;
  bool isValidNumericData(const QByteArray& _data) const;
  bool isValidImageData(const QByteArray& _data) const;

  // 可视化助手
  std::unique_ptr<NodeVisualizationHelper> visualization_helper_;

  // 配置参数
  QString data_type_;            // 数据类型：numeric, image, status
  QString widget_type_;          // 组件类型：NumericTable, NumericGauge, ImageDisplay
  QString visualization_title_;  // 可视化标题
  int display_duration_;         // 显示时长（毫秒）
  int update_interval_;          // 更新间隔（毫秒）
  bool enable_auto_close_;       // 是否自动关闭
  bool enable_data_logging_;     // 是否记录数据日志

  // 数据解析参数
  int expected_data_size_;  // 期望的数据大小
  QString data_format_;     // 数据格式：float32, int16, uint8, etc.
  int image_width_;         // 图像宽度
  int image_height_;        // 图像高度
  QString image_format_;    // 图像格式：grayscale, rgb, bgr

  // 数值范围参数
  double min_value_;    // 数值最小值
  double max_value_;    // 数值最大值
  int decimal_places_;  // 小数位数

  // 更新控制
  QTimer* update_timer_;
  qint64 last_update_time_;
  int update_counter_;
};

}  // namespace robosense::lidar

#endif  // UDP_DATA_VISUALIZER_NODE_H
