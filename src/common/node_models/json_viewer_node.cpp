#include "json_viewer_node.h"
#include "behaviortree_cpp/contrib/json.hpp"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QFile>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>
#include <QTextDocument>

namespace robosense::lidar
{

JsonViewerNode::JsonViewerNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config),
  json_input_(""),
  json_file_path_(""),
  visualization_mode_("auto"),
  widget_type_("NumericTable"),
  visualization_title_("JSON数据可视化"),
  visualization_duration_(5000),
  enable_visualization_(false),
  data_path_(""),
  min_value_(0.0),
  max_value_(100.0),
  decimal_places_(2),
  is_json_loaded_(false),
  visualization_helper_(std::make_unique<NodeVisualizationHelper>(QString::fromStdString(_name)))
{
  LOG_INFO("[JsonViewerNode] 节点初始化完成: {}", _name);
}

JsonViewerNode::~JsonViewerNode()
{
  // 清理资源
}

BT::PortsList JsonViewerNode::providedPorts()
{
  return {
    // 输入端口 - JSON相关
    BT::InputPort<std::string>("json_data", R"(<![CDATA[{
  "sensor_data": {
    "temperature": 25.6,
    "humidity": 68.2,
    "pressure": 1013.25,
    "voltage": 3.3
  },
  "status": {
    "power_on": true,
    "sensor_ready": true,
    "error_count": 0
  }
}]]>)",
                               "输入的JSON字符串数据"),
    BT::InputPort<std::string>("json_file", "", "JSON文件路径"),

    // 输入端口 - 可视化相关
    BT::InputPort<bool>("enable_visualization", true, "是否启用可视化显示"),
    BT::InputPort<std::string>("visualization_mode", "auto", "可视化模式: auto/numeric/status"),
    BT::InputPort<std::string>("widget_type", "NumericTable", "可视化组件: NumericTable/NumericGauge"),
    BT::InputPort<std::string>("visualization_title", "JSON数据可视化", "可视化标题"),
    BT::InputPort<int>("visualization_duration", 5000, "可视化显示时长(毫秒)"),
    BT::InputPort<std::string>("data_path", "", "JSON中数据的路径，如 'data.values'，空表示根级别"),
    BT::InputPort<double>("min_value", 0.0, "数值范围最小值"),
    BT::InputPort<double>("max_value", 100.0, "数值范围最大值"), BT::InputPort<int>("decimal_places", 2, "小数位数"),

    // 输出端口
    BT::OutputPort<std::string>("result", "处理结果状态"), BT::OutputPort<std::string>("error_message", "错误信息"),
    BT::OutputPort<int>("json_size", "JSON数据大小（字节）"),
    BT::OutputPort<bool>("visualization_shown", "可视化是否已显示"),
    BT::OutputPort<int>("visualization_data_count", "可视化数据项数量")
  };
}

BT::NodeStatus JsonViewerNode::tick()
{
  try
  {
    // 1. 读取JSON输入参数
    auto json_data_opt = getInput<std::string>("json_data");
    auto json_file_opt = getInput<std::string>("json_file");

    // 2. 读取可视化相关参数
    auto enable_visualization_opt   = getInput<bool>("enable_visualization");
    auto visualization_mode_opt     = getInput<std::string>("visualization_mode");
    auto widget_type_opt            = getInput<std::string>("widget_type");
    auto visualization_title_opt    = getInput<std::string>("visualization_title");
    auto visualization_duration_opt = getInput<int>("visualization_duration");
    auto data_path_opt              = getInput<std::string>("data_path");
    auto min_value_opt              = getInput<double>("min_value");
    auto max_value_opt              = getInput<double>("max_value");
    auto decimal_places_opt         = getInput<int>("decimal_places");

    // 3. 处理JSON数据输入
    LOG_DEBUG("[JsonViewerNode:{}] json_data_opt.has_value(): {}", name(), json_data_opt.has_value());
    LOG_DEBUG("[JsonViewerNode:{}] json_file_opt.has_value(): {}", name(), json_file_opt.has_value());

    // 检查json_data端口的原始值
    if (json_data_opt)
    {
      std::string raw_json_data = json_data_opt.value();
      LOG_DEBUG("[JsonViewerNode:{}] json_data原始值: {}", name(), raw_json_data);

      json_input_ = QString::fromStdString(raw_json_data);

      // 检查是否是CDATA格式的JSON数据
      if (json_input_.contains("<![CDATA[") && json_input_.contains("]]>"))
      {
        // 提取CDATA中的JSON数据
        int start_pos = json_input_.indexOf("<![CDATA[") + 9;  // 9 = strlen("<![CDATA[")
        int end_pos   = json_input_.lastIndexOf("]]>");
        if (start_pos < end_pos)
        {
          json_input_ = json_input_.mid(start_pos, end_pos - start_pos);
          LOG_INFO("[JsonViewerNode:{}] 从CDATA中提取JSON数据，长度: {}", name(), json_input_.length());
        }
        else
        {
          LOG_ERROR("[JsonViewerNode:{}] CDATA格式错误", name());
        }
      }
      // 处理HTML编码的JSON数据
      else if (json_input_.contains("&quot;") || json_input_.contains("&#"))
      {
        // 使用QTextDocument进行HTML解码
        QTextDocument doc;
        doc.setHtml(json_input_);
        json_input_ = doc.toPlainText();
        LOG_INFO("[JsonViewerNode:{}] HTML解码后的JSON数据，长度: {}", name(), json_input_.length());
      }
      else
      {
        LOG_INFO("[JsonViewerNode:{}] 接收到JSON数据，长度: {}", name(), json_input_.length());
      }

      LOG_DEBUG("[JsonViewerNode:{}] JSON数据内容: {}", name(), json_input_.toStdString());
    }
    else if (json_file_opt)
    {
      std::string raw_json_file = json_file_opt.value();
      LOG_DEBUG("[JsonViewerNode:{}] json_file原始值: {}", name(), raw_json_file);

      // 检查json_file是否实际包含JSON数据而不是文件路径
      if (!raw_json_file.empty() &&
          (raw_json_file.find('{') != std::string::npos || raw_json_file.find("&quot;") != std::string::npos ||
           raw_json_file.find("<![CDATA[") != std::string::npos))
      {
        LOG_INFO("[JsonViewerNode:{}] json_file端口包含JSON数据而非文件路径，使用作为JSON数据", name());
        json_input_ = QString::fromStdString(raw_json_file);

        // 检查是否是CDATA格式的JSON数据
        if (json_input_.contains("<![CDATA[") && json_input_.contains("]]>"))
        {
          // 提取CDATA中的JSON数据
          int start_pos = json_input_.indexOf("<![CDATA[") + 9;  // 9 = strlen("<![CDATA[")
          int end_pos   = json_input_.lastIndexOf("]]>");
          if (start_pos < end_pos)
          {
            json_input_ = json_input_.mid(start_pos, end_pos - start_pos);
            LOG_INFO("[JsonViewerNode:{}] 从CDATA中提取JSON数据，长度: {}", name(), json_input_.length());
          }
          else
          {
            LOG_ERROR("[JsonViewerNode:{}] CDATA格式错误", name());
          }
        }
        // 处理HTML编码的JSON数据
        else if (json_input_.contains("&quot;") || json_input_.contains("&#"))
        {
          // 使用QTextDocument进行HTML解码
          QTextDocument doc;
          doc.setHtml(json_input_);
          json_input_ = doc.toPlainText();
          LOG_INFO("[JsonViewerNode:{}] HTML解码后的JSON数据，长度: {}", name(), json_input_.length());
        }
        else
        {
          LOG_INFO("[JsonViewerNode:{}] 接收到JSON数据，长度: {}", name(), json_input_.length());
        }

        LOG_DEBUG("[JsonViewerNode:{}] JSON数据内容: {}", name(), json_input_.toStdString());
      }
      else
      {
        json_file_path_ = QString::fromStdString(raw_json_file);
        LOG_INFO("[JsonViewerNode:{}] 接收到JSON文件路径: {}", name(), json_file_path_.toStdString());
      }
    }
    else
    {
      setOutput("result", "FAILURE");
      setOutput("error_message", "未提供JSON数据或文件路径");
      setOutput("json_size", 0);
      setOutput("visualization_shown", false);
      setOutput("visualization_data_count", 0);
      LOG_ERROR("[JsonViewerNode:{}] 未提供JSON数据或文件路径", name());
      return BT::NodeStatus::FAILURE;
    }

    // 4. 更新可视化配置
    if (enable_visualization_opt)
    {
      enable_visualization_ = enable_visualization_opt.value();
    }
    if (visualization_mode_opt)
    {
      visualization_mode_ = QString::fromStdString(visualization_mode_opt.value());
    }
    if (widget_type_opt)
    {
      widget_type_ = QString::fromStdString(widget_type_opt.value());
    }
    if (visualization_title_opt)
    {
      visualization_title_ = QString::fromStdString(visualization_title_opt.value());
    }
    if (visualization_duration_opt)
    {
      visualization_duration_ = visualization_duration_opt.value();
    }
    if (data_path_opt)
    {
      data_path_ = QString::fromStdString(data_path_opt.value());
    }
    if (min_value_opt)
    {
      min_value_ = min_value_opt.value();
    }
    if (max_value_opt)
    {
      max_value_ = max_value_opt.value();
    }
    if (decimal_places_opt)
    {
      decimal_places_ = decimal_places_opt.value();
    }

    // 5. 加载JSON数据
    if (!loadJsonData())
    {
      setOutput("result", "LOAD_FAILED");
      setOutput("error_message", "JSON数据加载失败");
      setOutput("json_size", 0);
      setOutput("visualization_shown", false);
      setOutput("visualization_data_count", 0);
      return BT::NodeStatus::FAILURE;
    }

    // 6. 可视化功能
    bool visualization_shown     = false;
    int visualization_data_count = 0;

    if (enable_visualization_)
    {
      try
      {
        showVisualization();
        visualization_shown = true;

        // 统计可视化数据项数量
        QVariantMap numeric_data;
        if (extractNumericData(numeric_data))
        {
          visualization_data_count = numeric_data.size();
        }

        LOG_INFO("[JsonViewerNode] 可视化显示成功，数据项: {}", visualization_data_count);
      }
      catch (const std::exception& e)
      {
        LOG_ERROR("[JsonViewerNode] 可视化显示失败: {}", e.what());
      }
    }

    // 7. 设置输出
    setOutput("result", "SUCCESS");
    setOutput("error_message", "");
    setOutput("json_size", json_content_.size());
    setOutput("visualization_shown", visualization_shown);
    setOutput("visualization_data_count", visualization_data_count);

    LOG_INFO("[JsonViewerNode] JSON数据处理完成，大小: {} 字节", json_content_.size());

    return BT::NodeStatus::SUCCESS;
  }
  catch (const std::exception& e)
  {
    setOutput("result", "FAILURE");
    setOutput("error_message", e.what());
    setOutput("json_size", 0);
    setOutput("visualization_shown", false);
    setOutput("visualization_data_count", 0);
    LOG_ERROR("[JsonViewerNode] 执行异常: {}", e.what());
    return BT::NodeStatus::FAILURE;
  }
}

bool JsonViewerNode::loadJsonData()
{
  try
  {
    std::string json_string;

    // 从文件加载JSON数据
    if (!json_file_path_.isEmpty())
    {
      QFile file(json_file_path_);
      if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
      {
        LOG_ERROR("[JsonViewerNode] 无法打开JSON文件: {}", json_file_path_.toStdString());
        return false;
      }

      json_content_ = file.readAll();
      file.close();
      json_string = json_content_.toStdString();
      LOG_INFO("[JsonViewerNode] 从文件加载JSON数据: {} 字节", json_content_.size());
    }
    else
    {
      json_content_ = json_input_;
      json_string   = json_input_.toStdString();
    }

    // 使用nlohmann/json解析JSON数据
    nlohmann_json_ = nlohmann::json::parse(json_string);

    // 同时保持QJsonDocument兼容性（用于现有的extractNumericData等方法）
    QJsonParseError parse_error {};
    json_document_ = QJsonDocument::fromJson(json_content_.toUtf8(), &parse_error);

    if (parse_error.error != QJsonParseError::NoError)
    {
      LOG_ERROR("[JsonViewerNode] QJsonDocument解析失败: {}", parse_error.errorString().toStdString());
      return false;
    }

    is_json_loaded_ = true;
    LOG_INFO("[JsonViewerNode] JSON数据解析成功");
    return true;
  }
  catch (const nlohmann::json::parse_error& e)
  {
    LOG_ERROR("[JsonViewerNode] nlohmann::json解析失败: {}", e.what());
    return false;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("[JsonViewerNode] 加载JSON数据异常: {}", e.what());
    return false;
  }
}

// ==================== 可视化功能实现 ====================

void JsonViewerNode::showVisualization()
{
  if (!is_json_loaded_ || json_document_.isNull())
  {
    throw std::runtime_error("JSON数据未加载或无效");
  }

  // 根据可视化模式选择处理方式
  if (visualization_mode_ == "numeric" || (visualization_mode_ == "auto" && widget_type_.contains("Numeric")))
  {
    QVariantMap numeric_data;
    if (extractNumericData(numeric_data))
    {
      QVariantMap config       = createVisualizationConfig();
      config["min_value"]      = min_value_;
      config["max_value"]      = max_value_;
      config["decimal_places"] = decimal_places_;

      visualization_helper_->showNumericData(numeric_data, config);
      LOG_INFO("[JsonViewerNode] 显示数值可视化: {} 个数值", numeric_data.size());
    }
    else
    {
      throw std::runtime_error("无法从JSON中提取数值数据");
    }
  }
  else if (visualization_mode_ == "status")
  {
    QVariantMap status_data;
    if (extractStatusData(status_data))
    {
      QVariantMap config    = createVisualizationConfig();
      config["widget_type"] = "NumericTable";  // 状态数据使用表格显示

      visualization_helper_->showNumericData(status_data, config);
      LOG_INFO("[JsonViewerNode] 显示状态可视化: {} 个状态", status_data.size());
    }
    else
    {
      throw std::runtime_error("无法从JSON中提取状态数据");
    }
  }
  else
  {
    throw std::runtime_error(QString("不支持的可视化模式: %1").arg(visualization_mode_).toStdString());
  }
}

bool JsonViewerNode::extractNumericData(QVariantMap& _numeric_data)
{
  _numeric_data.clear();

  QJsonObject root_obj   = json_document_.object();
  QJsonObject target_obj = root_obj;

  // 如果指定了数据路径，导航到目标对象
  if (!data_path_.isEmpty())
  {
    QStringList path_parts = data_path_.split('.');
    for (const QString& part : path_parts)
    {
      if (target_obj.contains(part))
      {
        QJsonValue value = target_obj[part];
        if (value.isObject())
        {
          target_obj = value.toObject();
        }
        else
        {
          LOG_ERROR("[JsonViewerNode] 数据路径 '{}' 不是对象", part.toStdString());
          return false;
        }
      }
      else
      {
        LOG_ERROR("[JsonViewerNode] 数据路径 '{}' 不存在", part.toStdString());
        return false;
      }
    }
  }

  // 提取数值数据
  for (auto it = target_obj.begin(); it != target_obj.end(); ++it)
  {
    const QString& key      = it.key();
    const QJsonValue& value = it.value();

    if (value.isDouble())
    {
      _numeric_data[key] = value.toDouble();
    }
    else if (value.isString())
    {
      // 尝试将字符串转换为数值
      bool conversion_ok;
      double num_value = value.toString().toDouble(&conversion_ok);
      if (conversion_ok)
      {
        _numeric_data[key] = num_value;
      }
    }
    else if (value.isArray())
    {
      // 处理数组中的数值
      QJsonArray array = value.toArray();
      for (int i = 0; i < array.size() && i < 10; ++i)  // 最多处理10个元素
      {
        QJsonValue array_value = array[i];
        if (array_value.isDouble())
        {
          QString array_key        = QString("%1[%2]").arg(key).arg(i);
          _numeric_data[array_key] = array_value.toDouble();
        }
      }
    }
  }

  return !_numeric_data.isEmpty();
}

bool JsonViewerNode::extractStatusData(QVariantMap& _status_data)
{
  _status_data.clear();

  QJsonObject root_obj   = json_document_.object();
  QJsonObject target_obj = root_obj;

  // 如果指定了数据路径，导航到目标对象
  if (!data_path_.isEmpty())
  {
    QStringList path_parts = data_path_.split('.');
    for (const QString& part : path_parts)
    {
      if (target_obj.contains(part) && target_obj[part].isObject())
      {
        target_obj = target_obj[part].toObject();
      }
      else
      {
        return false;
      }
    }
  }

  // 提取状态数据（布尔值和状态字符串）
  for (auto it = target_obj.begin(); it != target_obj.end(); ++it)
  {
    const QString& key      = it.key();
    const QJsonValue& value = it.value();

    if (value.isBool())
    {
      _status_data[key] = value.toBool() ? 1.0 : 0.0;
    }
    else if (value.isString())
    {
      QString str_value = value.toString().toLower();
      if (str_value == "true" || str_value == "on" || str_value == "enabled")
      {
        _status_data[key] = 1.0;
      }
      else if (str_value == "false" || str_value == "off" || str_value == "disabled")
      {
        _status_data[key] = 0.0;
      }
      else
      {
        // 对于其他字符串，尝试转换为数值
        bool conversion_ok = false;
        double num_value   = str_value.toDouble(&conversion_ok);
        if (conversion_ok)
        {
          _status_data[key] = num_value;
        }
      }
    }
    else if (value.isDouble())
    {
      _status_data[key] = value.toDouble();
    }
  }

  return !_status_data.isEmpty();
}

QVariantMap JsonViewerNode::createVisualizationConfig() const
{
  QVariantMap config;
  config["widget_type"] = widget_type_;
  config["title"]       = visualization_title_;

  if (visualization_duration_ > 0)
  {
    config["auto_close_duration"] = visualization_duration_;
  }

  return config;
}

}  // namespace robosense::lidar
