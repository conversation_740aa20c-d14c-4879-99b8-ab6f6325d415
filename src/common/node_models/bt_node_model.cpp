﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "bt_node_model.h"
#include "QtNodes/internal/NodeDelegateModel.hpp"
#include "node_utils.h"
#include "property/property_view.h"
#include <QApplication>
#include <QBoxLayout>
#include <QComboBox>
#include <QDebug>
#include <QFile>
#include <QFont>
#include <QFormLayout>
#include <QJsonDocument>
#include <QLineEdit>
#include <QPainter>
#include <QSizePolicy>
#include <QWidget>
#include <qboxlayout.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

const int MARGIN              = 10;
const int DEFAULT_LINE_WIDTH  = 100;
const int DEFAULT_FIELD_WIDTH = 50;
const int DEFAULT_LABEL_WIDTH = 50;

BtNodeModel::BtNodeModel(NodeModel* _model) :
  main_widget_(new QWidget()),
  uid_(static_cast<int16_t>(AbsBehaviorTree::getUID())),
  main_layout_(new QVBoxLayout(main_widget_)),
  name_label_(new QLabel()),
  state_label_(new QLabel()),
  model_ptr_(std::make_shared<NodeModel>()),
  icon_renderer_(nullptr),
  style_caption_color_(QtNodes::NodeStyle().FontColor),
  style_caption_alias_((_model != nullptr) ? _model->registration_id : QString())
{
  // readStyle();
  // 初始化布局
  main_layout_->setMargin(0);
  main_layout_->setSpacing(2);
  main_widget_->setLayout(main_layout_);

  state_label_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
  state_label_->setFixedSize(20, 20);
  name_label_->setSizePolicy(QSizePolicy::Minimum, QSizePolicy::Fixed);
  name_label_->setMinimumSize(60, 20);

  main_widget_->setAttribute(Qt::WA_NoSystemBackground);
  main_widget_->setStyleSheet("color: white;");

  auto* grid_layout = new QGridLayout;

  grid_layout->addWidget(name_label_, 0, 0);
  grid_layout->addWidget(state_label_, 0, 1);

  main_layout_->addLayout(grid_layout);

  main_layout_->setSizeConstraint(QLayout::SizeConstraint::SetMaximumSize);

  updateNodeInfo(BT::Duration(), "", BT::NodeStatus::IDLE, BT::NodeStatus::IDLE);

  // 从_model复制必要的属性到model_ptr_（但不复制property_model，确保每个节点有独立的PropertyModel）
  if (_model != nullptr)
  {
    model_ptr_->type            = _model->type;
    model_ptr_->registration_id = _model->registration_id;
    model_ptr_->instance_name   = _model->instance_name;
    model_ptr_->display_name    = _model->display_name;
    model_ptr_->description     = _model->description;
    model_ptr_->ports           = _model->ports;
    // 注意：不复制 property_model，让每个节点有独立的 PropertyModel 实例
  }

  // 初始化NodeModel的PropertyModel
  model_ptr_->initPropertyModel();

  property_view_ = std::make_unique<PropertyView>(nullptr, true, model_ptr_->registration_id);
  property_view_->setModel(model_ptr_->property_model);
  property_view_->setButtonHidden(PropertyView::BtnHidden { true, true, true, true, true });

  name_label_->setText(model_ptr_->property_model->getValue<QString>("唯一名称", "name"));
  QObject::connect(model_ptr_->property_model.get(), &PropertyModel::signalValueChanged, this,
                   [this](const std::string& _group, const std::string& _key, const QVariant& _value) {
                     if (_group == "唯一名称" && _key == "name")
                     {
                       name_label_->setText(_value.toString());
                     }
                   });
}

BtNodeModel::~BtNodeModel() = default;

BT::NodeType BtNodeModel::nodeType() const
{
  if (!model_ptr_)
  {
    LOG_ERROR("nodeType : model_ptr_ is nullptr");
    return BT::NodeType::UNDEFINED;
  }
  return model_ptr_->type;
}

void BtNodeModel::initWidget()
{
  if (!style_icon_.isEmpty())
  {
    QFile file(style_icon_);
    if (!file.open(QIODevice::ReadOnly))
    {
      LOG_ERROR("file not opened: {}", style_icon_);
      file.close();
    }
    else
    {
      QByteArray bytes          = file.readAll();
      QByteArray new_color_fill = QString("fill:%1;").arg(style_caption_color_.name()).toUtf8();
      bytes.replace("fill:#ffffff;", new_color_fill);
      icon_renderer_ = new QSvgRenderer(bytes, this);
    }
  }

  updateNodeSize();
}

unsigned int BtNodeModel::nPorts(QtNodes::PortType _port_type) const
{
  if (!model_ptr_)
  {
    LOG_ERROR("nPorts : model_ptr_ is nullptr");
    return 0;
  }

  if (_port_type == QtNodes::PortType::Out)
  {
    if (nodeType() == BT::NodeType::ACTION || nodeType() == BT::NodeType::CONDITION)
    {
      return 0;
    }

    return 1;
  }
  if (_port_type == QtNodes::PortType::In)
  {
    return (model_ptr_->registration_id == AbsBehaviorTree::getRootId()) ? 0 : 1;
  }
  return 0;
}

QtNodes::ConnectionPolicy BtNodeModel::portOutConnectionPolicy(QtNodes::PortIndex /*_port_index*/) const
{
  if (!model_ptr_)
  {
    LOG_ERROR("portOutConnectionPolicy : model_ptr_ is nullptr");
    return QtNodes::ConnectionPolicy::One;
  }

  return (nodeType() == BT::NodeType::DECORATOR || model_ptr_->registration_id == AbsBehaviorTree::getRootId())
           ? QtNodes::ConnectionPolicy::One
           : QtNodes::ConnectionPolicy::Many;
}

void BtNodeModel::updateNodeSize()
{
  int field_column_width = DEFAULT_LABEL_WIDTH;
  int label_column_width = 0;

  main_widget_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
  main_widget_->adjustSize();

  emit embeddedWidgetSizeUpdated();
}

QtNodes::NodeDataType BtNodeModel::dataType(QtNodes::PortType /*_port_type*/, QtNodes::PortIndex /*_port_index*/) const
{
  return QtNodes::NodeDataType { "", "" };
}

std::shared_ptr<QtNodes::NodeData> BtNodeModel::outData(QtNodes::PortIndex /*_port_index*/) { return nullptr; }

void BtNodeModel::readStyle()
{
  QFile style_file(":/NodesStyle.json");

  if (!style_file.open(QIODevice::ReadOnly))
  {
    qWarning("Couldn't open NodesStyle.json");
    return;
  }

  QByteArray bytearray = style_file.readAll();
  style_file.close();
  QJsonParseError error {};
  QJsonDocument json_doc(QJsonDocument::fromJson(bytearray, &error));

  if (json_doc.isNull())
  {
    LOG_ERROR("Failed to create JSON doc: {}", error.errorString());
    return;
  }
  if (!json_doc.isObject())
  {
    LOG_ERROR("JSON is not an object.");
    return;
  }

  QJsonObject toplevel_object = json_doc.object();

  if (toplevel_object.isEmpty())
  {
    LOG_ERROR("JSON Object is empty.");
    return;
  }

  if (!model_ptr_)
  {
    LOG_ERROR("model_ptr_ is nullptr");
    return;
  }

  QString model_type_name(QString::fromStdString(toStr(model_ptr_->type)));

  for (const auto& model_name : { model_type_name, model_ptr_->registration_id })
  {
    if (toplevel_object.contains(model_name))
    {
      auto category_style = toplevel_object[model_name].toObject();
      if (category_style.contains("icon"))
      {
        style_icon_ = category_style["icon"].toString();
      }
      if (category_style.contains("caption_color"))
      {
        style_caption_color_ = category_style["caption_color"].toString();
      }
      if (category_style.contains("caption_alias"))
      {
        style_caption_alias_ = category_style["caption_alias"].toString();
      }
    }
  }
}

const QString& BtNodeModel::registrationName() const
{
  static const QString empty_string;
  if (!model_ptr_)
  {
    LOG_ERROR("registrationName : model_ptr_ is nullptr");
    return empty_string;
  }
  return model_ptr_->registration_id;
}

const QString& BtNodeModel::instanceName() const
{
  static const QString empty_string;
  if (!model_ptr_)
  {
    LOG_ERROR("instanceName : model_ptr_ is nullptr");
    return empty_string;
  }
  return model_ptr_->instance_name;
}

PortsMapping BtNodeModel::getCurrentPortMapping() const
{
  PortsMapping out;

  for (const auto& iter : ports_widgets_)
  {
    const auto& label = iter.first;
    const auto& value = iter.second;

    if (auto* line_edit = dynamic_cast<QLineEdit*>(value))
    {
      out.insert(std::make_pair(label, line_edit->text()));
    }
    else if (auto* combo = dynamic_cast<QComboBox*>(value))
    {
      out.insert(std::make_pair(label, combo->currentText()));
    }
  }
  return out;
}

QJsonObject BtNodeModel::save() const
{
  LOG_DEBUG("BtNodeModel::save() - 开始保存节点: {}", registrationName().toStdString());

  QJsonObject model_json = QtNodes::NodeDelegateModel::save();  // NOTE 这里必须调用QtNodes::NodeDelegateModel::save()
  model_json["name"]     = registrationName();
  model_json["alias"]    = instanceName();

  LOG_DEBUG("BtNodeModel::save() - 保存基本信息: name={}, alias={}", registrationName().toStdString(),
            instanceName().toStdString());

  // 保存端口部件的值
  for (const auto& iter : ports_widgets_)
  {
    if (auto* line_edit = dynamic_cast<QLineEdit*>(iter.second))
    {
      model_json[iter.first] = line_edit->text();
    }
    else if (auto* combo = dynamic_cast<QComboBox*>(iter.second))
    {
      model_json[iter.first] = combo->currentText();
    }
  }

  // 保存UI属性参数
  if (model_ptr_ && model_ptr_->property_model)
  {
    LOG_DEBUG("BtNodeModel::save() - 开始保存UI属性参数");
    QJsonObject properties_json;

    // 遍历所有组和属性
    for (const auto& [group_name, group] : model_ptr_->property_model->groups())
    {
      QJsonObject group_object;
      auto group_name_str = QString::fromStdString(group_name);

      // 遍历该组中的所有属性
      for (const auto& [full_key, property] : model_ptr_->property_model->properties())
      {
        QString full_key_str = QString::fromStdString(full_key);
        QStringList parts    = full_key_str.split('.');

        if (parts.size() == 2 && parts[0] == group_name_str)
        {
          const QString& key = parts[1];
          QVariant value     = property->value();
          group_object[key]  = QJsonValue::fromVariant(value);
          LOG_DEBUG("BtNodeModel::save() - 保存属性: {}.{} = {}", group_name, key.toStdString(),
                    value.toString().toStdString());
        }
      }

      if (!group_object.isEmpty())
      {
        properties_json[group_name_str] = group_object;
        LOG_DEBUG("BtNodeModel::save() - 保存属性组: {}", group_name);
      }
    }

    if (!properties_json.isEmpty())
    {
      model_json["properties"] = properties_json;
      LOG_DEBUG("BtNodeModel::save() - 成功保存所有UI属性参数");
    }
    else
    {
      LOG_WARN("BtNodeModel::save() - 没有UI属性参数需要保存");
    }
  }
  else
  {
    LOG_WARN("BtNodeModel::save() - model_ptr_或property_model为空，无法保存UI属性参数");
  }

  return model_json;
}

void BtNodeModel::load(QJsonObject const& _json_object)
{
  try
  {
    LOG_DEBUG("BtNodeModel::load() - 开始加载节点模型");
    QtNodes::NodeDelegateModel::load(_json_object);

    // 检查是否包含internal-data字段
    if (_json_object.contains("internal-data") && _json_object["internal-data"].isObject())
    {
      QJsonObject internal_data = _json_object["internal-data"].toObject();
      // 调用restore方法来恢复节点状态
      LOG_DEBUG("BtNodeModel::load() - 正在恢复节点状态: {}", internal_data["name"].toString().toStdString());
      restore(internal_data);
    }
    else
    {
      // 直接使用传入的JSON对象
      LOG_DEBUG("BtNodeModel::load() - 直接使用JSON对象恢复节点状态");
      restore(_json_object);
    }

    LOG_DEBUG("BtNodeModel::load() - 节点模型加载完成");
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("BtNodeModel::load() - 加载节点模型失败: {}", e.what());
  }
}

void BtNodeModel::restore(const QJsonObject& _model_json)
{
  LOG_INFO("开始恢复节点状态");

  // 检查name字段是否存在
  if (!_model_json.contains("name"))
  {
    LOG_WARN("JSON对象中没有name字段，尝试使用model-name字段");
    if (_model_json.contains("model-name"))
    {
      if (registrationName() != _model_json["model-name"].toString())
      {
        LOG_ERROR("节点类型不匹配: 期望 {} 但得到 {}", registrationName(), _model_json["model-name"].toString());
        return;
      }
    }
    else
    {
      LOG_ERROR("JSON对象中既没有name字段也没有model-name字段，无法恢复节点状态");
      return;
    }
  }
  else if (registrationName() != _model_json["name"].toString())
  {
    LOG_ERROR("节点类型不匹配: 期望 {} 但得到 {}", registrationName(), _model_json["name"].toString());
    return;
  }

  // 设置实例名称
  QString alias;
  if (_model_json.contains("alias"))
  {
    alias = _model_json["alias"].toString();
  }
  else if (_model_json.contains("name"))
  {
    // 如果没有alias字段，使用name字段作为实例名称
    alias = _model_json["name"].toString();
  }

  if (!alias.isEmpty())
  {
    LOG_INFO("设置节点实例名称: {}", alias);
    setInstanceName(alias);
  }

  // 恢复端口映射
  for (auto iter = _model_json.begin(); iter != _model_json.end(); iter++)
  {
    if (iter.key() != "alias" && iter.key() != "name" && iter.key() != "model-name" && iter.key() != "properties" &&
        iter.key() != "internal-data")
    {
      LOG_INFO("恢复端口映射: {} = {}", iter.key(), iter.value().toString());
      setPortMapping(iter.key(), iter.value().toString());
    }
  }

  // 恢复UI属性参数
  if (_model_json.contains("properties") && _model_json["properties"].isObject())
  {
    LOG_INFO("BtNodeModel::restore() - 开始恢复UI属性参数");
    restoreProperties(_model_json["properties"].toObject());
  }
  else
  {
    LOG_WARN("BtNodeModel::restore() - JSON中没有properties字段或不是有效对象");
  }

  LOG_INFO("节点状态恢复完成");
}

void BtNodeModel::restoreProperties(const QJsonObject& _properties_json)
{
  if (!model_ptr_)
  {
    LOG_ERROR("无法恢复属性：model_ptr_为空");
    return;
  }

  if (!model_ptr_->property_model)
  {
    LOG_ERROR("无法恢复属性：property_model为空");
    return;
  }

  // 遍历所有属性组
  for (auto group_iter = _properties_json.begin(); group_iter != _properties_json.end(); ++group_iter)
  {
    const QString& group_name = group_iter.key();

    if (!group_iter.value().isObject())
    {
      LOG_ERROR("属性组 {} 不是有效的JSON对象", group_name);
      continue;
    }

    QJsonObject group_object = group_iter.value().toObject();

    // 遍历该组中的所有属性
    for (auto prop_iter = group_object.begin(); prop_iter != group_object.end(); ++prop_iter)
    {
      const QString& key = prop_iter.key();
      QVariant value;

      // 根据JSON值类型转换为QVariant
      if (prop_iter.value().isDouble())
      {
        value = prop_iter.value().toDouble();
      }
      else if (prop_iter.value().isString())
      {
        value = prop_iter.value().toString();
      }
      else if (prop_iter.value().isBool())
      {
        value = prop_iter.value().toBool();
      }
      else if (prop_iter.value().isArray())
      {
        // 处理数组类型
        QJsonArray array = prop_iter.value().toArray();
        QVariantList list;
        for (const auto& item : array)
        {
          list.append(item.toVariant());
        }
        value = list;
      }
      else
      {
        value = prop_iter.value().toVariant();
      }

      // 更新属性模型中的值 - 使用强制设置，忽略焦点状态
      model_ptr_->property_model->forceSetValue(group_name.toStdString(), key.toStdString(), value);
    }
  }

  // 发出属性恢复完成信号，通知UI刷新
  Q_EMIT propertiesRestored();
}

void BtNodeModel::lock(bool _locked)
{
  for (const auto& iter : ports_widgets_)
  {
    const auto& field_widget = iter.second;

    if (auto* lineedit = dynamic_cast<QLineEdit*>(field_widget))
    {
      lineedit->setReadOnly(_locked);
    }
    else if (auto* combo = dynamic_cast<QComboBox*>(field_widget))
    {
      combo->setEnabled(!_locked);
    }
  }
}

void BtNodeModel::setPortMapping(const QString& _port_name, const QString& _port_value)
{
  auto widget_iter = ports_widgets_.find(_port_name);
  if (widget_iter == ports_widgets_.end())
  {
    return;
  }

  auto* line_edit = dynamic_cast<QLineEdit*>(widget_iter->second);
  if (line_edit != nullptr)
  {
    line_edit->setText(_port_value);
    return;
  }

  auto* combo = dynamic_cast<QComboBox*>(widget_iter->second);
  if (combo == nullptr)
  {
    return;
  }

  int index = combo->findText(_port_value);
  if (index == -1)
  {
    LOG_ERROR("error, combo value {} not found", _port_value);
    return;
  }

  combo->setCurrentIndex(index);
}

bool BtNodeModel::eventFilter(QObject* _obj, QEvent* _event)
{
  // if (_event->type() == QEvent::Paint && _obj == caption_logo_left_ && (icon_renderer_ != nullptr))
  // {
  //   QPainter paint(caption_logo_left_);
  //   icon_renderer_->render(&paint);
  // }
  return QtNodes::NodeDelegateModel::eventFilter(_obj, _event);
}

void BtNodeModel::updateNodeInfo(BT::Duration /*_timestamp*/,
                                 const std::string& /*_name*/,
                                 BT::NodeStatus /*_prev_status*/,
                                 BT::NodeStatus _status,
                                 const QVariantMap& _output_ports)
{
  if (!model_ptr_ || model_ptr_->registration_id == AbsBehaviorTree::getRootId())
  {
    return;
  }

  QString style_sheet;

  if (_status == BT::NodeStatus::RUNNING)
  {
    style_sheet = "background-color: #FFA500;"  // 橙色表示运行中
                  "color: white;"
                  "border-radius: 10px;";
  }
  else if (_status == BT::NodeStatus::SUCCESS)
  {
    style_sheet = "background-color: #7BB661;"  // 绿色表示成功
                  "color: white;"
                  "border-radius: 10px;";
  }
  else if (_status == BT::NodeStatus::FAILURE)
  {
    style_sheet = "background-color: #E74C3C;"  // 红色表示失败
                  "color: white;"
                  "border-radius: 10px;";
  }
  else
  {
    style_sheet = "background-color:rgb(129, 131, 129);"  // 默认灰色
                  "color: white;"
                  "border-radius: 10px;";
  }

  state_label_->setStyleSheet(style_sheet);

  // update output
  for (auto it = _output_ports.begin(); it != _output_ports.end(); ++it)
  {
    const QString& key    = it.key();
    const QVariant& value = it.value();

    LOG_DEBUG("key = {}", key);

    auto widget = ports_widgets_.find(key);
    if (widget == ports_widgets_.end())
    {
      continue;
    }

    auto* ptr = dynamic_cast<LineEditNode*>(widget->second);
    if (ptr == nullptr)
    {
      continue;
    }

    ptr->setText(value.toString());
  }
}

PropertyModel* BtNodeModel::propertyModel() const
{
  if (!model_ptr_ || !model_ptr_->property_model)
  {
    return nullptr;
  }
  return model_ptr_->property_model.get();
}

void BtNodeModel::setInstanceName(const QString& _name)
{
  if (!model_ptr_)
  {
    return;
  }

  model_ptr_->setInstanceName(QStringView(_name));

  if (property_view_ && model_ptr_->property_model)
  {
    // 重新设置模型并刷新UI
    property_view_->setModel(model_ptr_->property_model);
  }

  updateNodeSize();
  emit instanceNameChanged();
}

void BtNodeModel::onHighlightPortValue(const QString& _value)
{
  for (const auto& iter : ports_widgets_)
  {
    auto* line_edit = dynamic_cast<QLineEdit*>(iter.second);
    if (line_edit == nullptr)
    {
      continue;
    }

    QString line_str = line_edit->text();
    QString style_sheet;

    if (!_value.isEmpty() && line_str == _value)
    {
      style_sheet = "color: rgb(30,30,30); "
                    "background-color: #ffef0b; "
                    "border: 0px; ";
    }
    else
    {
      style_sheet = "color: rgb(30,30,30); "
                    "background-color: rgb(200,200,200); "
                    "border: 0px; ";
    }

    line_edit->setStyleSheet(style_sheet);
  }
}

void LineEditNode::mouseDoubleClickEvent(QMouseEvent* /*_event*/) { emit doubleClicked(); }

void LineEditNode::focusOutEvent(QFocusEvent* _event)
{
  QLineEdit::focusOutEvent(_event);
  emit lostFocus();
}

}  // namespace robosense::lidar
