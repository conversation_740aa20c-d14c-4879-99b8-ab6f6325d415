#include "udp_collect_node.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "udp_start_node.h"
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <iostream>
#include <sstream>
#include <thread>

namespace robosense::lidar
{

// 静态成员初始化
std::mutex UdpCollectNode::shared_receivers_mutex_;
std::unordered_map<std::string, std::shared_ptr<MEMSUDP>> UdpCollectNode::shared_receivers_;

UdpCollectNode::UdpCollectNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config),
  blackboard_(_config.blackboard),
  lua_initialized_(false),
  packet_length_(1024),
  timeout_ms_(1000),
  data_available_(false),
  timeout_occurred_(false)
{
  initializeLuaEnvironment();
  LOG_INFO("[UdpCollectNode:{}] 节点初始化完成", name());
}

UdpCollectNode::~UdpCollectNode()
{
  // 不需要停止UDP接收器，因为它是共享的
}

BT::PortsList UdpCollectNode::providedPorts()
{
  return { // 输入端口 - UDP配置
           BT::InputPort<std::shared_ptr<MEMSUDP>>("udp_handle", "UDP接收器句柄"),
           BT::InputPort<int>("packet_length", 1024, "期望的数据包长度"),
           BT::InputPort<int>("timeout_ms", 1000, "接收超时时间(毫秒)"),
           BT::InputPort<std::string>("receiver_id", "test_receiver", "接收器ID（用于共享数据键）"),
           BT::InputPort<std::string>("parser_script",
                                      "-- UDP数据解析脚本\n"
                                      "-- data_bytes: 字节数组\n"
                                      "-- data_hex: 十六进制字符串\n"
                                      "-- data_length: 数据长度\n"
                                      "\n"
                                      "log('Received UDP data, length: ' .. data_length, 'info')\n"
                                      "\n"
                                      "-- 解析数据并存储到黑板（带前缀）\n"
                                      "bb_set('data_length', tostring(data_length), 'string')\n"
                                      "bb_set('data_hex', data_hex, 'string')\n"
                                      "\n"
                                      "-- 设置自定义输出数据\n"
                                      "set_output('parsed_length', tostring(data_length))\n"
                                      "set_output('parsed_hex', data_hex)\n"
                                      "\n"
                                      "return 'SUCCESS'",
                                      "Lua数据解析脚本"),
           BT::InputPort<std::string>("bb_prefix", "udp", "黑板键前缀"),

           // 输出端口 - 解析结果
           BT::OutputPort<std::string>("result", "解析结果"), BT::OutputPort<std::string>("error_message", "错误信息"),
           BT::OutputPort<std::string>("parsed_data", "解析后的JSON数据"),
           BT::OutputPort<int>("data_length", "数据长度"), BT::OutputPort<bool>("data_available", "是否有数据可用"),
           BT::OutputPort<bool>("timeout_occurred", "是否发生超时")
  };
}

BT::NodeStatus UdpCollectNode::tick()
{
  try
  {
    // 读取配置参数
    auto udp_handle_opt    = getInput<std::shared_ptr<MEMSUDP>>("udp_handle");
    auto packet_length_opt = getInput<int>("packet_length");
    auto timeout_ms_opt    = getInput<int>("timeout_ms");
    auto parser_script_opt = getInput<std::string>("parser_script");
    auto bb_prefix_opt     = getInput<std::string>("bb_prefix");
    auto receiver_id_opt   = getInput<std::string>("receiver_id");

    if (packet_length_opt)
      packet_length_ = static_cast<std::size_t>(packet_length_opt.value());
    if (timeout_ms_opt)
      timeout_ms_ = static_cast<std::size_t>(timeout_ms_opt.value());
    if (bb_prefix_opt)
      bb_prefix_ = bb_prefix_opt.value();
    if (receiver_id_opt)
      receiver_id_ = receiver_id_opt.value();

    // 获取UDP接收器句柄
    if (!udp_handle_opt || !udp_handle_opt.value())
    {
      LOG_ERROR("[UdpCollectNode:{}] 未提供UDP接收器句柄", name());
      setOutput("result", "FAILED");
      setOutput("error_message", "未提供UDP接收器句柄");
      setOutput("data_available", false);
      return BT::NodeStatus::FAILURE;
    }

    udp_receiver_ = udp_handle_opt.value();
    LOG_DEBUG("[UdpCollectNode:{}] 使用UDP接收器句柄", name());

    // 从共享数据缓冲区获取数据
    std::vector<char> data_copy;
    bool data_received = false;

    // 获取receiver_id来构建数据键
    std::string data_key = receiver_id_;
    if (data_key.empty())
    {
      data_key = "test_receiver";  // 默认值，与UDP开始节点保持一致
    }

    // 等待数据可用
    auto start_time       = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::milliseconds(timeout_ms_);

    while (std::chrono::steady_clock::now() - start_time < timeout_duration)
    {
      if (UdpStartNode::isSharedDataAvailable(data_key))
      {
        data_received = UdpStartNode::getSharedData(data_key, data_copy);
        if (data_received)
        {
          break;
        }
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    if (!data_received)
    {
      LOG_DEBUG("[UdpCollectNode:{}] 从共享缓冲区获取数据超时，数据键: {}", name(), data_key);
      setOutput("result", "TIMEOUT");
      setOutput("error_message", "从共享缓冲区获取数据超时");
      setOutput("data_available", false);
      setOutput("timeout_occurred", true);
      return BT::NodeStatus::FAILURE;
    }

    LOG_DEBUG("[UdpCollectNode:{}] 开始解析UDP数据，长度: {}", name(), data_copy.size());

    // 执行Lua解析脚本
    if (parser_script_opt)
    {
      BT::NodeStatus parse_status = executeLuaParser(data_copy);

      // 设置输出
      setOutput("result", parse_result_);
      setOutput("error_message", error_message_);
      setOutput("parsed_data", buildOutputJson());
      setOutput("data_length", static_cast<int>(data_copy.size()));
      setOutput("data_available", true);
      setOutput("timeout_occurred", false);

      return parse_status;
    }
    else
    {
      // 没有解析脚本，直接返回原始数据信息
      setOutput("result", "SUCCESS");
      setOutput("error_message", "");
      setOutput("parsed_data", "{}");
      setOutput("data_length", static_cast<int>(data_copy.size()));
      setOutput("data_available", true);
      setOutput("timeout_occurred", false);

      LOG_INFO("[UdpCollectNode:{}] 数据采集成功，长度: {}", name(), data_copy.size());
      return BT::NodeStatus::SUCCESS;
    }
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("[UdpCollectNode:{}] 执行异常: {}", name(), e.what());
    setOutput("result", "ERROR");
    setOutput("error_message", e.what());
    setOutput("data_available", false);
    return BT::NodeStatus::FAILURE;
  }
}

void UdpCollectNode::onUdpDataReceived(const char* _data)
{
  std::lock_guard<std::mutex> lock(data_mutex_);

  latest_data_.assign(_data, _data + packet_length_);
  data_available_   = true;
  timeout_occurred_ = false;

  LOG_DEBUG("[UdpCollectNode:{}] 接收到UDP数据，长度: {}", name(), packet_length_);
}

void UdpCollectNode::onUdpTimeout()
{
  timeout_occurred_ = true;
  LOG_DEBUG("[UdpCollectNode:{}] UDP接收超时", name());
}

std::shared_ptr<MEMSUDP> UdpCollectNode::getSharedUdpReceiver(const std::string& _key)
{
  std::lock_guard<std::mutex> lock(shared_receivers_mutex_);
  auto it = shared_receivers_.find(_key);
  return (it != shared_receivers_.end()) ? it->second : nullptr;
}

void UdpCollectNode::initializeLuaEnvironment()
{
  try
  {
    lua_state_.open_libraries(sol::lib::base, sol::lib::string, sol::lib::math, sol::lib::table);
    setupBlackboardAccess();
    lua_initialized_ = true;
    LOG_DEBUG("[UdpCollectNode] Lua环境初始化完成");
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("[UdpCollectNode] Lua环境初始化失败: {}", e.what());
    lua_initialized_ = false;
  }
}

void UdpCollectNode::setupBlackboardAccess()
{
  // 设置黑板访问函数
  lua_state_["bb_get"] = [this](const std::string& key) { return getBlackboardValue(key); };
  lua_state_["bb_set"] = [this](const std::string& key, const std::string& value, const std::string& type) {
    setBlackboardValue(key, value, type);
  };
  lua_state_["bb_has"] = [this](const std::string& key) { return hasBlackboardKey(key); };

  // 设置日志函数
  lua_state_["log"] = [this](const std::string& message, const std::string& level) { logMessage(message, level); };

  // 设置输出数据函数
  lua_state_["set_output"]   = [this](const std::string& key, const std::string& value) { setOutputData(key, value); };
  lua_state_["clear_output"] = [this]() { clearOutputData(); };

  // 设置数据转换函数
  lua_state_["bytes_to_hex"]   = [this](const std::vector<char>& data) { return bytesToHexString(data); };
  lua_state_["bytes_to_table"] = [this](const std::vector<char>& data) { return bytesToLuaTable(data); };
}

BT::NodeStatus UdpCollectNode::executeLuaParser(const std::vector<char>& _data)
{
  if (!lua_initialized_)
  {
    error_message_ = "Lua环境未初始化";
    parse_result_  = "ERROR";
    return BT::NodeStatus::FAILURE;
  }

  try
  {
    // 准备脚本数据
    script_data_ = _data;

    // 设置Lua全局变量
    lua_state_["data_bytes"]  = bytesToLuaTable(_data);
    lua_state_["data_hex"]    = bytesToHexString(_data);
    lua_state_["data_length"] = _data.size();

    // 获取解析脚本
    auto parser_script_opt = getInput<std::string>("parser_script");
    if (!parser_script_opt)
    {
      error_message_ = "未提供解析脚本";
      parse_result_  = "ERROR";
      return BT::NodeStatus::FAILURE;
    }

    // 执行Lua脚本
    auto result = lua_state_.script(parser_script_opt.value());

    // 获取返回值
    if (result.valid())
    {
      if (result.get_type() == sol::type::string)
      {
        parse_result_ = result.get<std::string>();
      }
      else
      {
        parse_result_ = "SUCCESS";
      }
      error_message_ = "";

      LOG_DEBUG("[UdpCollectNode:{}] Lua脚本执行成功，结果: {}", name(), parse_result_);

      if (parse_result_ == "SUCCESS")
      {
        return BT::NodeStatus::SUCCESS;
      }
      else if (parse_result_ == "FAILURE")
      {
        return BT::NodeStatus::FAILURE;
      }
      else
      {
        return BT::NodeStatus::SUCCESS;  // 默认成功
      }
    }
    else
    {
      sol::error err = result;
      error_message_ = err.what();
      parse_result_  = "ERROR";
      LOG_ERROR("[UdpCollectNode:{}] Lua脚本执行失败: {}", name(), error_message_);
      return BT::NodeStatus::FAILURE;
    }
  }
  catch (const std::exception& e)
  {
    error_message_ = e.what();
    parse_result_  = "ERROR";
    LOG_ERROR("[UdpCollectNode:{}] 执行Lua解析脚本异常: {}", name(), e.what());
    return BT::NodeStatus::FAILURE;
  }
}

// 辅助方法实现
void UdpCollectNode::clearOutputData() { output_data_.clear(); }

void UdpCollectNode::setOutputData(const std::string& _key, const std::string& _value) { output_data_[_key] = _value; }

std::string UdpCollectNode::buildOutputJson() const
{
  if (output_data_.empty())
  {
    return "{}";
  }

  std::stringstream ss;
  ss << "{";
  bool first = true;
  for (const auto& pair : output_data_)
  {
    if (!first)
      ss << ",";
    ss << "\"" << pair.first << "\":\"" << pair.second << "\"";
    first = false;
  }
  ss << "}";
  return ss.str();
}

std::string UdpCollectNode::getBlackboardValue(const std::string& _key)
{
  if (!blackboard_)
    return "";

  std::string full_key = bb_prefix_.empty() ? _key : bb_prefix_ + "." + _key;
  try
  {
    return blackboard_->get<std::string>(full_key);
  }
  catch (...)
  {
    return "";
  }
}

void UdpCollectNode::setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type)
{
  if (!blackboard_)
    return;

  std::string full_key = bb_prefix_.empty() ? _key : bb_prefix_ + "." + _key;
  blackboard_->set(full_key, _value);
}

bool UdpCollectNode::hasBlackboardKey(const std::string& _key)
{
  if (!blackboard_)
    return false;

  std::string full_key = bb_prefix_.empty() ? _key : bb_prefix_ + "." + _key;
  try
  {
    blackboard_->get<std::string>(full_key);
    return true;
  }
  catch (...)
  {
    return false;
  }
}

void UdpCollectNode::logMessage(const std::string& _message, const std::string& _level)
{
  if (_level == "error")
  {
    LOG_ERROR("[UdpCollectNode:{}] {}", name(), _message);
  }
  else if (_level == "warn")
  {
    LOG_WARN("[UdpCollectNode:{}] {}", name(), _message);
  }
  else if (_level == "debug")
  {
    LOG_DEBUG("[UdpCollectNode:{}] {}", name(), _message);
  }
  else
  {
    LOG_INFO("[UdpCollectNode:{}] {}", name(), _message);
  }
}

sol::table UdpCollectNode::bytesToLuaTable(const std::vector<char>& _data)
{
  sol::table table = lua_state_.create_table();
  for (size_t i = 0; i < _data.size(); ++i)
  {
    table[i + 1] = static_cast<unsigned char>(_data[i]);  // Lua索引从1开始
  }
  return table;
}

std::string UdpCollectNode::bytesToHexString(const std::vector<char>& _data)
{
  std::stringstream ss;
  ss << std::hex << std::setfill('0');
  for (const auto& byte : _data)
  {
    ss << std::setw(2) << static_cast<unsigned char>(byte);
  }
  return ss.str();
}

}  // namespace robosense::lidar
