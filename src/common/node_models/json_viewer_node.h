#pragma once

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/contrib/json.hpp"
#include "visualization/visualization_manager.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QMetaObject>
#include <QTimer>
#include <QTreeView>
#include <QWidget>
#include <memory>

class QJsonModel;

namespace robosense::lidar
{

/**
 * @brief JSON查看器节点
 * 
 * 功能特性：
 * 1. 支持输入JSON字符串或加载JSON文件
 * 2. 支持指定外部QWidget作为显示容器
 * 3. 支持自动弹窗显示或静默处理
 * 4. 提供JSON数据的树形视图展示
 * 5. 支持JSON数据的编辑和导出
 */
class JsonViewerNode : public BT::SyncActionNode
{
public:
  JsonViewerNode(const std::string& _name, const BT::NodeConfig& _config);
  ~JsonViewerNode() override;

  JsonViewerNode(const JsonViewerNode&)            = delete;
  JsonViewerNode& operator=(const JsonViewerNode&) = delete;
  JsonViewerNode(JsonViewerNode&&)                 = delete;
  JsonViewerNode& operator=(JsonViewerNode&&)      = delete;

  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;

private:
  // 核心功能方法
  bool loadJsonData();

  // 可视化功能方法
  void showVisualization();
  bool extractNumericData(QVariantMap& _numeric_data);
  bool extractStatusData(QVariantMap& _status_data);
  [[nodiscard]] QVariantMap createVisualizationConfig() const;

  // JSON处理方法
  bool loadFromString(const QString& _json_string);
  bool loadFromFile(const QString& _file_path);
  bool validateJsonData();

  // UI相关方法
  QWidget* getTargetWidget();
  void createStandaloneWindow();
  void embedInTargetWidget(QWidget* _target_widget);

  // 配置解析方法
  enum class DisplayMode : std::uint8_t
  {
    NONE,         // 不显示
    AUTO_POPUP,   // 自动弹窗
    EMBED_WIDGET  // 嵌入到指定widget
  };

  [[nodiscard]] DisplayMode parseDisplayMode() const;

private:
  // JSON数据
  QJsonDocument json_document_;
  nlohmann::json nlohmann_json_;  // 使用nlohmann/json库解析的JSON数据
  QString json_content_;

  // 配置参数
  QString json_input_;
  QString json_file_path_;

  // 可视化配置参数
  QString visualization_mode_;   // 可视化模式：auto, numeric, status
  QString widget_type_;          // 可视化组件类型：NumericTable, NumericGauge
  QString visualization_title_;  // 可视化标题
  int visualization_duration_;   // 可视化显示时长
  bool enable_visualization_;    // 是否启用可视化
  QString data_path_;            // JSON中数据的路径，如 "data.values"
  double min_value_;             // 数值范围最小值
  double max_value_;             // 数值范围最大值
  int decimal_places_;           // 小数位数

  // 状态标志
  bool is_json_loaded_;

  // 可视化助手
  std::unique_ptr<NodeVisualizationHelper> visualization_helper_;
};

}  // namespace robosense::lidar
