#include "udp_data_parser_node.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <iostream>
#include <sstream>

namespace robosense::lidar
{

// =============================================================================
// UdpDataParserNode 实现
// =============================================================================

UdpDataParserNode::UdpDataParserNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config),
  blackboard_(_config.blackboard),
  lua_initialized_(false),
  udp_started_(false),
  udp_port_(6699),
  packet_length_(1024),
  timeout_ms_(1000),
  data_available_(false),
  timeout_occurred_(false)
{
  initializeLuaEnvironment();
}

UdpDataParserNode::~UdpDataParserNode() { stopUdpReceiver(); }

BT::PortsList UdpDataParserNode::providedPorts()
{
  return {
    // 输入端口
    BT::InputPort<std::string>("udp_ip", "0.0.0.0", "UDP监听IP地址"),
    BT::InputPort<int>("udp_port", 6699, "UDP监听端口"), BT::InputPort<int>("packet_length", 1024, "期望的数据包长度"),
    BT::InputPort<int>("timeout_ms", 1000, "接收超时时间(毫秒)"),
    BT::InputPort<std::string>("group_ip", "", "组播IP地址(可选)"),
    BT::InputPort<std::string>("parser_script",
                               "-- UDP数据解析脚本\n"
                               "-- data_bytes: 字节数组\n"
                               "-- data_hex: 十六进制字符串\n"
                               "-- data_length: 数据长度\n"
                               "\n"
                               "log('Received UDP data, length: ' .. data_length, 'info')\n"
                               "\n"
                               "-- 解析数据并存储到黑板（带前缀）\n"
                               "bb_set('data_length', tostring(data_length), 'string')\n"
                               "bb_set('data_hex', data_hex, 'string')\n"
                               "\n"
                               "-- 自定义输出数据供其他节点使用\n"
                               "clear_output()\n"
                               "set_output('magic', string.format('0x%02X', read_u8(0)))\n"
                               "set_output('version', tostring(read_u8(1)))\n"
                               "set_output('payload_hex', slice_hex(4, 8))\n"
                               "\n"
                               "return OK\n",
                               "Lua解析脚本"),
    BT::InputPort<std::string>("parser_script_file", "", "Lua解析脚本文件路径"),
    BT::InputPort<bool>("auto_start", true, "是否自动启动UDP接收"),
    BT::InputPort<std::string>("bb_prefix", "", "黑板键前缀(可选)，默认使用节点名"),

    // 输出端口 - 通用数据输出
    BT::OutputPort<std::string>("parse_result", "解析结果状态"),
    BT::OutputPort<std::string>("error_message", "错误信息"), BT::OutputPort<int>("data_length", "接收到的数据长度"),
    BT::OutputPort<std::string>("data_hex", "数据的十六进制表示"),
    BT::OutputPort<std::string>("output_json", "自定义输出数据JSON"),
    BT::OutputPort<bool>("data_available", "是否有新数据可用"), BT::OutputPort<bool>("timeout_occurred", "是否发生超时")
  };
}

BT::NodeStatus UdpDataParserNode::tick()
{
  try
  {
    // 获取配置参数
    auto udp_ip_result        = getInput<std::string>("udp_ip");
    auto udp_port_result      = getInput<int>("udp_port");
    auto packet_length_result = getInput<int>("packet_length");
    auto timeout_ms_result    = getInput<int>("timeout_ms");
    auto group_ip_result      = getInput<std::string>("group_ip");
    auto auto_start_result    = getInput<bool>("auto_start");
    auto bb_prefix_result     = getInput<std::string>("bb_prefix");

    if (udp_ip_result)
    {
      udp_ip_ = udp_ip_result.value();
    }
    if (udp_port_result)
    {
      udp_port_ = static_cast<uint16_t>(udp_port_result.value());
    }
    if (packet_length_result)
    {
      packet_length_ = static_cast<std::size_t>(packet_length_result.value());
      LOG_DEBUG("packet_length 从XML读取: {}", packet_length_);
    }
    if (timeout_ms_result)
    {
      timeout_ms_ = static_cast<std::size_t>(timeout_ms_result.value());
    }
    if (group_ip_result)
    {
      group_ip_ = group_ip_result.value();
    }

    // 处理黑板前缀，默认使用节点名
    bb_prefix_ = bb_prefix_result && !bb_prefix_result.value().empty() ? bb_prefix_result.value() : name();

    bool auto_start = auto_start_result ? auto_start_result.value() : true;

    // 如果需要自动启动且UDP未启动，则启动UDP接收
    if (auto_start && !udp_started_)
    {
      if (!startUdpReceiver())
      {
        setOutput("error_message", "Failed to start UDP receiver");
        return BT::NodeStatus::FAILURE;
      }
    }

    // 对于同步节点，阻塞等待数据或超时
    auto start_time       = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::milliseconds(timeout_ms_);

    while (!data_available_.load() && !timeout_occurred_.load())
    {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));

      // 检查是否超时
      auto elapsed = std::chrono::steady_clock::now() - start_time;
      if (elapsed >= timeout_duration)
      {
        setOutput("timeout_occurred", true);
        setOutput("data_available", false);
        setOutput("error_message", "UDP receive timeout");
        return BT::NodeStatus::FAILURE;
      }
    }

    // 检查是否因为超时而退出循环
    if (timeout_occurred_.load())
    {
      setOutput("timeout_occurred", true);
      setOutput("data_available", false);
      setOutput("error_message", "UDP receive timeout");
      timeout_occurred_.store(false);  // 重置超时标志
      return BT::NodeStatus::FAILURE;
    }

    // 获取最新数据
    std::vector<char> data_copy;
    {
      std::lock_guard<std::mutex> lock(data_mutex_);
      data_copy = latest_data_;
      data_available_.store(false);  // 重置数据可用标志
    }

    // 设置输出端口
    setOutput("data_available", true);
    setOutput("data_length", static_cast<int>(data_copy.size()));
    setOutput("data_hex", bytesToHexString(data_copy));
    setOutput("timeout_occurred", false);

    // 保存给脚本访问，并重置事务
    script_data_ = data_copy;
    pending_bb_.clear();
    in_scope_ = false;

    // 执行Lua解析脚本
    return executeLuaParser(data_copy);
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "UDP parser error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }
}

void UdpDataParserNode::initializeLuaEnvironment()
{
  try
  {
    // 打开Lua标准库
    lua_state_.open_libraries(sol::lib::base, sol::lib::string, sol::lib::math, sol::lib::table, sol::lib::io,
                              sol::lib::os);

    // 设置黑板访问函数
    setupBlackboardAccess();

    lua_initialized_ = true;
    logMessage("Lua environment initialized successfully", "info");
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "Failed to initialize Lua environment: " + std::string(e.what());
    logMessage(error_msg, "error");
    lua_initialized_ = false;
  }
}

void UdpDataParserNode::setupBlackboardAccess()
{
  // 绑定黑板访问函数到Lua
  lua_state_["bb_get"] = [this](const std::string& _key) -> std::string { return getBlackboardValue(_key); };

  // 仅支持新规范：3参数 + 前缀 + 事务
  lua_state_["bb_set"] = [this](const std::string& _key, const std::string& _value, const std::string& _type) {
    std::string full_key = bb_prefix_.empty() ? _key : (bb_prefix_ + "." + _key);
    if (in_scope_)
    {
      pending_bb_[full_key] = std::make_pair(_value, _type);
      return;
    }
    setBlackboardValue(full_key, _value, _type);
  };

  // 事务API：begin_scope / commit_scope
  lua_state_["begin_scope"] = [this](sol::optional<std::string> _prefix) {
    pending_bb_.clear();
    in_scope_ = true;
    if (_prefix && !_prefix->empty())
    {
      bb_prefix_ = *_prefix;
    }
  };
  lua_state_["commit_scope"] = [this]() {
    for (const auto& kv_pair : pending_bb_)
    {
      setBlackboardValue(kv_pair.first, kv_pair.second.first, kv_pair.second.second);
    }
    pending_bb_.clear();
    in_scope_ = false;
  };

  // 常用解析辅助（示例两个，更多可按需补充）
  lua_state_["read_u8"] = [this](int _offset) -> int {
    if (_offset < 0 || static_cast<std::size_t>(_offset) >= script_data_.size())
    {
      return 0;
    }
    unsigned char byte_value = static_cast<unsigned char>(script_data_[static_cast<std::size_t>(_offset)]);
    return static_cast<int>(byte_value);
  };
  lua_state_["slice_hex"] = [this](int _offset, int _length) -> std::string {
    if (_offset < 0 || _length <= 0)
    {
      return {};
    }
    std::size_t start = static_cast<std::size_t>(_offset);
    std::size_t count = static_cast<std::size_t>(_length);
    if (start >= script_data_.size())
    {
      return {};
    }
    if (start + count > script_data_.size())
    {
      count = script_data_.size() - start;
    }
    std::vector<char> sub(script_data_.begin() + static_cast<int64_t>(start),
                          script_data_.begin() + static_cast<int64_t>(start + count));
    return bytesToHexString(sub);
  };

  // 自定义输出数据API
  lua_state_["set_output"] = [this](const std::string& _key, const std::string& _value) {
    setOutputData(_key, _value);
  };
  lua_state_["clear_output"] = [this]() { clearOutputData(); };

  lua_state_["bb_has"] = [this](const std::string& key) -> bool { return hasBlackboardKey(key); };

  lua_state_["log"] = [this](const std::string& message, const std::string& level) { logMessage(message, level); };

  // 添加便捷的类型特化函数
  lua_state_["bb_set_int"] = [this](const std::string& key, int value) {
    setBlackboardValue(key, std::to_string(value), "int");
  };

  lua_state_["bb_set_double"] = [this](const std::string& key, double value) {
    setBlackboardValue(key, std::to_string(value), "double");
  };

  lua_state_["bb_set_bool"] = [this](const std::string& key, bool value) {
    setBlackboardValue(key, value ? "true" : "false", "bool");
  };

  lua_state_["bb_get_int"] = [this](const std::string& key) -> int {
    if (!hasBlackboardKey(key))
    {
      return 0;
    }
    try
    {
      return blackboard_->get<int>(key);
    }
    catch (...)
    {
      return 0;
    }
  };

  lua_state_["bb_get_double"] = [this](const std::string& key) -> double {
    if (!hasBlackboardKey(key))
    {
      return 0.0;
    }
    try
    {
      return blackboard_->get<double>(key);
    }
    catch (...)
    {
      return 0.0;
    }
  };

  lua_state_["bb_get_bool"] = [this](const std::string& key) -> bool {
    if (!hasBlackboardKey(key))
    {
      return false;
    }
    try
    {
      return blackboard_->get<bool>(key);
    }
    catch (...)
    {
      return false;
    }
  };

  // 添加BT节点状态常量
  lua_state_["OK"]      = static_cast<int>(BT::NodeStatus::SUCCESS);
  lua_state_["NG"]      = static_cast<int>(BT::NodeStatus::FAILURE);
  lua_state_["RUNNING"] = static_cast<int>(BT::NodeStatus::RUNNING);
}

bool UdpDataParserNode::startUdpReceiver()
{
  try
  {
    if (udp_started_)
    {
      stopUdpReceiver();
    }

    // 创建UDP接收器
    udp_receiver_ = std::make_unique<MEMSUDP>(packet_length_, timeout_ms_);

    // 注册回调函数
    udp_receiver_->regRecvCallback([this](const char* data) { onUdpDataReceived(data); });

    udp_receiver_->regTimeoutCallback([this]() { onUdpTimeout(); });

    // 启动UDP接收
    bool success = udp_receiver_->start(udp_ip_, udp_port_, group_ip_);
    if (success)
    {
      udp_started_ = true;
      logMessage("UDP receiver started on " + udp_ip_ + ":" + std::to_string(udp_port_), "info");
    }
    else
    {
      logMessage("Failed to start UDP receiver", "error");
    }

    return success;
  }
  catch (const std::exception& e)
  {
    logMessage("Exception in startUdpReceiver: " + std::string(e.what()), "error");
    return false;
  }
}

void UdpDataParserNode::stopUdpReceiver()
{
  if (udp_receiver_ && udp_started_)
  {
    udp_receiver_->stop();
    udp_started_ = false;
    logMessage("UDP receiver stopped", "info");
  }
}

void UdpDataParserNode::onUdpDataReceived(const char* _data)
{
  try
  {
    std::lock_guard<std::mutex> lock(data_mutex_);
    latest_data_.assign(_data, _data + packet_length_);
    data_available_.store(true);
    timeout_occurred_.store(false);

    logMessage("UDP data received, length: " + std::to_string(packet_length_), "info");
  }
  catch (const std::exception& e)
  {
    logMessage("Exception in onUdpDataReceived: " + std::string(e.what()), "error");
  }
}

void UdpDataParserNode::onUdpTimeout()
{
  timeout_occurred_.store(true);
  logMessage("UDP receive timeout", "warn");
}

BT::NodeStatus UdpDataParserNode::executeLuaParser(const std::vector<char>& _data)
{
  if (!lua_initialized_)
  {
    std::string error_msg = "Lua environment not initialized";
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }

  try
  {
    // 准备Lua脚本的输入数据
    lua_state_["data_bytes"]  = bytesToLuaTable(_data);
    lua_state_["data_hex"]    = bytesToHexString(_data);
    lua_state_["data_length"] = static_cast<int>(_data.size());

    // 每次执行脚本前清空输出数据
    clearOutputData();

    // 获取解析脚本
    std::string script_content;
    auto script_result = getInput<std::string>("parser_script");
    if (script_result)
    {
      script_content = script_result.value();
    }
    else
    {
      // 尝试从文件加载脚本
      auto script_file_result = getInput<std::string>("parser_script_file");
      if (script_file_result && !script_file_result.value().empty())
      {
        try
        {
          auto result = lua_state_.script_file(script_file_result.value());
          setOutput("parse_result", "Script file executed successfully");
          // 输出自定义数据
          auto output_json = buildOutputJson();
          setOutput("output_json", output_json);
          LOG_DEBUG("output_json : {}", output_json);
          return BT::NodeStatus::SUCCESS;
        }
        catch (const std::exception& e)
        {
          std::string error_msg = "Lua script file error: " + std::string(e.what());
          setOutput("error_message", error_msg);
          logMessage(error_msg, "error");
          return BT::NodeStatus::FAILURE;
        }
      }
      else
      {
        std::string error_msg = "No parser script provided";
        setOutput("error_message", error_msg);
        logMessage(error_msg, "error");
        return BT::NodeStatus::FAILURE;
      }
    }

    // 执行Lua脚本
    auto result = lua_state_.script(script_content);

    // 检查脚本是否返回了状态值
    if (result.valid())
    {
      sol::object return_value = result;
      if (return_value.is<int>())
      {
        int status_code = return_value.as<int>();
        if (status_code == static_cast<int>(BT::NodeStatus::SUCCESS) ||
            status_code == static_cast<int>(BT::NodeStatus::FAILURE) ||
            status_code == static_cast<int>(BT::NodeStatus::RUNNING))
        {
          setOutput("parse_result", "Script executed with return status: " + std::to_string(status_code));
          // 输出自定义数据
          auto output_json = buildOutputJson();
          setOutput("output_json", output_json);
          LOG_DEBUG("output_json : {}", output_json);
          return static_cast<BT::NodeStatus>(status_code);
        }
      }
      else if (return_value.is<std::string>())
      {
        std::string result_str = return_value.as<std::string>();
        setOutput("parse_result", result_str);
      }
    }

    setOutput("parse_result", "Script executed successfully");
    // 输出自定义数据
    auto output_json = buildOutputJson();
    setOutput("output_json", output_json);
    LOG_DEBUG("output_json : {}", output_json);
    return BT::NodeStatus::SUCCESS;
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "Lua parser execution error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }
}

std::string UdpDataParserNode::getBlackboardValue(const std::string& _key)
{
  if (!blackboard_ || !hasBlackboardKey(_key))
  {
    return "";
  }

  try
  {
    auto any_locked = blackboard_->getAnyLocked(_key);
    if (any_locked)
    {
      auto entry = blackboard_->getEntry(_key);
      if (entry && entry->string_converter)
      {
        // 使用 BT::toStr 进行类型转换
        return BT::toStr(any_locked.get());
      }
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error getting blackboard value for key '" + _key + "': " + e.what(), "error");
  }

  return "";
}

void UdpDataParserNode::setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type)
{
  if (!blackboard_)
  {
    return;
  }

  try
  {
    if (_type == "int")
    {
      blackboard_->set(_key, std::stoi(_value));
    }
    else if (_type == "double")
    {
      blackboard_->set(_key, std::stod(_value));
    }
    else if (_type == "bool")
    {
      blackboard_->set(_key, _value == "true" || _value == "1");
    }
    else
    {
      blackboard_->set(_key, _value);
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error setting blackboard value for key '" + _key + "': " + e.what(), "error");
  }
}

// ---------------- 自定义输出数据辅助 ----------------
void UdpDataParserNode::clearOutputData() { output_data_.clear(); }

void UdpDataParserNode::setOutputData(const std::string& _key, const std::string& _value)
{
  output_data_[_key] = _value;
}

std::string UdpDataParserNode::buildOutputJson() const
{
  if (output_data_.empty())
  {
    return "{}";
  }

  std::ostringstream oss;
  oss << "{";
  bool first_item = true;
  for (const auto& kv_pair : output_data_)
  {
    if (!first_item)
    {
      oss << ",";
    }
    oss << "\"" << kv_pair.first << "\":\"" << kv_pair.second << "\"";
    first_item = false;
  }
  oss << "}";
  return oss.str();
}

bool UdpDataParserNode::hasBlackboardKey(const std::string& _key)
{
  return blackboard_ && blackboard_->getEntry(_key) != nullptr;
}

void UdpDataParserNode::logMessage(const std::string& _message, const std::string& _level)
{
  if (_level == "error")
  {
    LOG_ERROR("[UdpDataParserNode:{}] : {}", name(), _message);
    return;
  }
  if (_level == "warn")
  {
    LOG_WARN("[UdpDataParserNode:{}] : {}", name(), _message);
    return;
  }
  LOG_INFO("[UdpDataParserNode:{}] : {}", name(), _message);
}

sol::table UdpDataParserNode::bytesToLuaTable(const std::vector<char>& _data)
{
  sol::table byte_table = lua_state_.create_table();
  for (size_t i = 0; i < _data.size(); ++i)
  {
    byte_table[i + 1] = static_cast<unsigned char>(_data[i]);  // Lua数组从1开始
  }
  return byte_table;
}

std::string UdpDataParserNode::bytesToHexString(const std::vector<char>& _data)
{
  std::ostringstream oss;
  oss << std::hex << std::uppercase << std::setfill('0');
  for (const auto& byte : _data)
  {
    oss << std::setw(2) << static_cast<unsigned char>(byte);
  }
  return oss.str();
}

// =============================================================================
// UdpDataParserAsyncNode 实现
// =============================================================================

UdpDataParserAsyncNode::UdpDataParserAsyncNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::StatefulActionNode(_name, _config),
  lua_initialized_(false),
  udp_started_(false),
  udp_port_(6699),
  packet_length_(1024),
  timeout_ms_(1000),
  data_available_(false),
  timeout_occurred_(false),
  parsing_in_progress_(false)
{
  blackboard_ = _config.blackboard;
  initializeLuaEnvironment();
}

UdpDataParserAsyncNode::~UdpDataParserAsyncNode() { stopUdpReceiver(); }

BT::PortsList UdpDataParserAsyncNode::providedPorts()
{
  return UdpDataParserNode::providedPorts();  // 使用相同的端口配置
}

BT::NodeStatus UdpDataParserAsyncNode::onStart()
{
  try
  {
    parsing_in_progress_ = false;
    start_time_          = std::chrono::steady_clock::now();

    // 获取配置参数
    auto udp_ip_result        = getInput<std::string>("udp_ip");
    auto udp_port_result      = getInput<int>("udp_port");
    auto packet_length_result = getInput<int>("packet_length");
    auto timeout_ms_result    = getInput<int>("timeout_ms");
    auto group_ip_result      = getInput<std::string>("group_ip");
    auto auto_start_result    = getInput<bool>("auto_start");

    if (udp_ip_result)
    {
      udp_ip_ = udp_ip_result.value();
    }
    if (udp_port_result)
    {
      udp_port_ = static_cast<uint16_t>(udp_port_result.value());
    }
    if (packet_length_result)
    {
      packet_length_ = static_cast<std::size_t>(packet_length_result.value());
    }
    if (timeout_ms_result)
    {
      timeout_ms_ = static_cast<std::size_t>(timeout_ms_result.value());
    }
    if (group_ip_result)
    {
      group_ip_ = group_ip_result.value();
    }

    bool auto_start = auto_start_result ? auto_start_result.value() : true;

    // 如果需要自动启动且UDP未启动，则启动UDP接收
    if (auto_start && !udp_started_)
    {
      if (!startUdpReceiver())
      {
        setOutput("error_message", "Failed to start UDP receiver");
        return BT::NodeStatus::FAILURE;
      }
    }

    return BT::NodeStatus::RUNNING;
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "UDP parser async start error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }
}

BT::NodeStatus UdpDataParserAsyncNode::onRunning()
{
  try
  {
    // 检查是否有新数据
    if (!data_available_.load())
    {
      // 检查是否超时
      if (timeout_occurred_.load())
      {
        setOutput("timeout_occurred", true);
        setOutput("error_message", "UDP receive timeout");
        timeout_occurred_.store(false);  // 重置超时标志
        return BT::NodeStatus::FAILURE;
      }

      // 没有新数据，继续等待
      setOutput("data_available", false);
      return BT::NodeStatus::RUNNING;
    }

    // 获取最新数据
    std::vector<char> data_copy;
    {
      std::lock_guard<std::mutex> lock(data_mutex_);
      data_copy = latest_data_;
      data_available_.store(false);  // 重置数据可用标志
    }

    // 设置输出端口
    setOutput("data_available", true);
    setOutput("data_length", static_cast<int>(data_copy.size()));
    setOutput("data_hex", bytesToHexString(data_copy));
    setOutput("timeout_occurred", false);

    // 执行Lua解析脚本
    parsing_in_progress_  = true;
    BT::NodeStatus result = executeLuaParser(data_copy);
    parsing_in_progress_  = false;

    return result;
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "UDP parser async running error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    parsing_in_progress_ = false;
    return BT::NodeStatus::FAILURE;
  }
}

void UdpDataParserAsyncNode::onHalted()
{
  parsing_in_progress_ = false;
  logMessage("UDP parser async node halted", "info");
}

// 异步版本的私有方法实现（复用同步版本的逻辑）
void UdpDataParserAsyncNode::initializeLuaEnvironment()
{
  try
  {
    lua_state_.open_libraries(sol::lib::base, sol::lib::string, sol::lib::math, sol::lib::table, sol::lib::io,
                              sol::lib::os);
    setupBlackboardAccess();
    lua_initialized_ = true;
    logMessage("Lua environment initialized successfully", "info");
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "Failed to initialize Lua environment: " + std::string(e.what());
    logMessage(error_msg, "error");
    lua_initialized_ = false;
  }
}

void UdpDataParserAsyncNode::setupBlackboardAccess()
{
  lua_state_["bb_get"] = [this](const std::string& key) -> std::string { return getBlackboardValue(key); };

  lua_state_["bb_set"] = [this](const std::string& key, const std::string& value, const std::string& type) {
    setBlackboardValue(key, value, type);
  };
  lua_state_["bb_has"] = [this](const std::string& key) -> bool { return hasBlackboardKey(key); };
  lua_state_["log"]    = [this](const std::string& message, const std::string& level) { logMessage(message, level); };

  // 类型特化函数
  lua_state_["bb_set_int"] = [this](const std::string& key, int value) {
    setBlackboardValue(key, std::to_string(value), "int");
  };
  lua_state_["bb_set_double"] = [this](const std::string& key, double value) {
    setBlackboardValue(key, std::to_string(value), "double");
  };
  lua_state_["bb_set_bool"] = [this](const std::string& key, bool value) {
    setBlackboardValue(key, value ? "true" : "false", "bool");
  };
  lua_state_["bb_get_int"] = [this](const std::string& key) -> int {
    if (!hasBlackboardKey(key))
    {
      return 0;
    }
    try
    {
      return blackboard_->get<int>(key);
    }
    catch (...)
    {
      return 0;
    }
  };
  lua_state_["bb_get_double"] = [this](const std::string& key) -> double {
    if (!hasBlackboardKey(key))
    {
      return 0.0;
    }
    try
    {
      return blackboard_->get<double>(key);
    }
    catch (...)
    {
      return 0.0;
    }
  };
  lua_state_["bb_get_bool"] = [this](const std::string& key) -> bool {
    if (!hasBlackboardKey(key))
    {
      return false;
    }
    try
    {
      return blackboard_->get<bool>(key);
    }
    catch (...)
    {
      return false;
    }
  };

  // BT节点状态常量
  lua_state_["OK"]      = static_cast<int>(BT::NodeStatus::SUCCESS);
  lua_state_["NG"]      = static_cast<int>(BT::NodeStatus::FAILURE);
  lua_state_["RUNNING"] = static_cast<int>(BT::NodeStatus::RUNNING);
}

// 其他方法复用同步版本的实现
bool UdpDataParserAsyncNode::startUdpReceiver()
{
  try
  {
    if (udp_started_)
    {
      stopUdpReceiver();
    }

    udp_receiver_ = std::make_unique<MEMSUDP>(packet_length_, timeout_ms_);

    udp_receiver_->regRecvCallback([this](const char* data) { onUdpDataReceived(data); });

    udp_receiver_->regTimeoutCallback([this]() { onUdpTimeout(); });

    bool success = udp_receiver_->start(udp_ip_, udp_port_, group_ip_);
    if (success)
    {
      udp_started_ = true;
      logMessage("UDP receiver started on " + udp_ip_ + ":" + std::to_string(udp_port_), "info");
    }
    else
    {
      logMessage("Failed to start UDP receiver", "error");
    }

    return success;
  }
  catch (const std::exception& e)
  {
    logMessage("Exception in startUdpReceiver: " + std::string(e.what()), "error");
    return false;
  }
}

void UdpDataParserAsyncNode::stopUdpReceiver()
{
  if (udp_receiver_ && udp_started_)
  {
    udp_receiver_->stop();
    udp_started_ = false;
    logMessage("UDP receiver stopped", "info");
  }
}

void UdpDataParserAsyncNode::onUdpDataReceived(const char* _data)
{
  try
  {
    std::lock_guard<std::mutex> lock(data_mutex_);
    latest_data_.assign(_data, _data + packet_length_);
    data_available_.store(true);
    timeout_occurred_.store(false);

    logMessage("UDP data received, length: " + std::to_string(packet_length_), "info");
  }
  catch (const std::exception& e)
  {
    logMessage("Exception in onUdpDataReceived: " + std::string(e.what()), "error");
  }
}

void UdpDataParserAsyncNode::onUdpTimeout()
{
  timeout_occurred_.store(true);
  logMessage("UDP receive timeout", "warn");
}

// 其他辅助方法的实现（复用同步版本的逻辑）
BT::NodeStatus UdpDataParserAsyncNode::executeLuaParser(const std::vector<char>& _data)
{
  if (!lua_initialized_)
  {
    std::string error_msg = "Lua environment not initialized";
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }

  try
  {
    lua_state_["data_bytes"]  = bytesToLuaTable(_data);
    lua_state_["data_hex"]    = bytesToHexString(_data);
    lua_state_["data_length"] = static_cast<int>(_data.size());

    std::string script_content;
    auto script_result = getInput<std::string>("parser_script");
    if (script_result)
    {
      script_content = script_result.value();
    }
    else
    {
      auto script_file_result = getInput<std::string>("parser_script_file");
      if (script_file_result && !script_file_result.value().empty())
      {
        try
        {
          auto result = lua_state_.script_file(script_file_result.value());
          setOutput("parse_result", "Script file executed successfully");
          return BT::NodeStatus::SUCCESS;
        }
        catch (const std::exception& e)
        {
          std::string error_msg = "Lua script file error: " + std::string(e.what());
          setOutput("error_message", error_msg);
          logMessage(error_msg, "error");
          return BT::NodeStatus::FAILURE;
        }
      }
      else
      {
        std::string error_msg = "No parser script provided";
        setOutput("error_message", error_msg);
        logMessage(error_msg, "error");
        return BT::NodeStatus::FAILURE;
      }
    }

    auto result = lua_state_.script(script_content);

    if (result.valid())
    {
      sol::object return_value = result;
      if (return_value.is<int>())
      {
        int status_code = return_value.as<int>();
        if (status_code == static_cast<int>(BT::NodeStatus::SUCCESS) ||
            status_code == static_cast<int>(BT::NodeStatus::FAILURE) ||
            status_code == static_cast<int>(BT::NodeStatus::RUNNING))
        {
          setOutput("parse_result", "Script executed with return status: " + std::to_string(status_code));
          return static_cast<BT::NodeStatus>(status_code);
        }
      }
      else if (return_value.is<std::string>())
      {
        std::string result_str = return_value.as<std::string>();
        setOutput("parse_result", result_str);
      }
    }

    setOutput("parse_result", "Script executed successfully");
    return BT::NodeStatus::SUCCESS;
  }
  catch (const std::exception& e)
  {
    std::string error_msg = "Lua parser execution error: " + std::string(e.what());
    setOutput("error_message", error_msg);
    logMessage(error_msg, "error");
    return BT::NodeStatus::FAILURE;
  }
}

std::string UdpDataParserAsyncNode::getBlackboardValue(const std::string& _key)
{
  if (!blackboard_ || !hasBlackboardKey(_key))
  {
    return "";
  }

  try
  {
    auto any_locked = blackboard_->getAnyLocked(_key);
    if (any_locked)
    {
      auto entry = blackboard_->getEntry(_key);
      if (entry && entry->string_converter)
      {
        return BT::toStr(any_locked.get());
      }
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error getting blackboard value for key '" + _key + "': " + e.what(), "error");
  }

  return "";
}

void UdpDataParserAsyncNode::setBlackboardValue(const std::string& _key,
                                                const std::string& _value,
                                                const std::string& _type)
{
  if (!blackboard_)
  {
    return;
  }

  try
  {
    if (_type == "int")
    {
      blackboard_->set(_key, std::stoi(_value));
    }
    else if (_type == "double")
    {
      blackboard_->set(_key, std::stod(_value));
    }
    else if (_type == "bool")
    {
      blackboard_->set(_key, _value == "true" || _value == "1");
    }
    else
    {
      blackboard_->set(_key, _value);
    }
  }
  catch (const std::exception& e)
  {
    logMessage("Error setting blackboard value for key '" + _key + "': " + e.what(), "error");
  }
}

bool UdpDataParserAsyncNode::hasBlackboardKey(const std::string& _key)
{
  return blackboard_ && blackboard_->getEntry(_key) != nullptr;
}

void UdpDataParserAsyncNode::logMessage(const std::string& _message, const std::string& _level)
{
  if (_level == "error")
  {
    LOG_ERROR("[UdpDataParserAsyncNode:{}] : {}", name(), _message);
  }
  else if (_level == "warn")
  {
    LOG_WARN("[UdpDataParserAsyncNode:{}] : {}", name(), _message);
  }
  else
  {
    LOG_INFO("[UdpDataParserAsyncNode:{}] : {}", name(), _message);
  }
}

sol::table UdpDataParserAsyncNode::bytesToLuaTable(const std::vector<char>& _data)
{
  sol::table byte_table = lua_state_.create_table();
  for (size_t i = 0; i < _data.size(); ++i)
  {
    byte_table[i + 1] = static_cast<unsigned char>(_data[i]);  // Lua数组从1开始
  }
  return byte_table;
}

std::string UdpDataParserAsyncNode::bytesToHexString(const std::vector<char>& _data)
{
  std::ostringstream oss;
  oss << std::hex << std::uppercase << std::setfill('0');
  for (const auto& byte : _data)
  {
    oss << std::setw(2) << static_cast<unsigned char>(byte);
  }
  return oss.str();
}

}  // namespace robosense::lidar
