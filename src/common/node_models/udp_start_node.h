#pragma once

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/blackboard.h"
#include <atomic>
#include <memory>
#include <mutex>
#include <string>

// 包含 MEMSUDP - 使用本地增强版本
#include "../mems_udp.h"

namespace robosense::lidar
{

/**
 * @brief UDP开始节点
 * 
 * 负责启动UDP接收器，配置监听参数，为后续的数据采集做准备。
 * 这个节点只负责启动UDP服务，不进行数据处理。
 */
class UdpStartNode : public BT::SyncActionNode
{
public:
  UdpStartNode(const std::string& _name, const BT::NodeConfig& _config);

  // 禁用拷贝和移动
  UdpStartNode(const UdpStartNode&)            = delete;
  UdpStartNode& operator=(const UdpStartNode&) = delete;
  UdpStartNode(UdpStartNode&&)                 = delete;
  UdpStartNode& operator=(UdpStartNode&&)      = delete;

  ~UdpStartNode() override;

  /**
   * @brief 提供节点端口配置
   */
  static BT::PortsList providedPorts();

  /**
   * @brief 节点执行函数
   */
  BT::NodeStatus tick() override;

  /**
   * @brief 获取共享数据
   */
  static bool getSharedData(const std::string& _key, std::vector<char>& _data);

  /**
   * @brief 设置共享数据
   */
  static void setSharedData(const std::string& _key, const std::vector<char>& _data);

  /**
   * @brief 检查是否有共享数据可用
   */
  static bool isSharedDataAvailable(const std::string& _key);

private:
  /**
   * @brief 启动UDP接收器
   */
  bool startUdpReceiver();

  /**
   * @brief 停止UDP接收器
   */
  void stopUdpReceiver();

  /**
   * @brief UDP数据接收回调（暂存数据）
   */
  void onUdpDataReceived(const char* _data);

  /**
   * @brief UDP超时回调
   */
  void onUdpTimeout();

private:
  std::shared_ptr<BT::Blackboard> blackboard_;  // 黑板引用

  // UDP配置参数
  std::string udp_ip_;         // UDP IP地址
  uint16_t udp_port_;          // UDP端口
  std::size_t packet_length_;  // 数据包长度
  std::size_t timeout_ms_;     // 超时时间(毫秒)
  std::string group_ip_;       // 组播IP地址
  std::string receiver_id_;    // 接收器唯一标识

  // UDP接收器
  std::shared_ptr<MEMSUDP> udp_receiver_;  // UDP接收器
  bool udp_started_;                       // UDP是否已启动

  // 数据缓存（用于验证连接）
  std::mutex data_mutex_;               // 数据互斥锁
  std::vector<char> latest_data_;       // 最新接收的数据
  std::atomic<bool> data_available_;    // 是否有新数据可用
  std::atomic<bool> timeout_occurred_;  // 是否发生超时

  // 共享数据缓冲区（用于与UDP数据采集节点共享数据）
  static std::mutex shared_data_mutex_;
  static std::unordered_map<std::string, std::vector<char>> shared_data_buffers_;
  static std::unordered_map<std::string, std::atomic<bool>> shared_data_available_flags_;
};

}  // namespace robosense::lidar
