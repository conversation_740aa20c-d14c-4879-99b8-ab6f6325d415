#include "udp_start_node.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <chrono>
#include <iostream>
#include <sstream>

namespace robosense::lidar
{

// 静态成员初始化
std::mutex UdpStartNode::shared_data_mutex_;
std::unordered_map<std::string, std::vector<char>> UdpStartNode::shared_data_buffers_;
std::unordered_map<std::string, std::atomic<bool>> UdpStartNode::shared_data_available_flags_;

UdpStartNode::UdpStartNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config),
  blackboard_(_config.blackboard),
  udp_port_(6699),
  packet_length_(1024),
  timeout_ms_(1000),
  udp_started_(false),
  data_available_(false),
  timeout_occurred_(false)
{
  LOG_INFO("[UdpStartNode:{}] 节点初始化完成", name());
}

UdpStartNode::~UdpStartNode() { stopUdpReceiver(); }

BT::PortsList UdpStartNode::providedPorts()
{
  return {
    // 输入端口 - UDP配置
    BT::InputPort<std::string>("udp_ip", "0.0.0.0", "UDP监听IP地址"),
    BT::InputPort<int>("udp_port", 6699, "UDP监听端口"), BT::InputPort<int>("packet_length", 1024, "期望的数据包长度"),
    BT::InputPort<int>("timeout_ms", 1000, "接收超时时间(毫秒)"),
    BT::InputPort<std::string>("group_ip", "", "组播IP地址(可选)"),
    BT::InputPort<std::string>("receiver_id", "default", "UDP接收器唯一标识"),

    // 输出端口 - 状态信息
    BT::OutputPort<std::string>("result", "UDP启动结果"), BT::OutputPort<std::string>("error_message", "错误信息"),
    BT::OutputPort<bool>("udp_started", "UDP是否已启动"),
    BT::OutputPort<std::shared_ptr<MEMSUDP>>("udp_handle", "UDP接收器句柄")
  };
}

BT::NodeStatus UdpStartNode::tick()
{
  try
  {
    // 读取配置参数
    auto udp_ip_opt        = getInput<std::string>("udp_ip");
    auto udp_port_opt      = getInput<int>("udp_port");
    auto packet_length_opt = getInput<int>("packet_length");
    auto timeout_ms_opt    = getInput<int>("timeout_ms");
    auto group_ip_opt      = getInput<std::string>("group_ip");
    auto receiver_id_opt   = getInput<std::string>("receiver_id");

    if (udp_ip_opt)
      udp_ip_ = udp_ip_opt.value();
    if (udp_port_opt)
      udp_port_ = static_cast<uint16_t>(udp_port_opt.value());
    if (packet_length_opt)
      packet_length_ = static_cast<std::size_t>(packet_length_opt.value());
    if (timeout_ms_opt)
      timeout_ms_ = static_cast<std::size_t>(timeout_ms_opt.value());
    if (group_ip_opt)
      group_ip_ = group_ip_opt.value();
    if (receiver_id_opt)
      receiver_id_ = receiver_id_opt.value();

    LOG_INFO("[UdpStartNode:{}] 配置参数: IP={}, Port={}, PacketLength={}, Timeout={}ms, GroupIP={}, ReceiverID={}",
             name(), udp_ip_, udp_port_, packet_length_, timeout_ms_, group_ip_, receiver_id_);

    // 启动UDP接收器
    if (startUdpReceiver())
    {
      LOG_INFO("[UdpStartNode:{}] 成功启动UDP接收器", name());
      udp_started_ = true;

      // 设置输出
      setOutput("result", "SUCCESS");
      setOutput("error_message", "");
      setOutput("udp_started", true);
      setOutput("udp_handle", udp_receiver_);

      // 将接收器信息存储到黑板
      if (blackboard_)
      {
        blackboard_->set("udp_started", true);
      }

      LOG_INFO("[UdpStartNode:{}] UDP启动成功", name());
    }
    else
    {
      LOG_ERROR("[UdpStartNode:{}] 启动UDP接收器失败", name());
      setOutput("result", "FAILED");
      setOutput("error_message", "启动UDP接收器失败");
      setOutput("udp_started", false);
      return BT::NodeStatus::FAILURE;
    }
    return BT::NodeStatus::SUCCESS;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("[UdpStartNode:{}] 执行异常: {}", name(), e.what());
    setOutput("result", "ERROR");
    setOutput("error_message", e.what());
    setOutput("udp_started", false);
    return BT::NodeStatus::FAILURE;
  }
}

bool UdpStartNode::startUdpReceiver()
{
  try
  {
    // 创建UDP接收器（需要指定数据包长度）
    udp_receiver_ = std::make_shared<MEMSUDP>(packet_length_, timeout_ms_);

    // 设置回调函数
    udp_receiver_->regRecvCallback([this](const char* data) { onUdpDataReceived(data); });
    udp_receiver_->regTimeoutCallback([this]() { onUdpTimeout(); });

    // 启动UDP接收
    bool success = false;
    if (!group_ip_.empty())
    {
      // 组播模式
      success = udp_receiver_->start(udp_ip_, udp_port_, group_ip_);
      LOG_INFO("[UdpStartNode:{}] 启动UDP组播接收: {}:{} (组播: {})", name(), udp_ip_, udp_port_, group_ip_);
    }
    else
    {
      // 单播模式
      success = udp_receiver_->start(udp_ip_, udp_port_);
      LOG_INFO("[UdpStartNode:{}] 启动UDP单播接收: {}:{}", name(), udp_ip_, udp_port_);
    }

    if (success)
    {
      LOG_INFO("[UdpStartNode:{}] UDP接收器启动成功", name());
      return true;
    }
    else
    {
      LOG_ERROR("[UdpStartNode:{}] UDP接收器启动失败", name());
      udp_receiver_.reset();
      return false;
    }
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("[UdpStartNode:{}] 启动UDP接收器异常: {}", name(), e.what());
    udp_receiver_.reset();
    return false;
  }
}

void UdpStartNode::stopUdpReceiver()
{
  if (udp_receiver_)
  {
    udp_receiver_->stop();
    udp_receiver_.reset();
    udp_started_ = false;
    LOG_INFO("[UdpStartNode:{}] UDP接收器已停止", name());
  }
}

void UdpStartNode::onUdpDataReceived(const char* _data)
{
  std::lock_guard<std::mutex> lock(data_mutex_);

  // 暂存数据用于验证连接
  latest_data_.assign(_data, _data + packet_length_);
  data_available_   = true;
  timeout_occurred_ = false;

  // 将数据存储到共享缓冲区
  std::string data_key = receiver_id_;
  setSharedData(data_key, latest_data_);

  LOG_DEBUG("[UdpStartNode:{}] 接收到UDP数据，长度: {}，已存储到共享缓冲区: {}", name(), packet_length_, data_key);
}

void UdpStartNode::onUdpTimeout()
{
  timeout_occurred_ = true;
  LOG_DEBUG("[UdpStartNode:{}] UDP接收超时", name());
}

bool UdpStartNode::getSharedData(const std::string& _key, std::vector<char>& _data)
{
  std::lock_guard<std::mutex> lock(shared_data_mutex_);
  auto it = shared_data_buffers_.find(_key);
  if (it != shared_data_buffers_.end())
  {
    _data = it->second;
    // 读取后清除数据可用标志
    auto flag_it = shared_data_available_flags_.find(_key);
    if (flag_it != shared_data_available_flags_.end())
    {
      flag_it->second = false;
    }
    return true;
  }
  return false;
}

void UdpStartNode::setSharedData(const std::string& _key, const std::vector<char>& _data)
{
  std::lock_guard<std::mutex> lock(shared_data_mutex_);
  shared_data_buffers_[_key]         = _data;
  shared_data_available_flags_[_key] = true;
}

bool UdpStartNode::isSharedDataAvailable(const std::string& _key)
{
  std::lock_guard<std::mutex> lock(shared_data_mutex_);
  auto it = shared_data_available_flags_.find(_key);
  return (it != shared_data_available_flags_.end()) && it->second.load();
}

}  // namespace robosense::lidar
