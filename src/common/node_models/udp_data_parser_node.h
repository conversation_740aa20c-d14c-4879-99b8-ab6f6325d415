#pragma once

#include "behaviortree_cpp/action_node.h"
#include "behaviortree_cpp/blackboard.h"
#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

// 直接包含 Sol2 头文件
#define SOL_ALL_SAFETIES_ON 1
#include <sol/sol.hpp>

// 包含 MEMSUDP - 使用本地增强版本
#include "../mems_udp.h"

namespace robosense::lidar
{

/**
 * @brief UDP数据解析节点
 * 
 * 这个节点使用MEMSUDP接收UDP数据，并通过Lua脚本进行数据解析。
 * 解析后的数据可以存储到黑板或输出给其他节点。
 */
class UdpDataParserNode : public BT::SyncActionNode
{
public:
  UdpDataParserNode(const std::string& _name, const BT::NodeConfig& _config);

  // 禁用拷贝和移动
  UdpDataParserNode(const UdpDataParserNode&)            = delete;
  UdpDataParserNode& operator=(const UdpDataParserNode&) = delete;
  UdpDataParserNode(UdpDataParserNode&&)                 = delete;
  UdpDataParserNode& operator=(UdpDataParserNode&&)      = delete;

  ~UdpDataParserNode() override;

  /**
   * @brief 提供节点端口配置
   */
  static BT::PortsList providedPorts();

  /**
   * @brief 节点执行函数
   */
  BT::NodeStatus tick() override;

private:
  struct ParsedField
  {
    std::string name_;
    std::string value_;
    std::string type_;
    int offset_ { 0 };
    int length_ { 0 };
    std::string desc_;
  };

  void clearOutputData();
  void setOutputData(const std::string& _key, const std::string& _value);
  std::string buildOutputJson() const;
  /**
   * @brief 初始化Lua环境
   */
  void initializeLuaEnvironment();

  /**
   * @brief 设置黑板访问函数
   */
  void setupBlackboardAccess();

  /**
   * @brief 启动UDP接收
   */
  bool startUdpReceiver();

  /**
   * @brief 停止UDP接收
   */
  void stopUdpReceiver();

  /**
   * @brief UDP数据接收回调
   */
  void onUdpDataReceived(const char* _data);

  /**
   * @brief UDP超时回调
   */
  void onUdpTimeout();

  /**
   * @brief 执行Lua解析脚本
   */
  BT::NodeStatus executeLuaParser(const std::vector<char>& _data);

  /**
   * @brief 获取黑板值
   */
  std::string getBlackboardValue(const std::string& _key);

  /**
   * @brief 设置黑板值
   */
  void setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type);

  /**
   * @brief 检查黑板键是否存在
   */
  bool hasBlackboardKey(const std::string& _key);

  /**
   * @brief 记录日志
   */
  void logMessage(const std::string& _message, const std::string& _level);

  /**
   * @brief 将字节数据转换为Lua表
   */
  sol::table bytesToLuaTable(const std::vector<char>& _data);

  /**
   * @brief 将字节数据转换为十六进制字符串
   */
  std::string bytesToHexString(const std::vector<char>& _data);

private:
  sol::state lua_state_;                        // Lua状态机
  std::shared_ptr<BT::Blackboard> blackboard_;  // 黑板引用
  bool lua_initialized_;                        // Lua环境是否已初始化

  // UDP相关
  std::unique_ptr<MEMSUDP> udp_receiver_;  // UDP接收器
  bool udp_started_;                       // UDP是否已启动
  std::string udp_ip_;                     // UDP IP地址
  uint16_t udp_port_;                      // UDP端口
  std::size_t packet_length_;              // 数据包长度
  std::size_t timeout_ms_;                 // 超时时间(毫秒)
  std::string group_ip_;                   // 组播IP地址

  // 数据缓存
  std::mutex data_mutex_;               // 数据互斥锁
  std::vector<char> latest_data_;       // 最新接收的数据
  std::atomic<bool> data_available_;    // 是否有新数据可用
  std::atomic<bool> timeout_occurred_;  // 是否发生超时

  // 解析结果
  std::string parse_result_;   // 解析结果
  std::string error_message_;  // 错误信息

  // 黑板前缀与事务
  std::string bb_prefix_;
  std::unordered_map<std::string, std::pair<std::string, std::string>> pending_bb_;
  bool in_scope_ { false };

  // 脚本期间可读的数据拷贝
  std::vector<char> script_data_;

  // 自定义输出数据缓存
  std::unordered_map<std::string, std::string> output_data_;
};

/**
 * @brief UDP数据解析异步节点
 * 
 * 异步版本的UDP数据解析节点，适用于长时间运行的解析任务
 */
class UdpDataParserAsyncNode : public BT::StatefulActionNode
{
public:
  UdpDataParserAsyncNode(const std::string& _name, const BT::NodeConfig& _config);

  // 禁用拷贝和移动
  UdpDataParserAsyncNode(const UdpDataParserAsyncNode&)            = delete;
  UdpDataParserAsyncNode& operator=(const UdpDataParserAsyncNode&) = delete;
  UdpDataParserAsyncNode(UdpDataParserAsyncNode&&)                 = delete;
  UdpDataParserAsyncNode& operator=(UdpDataParserAsyncNode&&)      = delete;

  ~UdpDataParserAsyncNode() override;

  /**
   * @brief 提供节点端口配置
   */
  static BT::PortsList providedPorts();

  /**
   * @brief 节点启动函数
   */
  BT::NodeStatus onStart() override;

  /**
   * @brief 节点运行函数
   */
  BT::NodeStatus onRunning() override;

  /**
   * @brief 节点停止函数
   */
  void onHalted() override;

private:
  // 与同步版本相同的私有方法
  void initializeLuaEnvironment();
  void setupBlackboardAccess();
  bool startUdpReceiver();
  void stopUdpReceiver();
  void onUdpDataReceived(const char* _data);
  void onUdpTimeout();
  BT::NodeStatus executeLuaParser(const std::vector<char>& _data);
  std::string getBlackboardValue(const std::string& _key);
  void setBlackboardValue(const std::string& _key, const std::string& _value, const std::string& _type);
  bool hasBlackboardKey(const std::string& _key);
  void logMessage(const std::string& _message, const std::string& _level);
  sol::table bytesToLuaTable(const std::vector<char>& _data);
  std::string bytesToHexString(const std::vector<char>& _data);

private:
  sol::state lua_state_;                        // Lua状态机
  std::shared_ptr<BT::Blackboard> blackboard_;  // 黑板引用
  bool lua_initialized_;                        // Lua环境是否已初始化

  // UDP相关
  std::unique_ptr<MEMSUDP> udp_receiver_;  // UDP接收器
  bool udp_started_;                       // UDP是否已启动
  std::string udp_ip_;                     // UDP IP地址
  uint16_t udp_port_;                      // UDP端口
  std::size_t packet_length_;              // 数据包长度
  std::size_t timeout_ms_;                 // 超时时间(毫秒)
  std::string group_ip_;                   // 组播IP地址

  // 数据缓存
  std::mutex data_mutex_;               // 数据互斥锁
  std::vector<char> latest_data_;       // 最新接收的数据
  std::atomic<bool> data_available_;    // 是否有新数据可用
  std::atomic<bool> timeout_occurred_;  // 是否发生超时

  // 解析结果
  std::string parse_result_;   // 解析结果
  std::string error_message_;  // 错误信息

  // 异步状态
  bool parsing_in_progress_;                          // 是否正在解析
  std::chrono::steady_clock::time_point start_time_;  // 开始时间
};

}  // namespace robosense::lidar
