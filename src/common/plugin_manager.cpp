﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "plugin_manager.h"
#include "common_types.h"
#include "node_models/node_manager.h"
#include "node_models/node_plugin_base.h"
#include "node_plugin_interface.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QPluginLoader>
#include <QtCore/QCoreApplication>
#include <QtCore/QDir>

namespace robosense::lidar
{

PluginManager& PluginManager::getInstance()
{
  static PluginManager instance;
  return instance;
}

PluginManager::PluginManager() { LOG_DEBUG("初始化插件管理器"); }

PluginManager::~PluginManager()
{
  // 卸载所有插件
  for (auto& pair : plugin_loaders_)
  {
    QPluginLoader* loader = pair.second;
    if (loader->isLoaded())
    {
      loader->unload();
    }
    delete loader;
  }
  plugin_loaders_.clear();
  all_node_infos_.nodes.clear();
}

int PluginManager::loadPlugins(const QString& _plugin_dir)
{
  NodeInfos {}.nodes.swap(all_node_infos_.nodes);
  QDir dir(_plugin_dir);
  if (!dir.exists())
  {
    LOG_ERROR("插件目录不存在: {}", _plugin_dir);
    return 0;
  }

  int loaded_count = 0;

  // 获取所有插件文件
  QStringList filters;
#ifdef Q_OS_WIN
  filters << "*.dll";
#else
  filters << "*.so"
          << "*.dylib";
#endif

  dir.setNameFilters(filters);
  QStringList plugin_files = dir.entryList(QDir::Files);

  LOG_INFO("找到 {} 个插件文件", plugin_files.size());

  for (const auto& file_name : plugin_files)
  {
    QString file_path = dir.absoluteFilePath(file_name);
    PluginLoaderGuard guard(file_path);
    auto* loader = guard.get();

    QObject* plugin = loader->instance();
    if (plugin == nullptr)
    {
      LOG_ERROR("加载插件失败: {} 错误: {}", file_path.toStdString(), loader->errorString().toStdString());
      continue;
    }

    auto* plugin_interface = qobject_cast<NodePluginInterface*>(plugin);
    if (plugin_interface == nullptr)
    {
      LOG_WARN("无效的插件接口: {}", file_path.toStdString());
      continue;
    }

    auto plugin_name = plugin_interface->name();
    // 检查插件是否已经加载
    if (plugin_loaders_.find(plugin_name) != plugin_loaders_.end())
    {
      LOG_WARN("插件已加载，跳过: {} ({})", plugin_name, file_path.toStdString());
      continue;
    }

    LOG_INFO("正在初始化插件: {}", plugin_name);
    if (!plugin_interface->initialize(&NodeManager::getInstance()))
    {
      LOG_ERROR("插件初始化失败: {}", plugin_name);
      continue;
    }

    plugin_loaders_[plugin_name] = guard.release();  // 成功时转移所有权

    // 获取并记录插件提供的节点类型
    auto node_infos = plugin_interface->nodeInfos();
    if (node_infos.nodes.empty())
    {
      LOG_WARN("插件 {} 没有提供任何节点类型", plugin_name);
    }
    else
    {
      LOG_DEBUG("插件 {} 提供了以下节点类型:", plugin_name);
      all_node_infos_.nodes.swap(node_infos.nodes);
      all_node_infos_.plugin_name = plugin_name;
      for (const auto& node : node_infos.nodes)
      {
        LOG_DEBUG("  - {}", node.node_type);
      }
    }

    // 再次检查注册表，确认节点是否已注册
    auto registry = NodeManager::getInstance().qtNodeRegistry();
    auto creators = registry->registeredModelCreators();
    LOG_DEBUG("插件加载后的注册节点模型:");
    for (const auto& pair : creators)
    {
      LOG_DEBUG("  - {}", pair.first.toStdString());
    }

    LOG_DEBUG("成功加载插件: {} 版本: {}", plugin_name, plugin_interface->version());
    loaded_count++;
  }

  return loaded_count;
}

QPluginLoader* PluginManager::pluginLoader(const std::string& _name) const
{
  auto iter = plugin_loaders_.find(_name);
  return (iter != plugin_loaders_.end()) ? iter->second : nullptr;
}

NodeInfos PluginManager::allNodeInfos() const { return all_node_infos_; }

PluginLoaderGuard::PluginLoaderGuard(const QString& _file_path) : loader_(new QPluginLoader(_file_path)) {}

PluginLoaderGuard::~PluginLoaderGuard()
{
  if (loader_ != nullptr)
  {
    if (loader_->isLoaded())
    {
      loader_->unload();
    }
    delete loader_;
  }
}

PluginLoaderGuard::PluginLoaderGuard(PluginLoaderGuard&& _other) noexcept : loader_(_other.loader_)
{
  _other.loader_ = nullptr;
}

QPluginLoader* PluginLoaderGuard::release()
{
  QPluginLoader* tmp = loader_;
  loader_            = nullptr;
  return tmp;
}

QPluginLoader* PluginLoaderGuard::get() const { return loader_; }

}  // namespace robosense::lidar
