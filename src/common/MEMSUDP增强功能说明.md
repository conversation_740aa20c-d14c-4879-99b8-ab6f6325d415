# MEMSUDP 增强功能使用说明

## 概述

本次对 `MEMSUDP` 类进行了增强，新增了支持动态设置特定长度和特定超时等待接收的功能。这些增强功能使得 UDP 数据接收更加灵活和可控。

## 新增功能

### 1. 带超时的构造函数

```cpp
/**
 * @brief     Construct a new MEMSUDP object with custom timeout
 * 
 * @param     _len               captured udp's length is not equal with _len will be delete
 * @param     _timeout_ms        timeout in milliseconds for receiving data
 */
explicit MEMSUDP(std::size_t _len, std::size_t _timeout_ms);
```

**使用示例：**
```cpp
// 创建一个期望包长度为1024字节，超时时间为5秒的UDP接收器
auto udp_receiver = std::make_unique<MEMSUDP>(1024, 5000);
```

### 2. 动态设置包长度

```cpp
/**
 * @brief     set expected packet length for udp receive
 * 
 * @param     _len               new packet length to expect
 */
void setPacketLength(const std::size_t _len);
```

**使用示例：**
```cpp
udp_receiver->setPacketLength(2048);  // 动态调整期望的包长度为2048字节
```

### 3. 获取当前包长度

```cpp
/**
 * @brief     get current packet length setting
 * 
 * @retval    std::size_t        current packet length
 */
std::size_t getPacketLength() const;
```

**使用示例：**
```cpp
std::size_t current_length = udp_receiver->getPacketLength();
std::cout << "当前包长度: " << current_length << " 字节" << std::endl;
```

### 4. 获取当前超时设置

```cpp
/**
 * @brief     get current timeout setting
 * 
 * @retval    std::size_t        current timeout in milliseconds
 */
std::size_t getTimeoutMS() const;
```

**使用示例：**
```cpp
std::size_t current_timeout = udp_receiver->getTimeoutMS();
std::cout << "当前超时时间: " << current_timeout << " 毫秒" << std::endl;
```

### 5. 阻塞式特定长度和超时接收

```cpp
/**
 * @brief     receive data with specific length and timeout (blocking call)
 *
 * @param     _buffer            buffer to store received data
 * @param     _expected_len      expected length of data to receive
 * @param     _timeout_ms        timeout in milliseconds for this specific receive
 * @retval    true               data received successfully
 * @retval    false              timeout or error occurred
 */
bool receiveWithTimeout(std::vector<char>& _buffer, std::size_t _expected_len, std::size_t _timeout_ms);
```

**使用示例：**
```cpp
std::vector<char> receive_buffer;
bool success = udp_receiver->receiveWithTimeout(receive_buffer, 256, 2000);
if (success) {
    std::cout << "成功接收到 " << receive_buffer.size() << " 字节数据" << std::endl;
} else {
    std::cout << "接收失败或超时" << std::endl;
}
```

### 6. 超时自动退出功能

```cpp
/**
 * @brief     enable auto stop when timeout occurs continuously
 *
 * @param     _max_timeout_count maximum continuous timeout count before auto stop
 */
void enableAutoStopOnTimeout(std::size_t _max_timeout_count);

/**
 * @brief     disable auto stop on timeout
 */
void disableAutoStopOnTimeout();

/**
 * @brief     check if auto stop on timeout is enabled
 *
 * @retval    true               auto stop is enabled
 * @retval    false              auto stop is disabled
 */
bool isAutoStopOnTimeoutEnabled() const;
```

**使用示例：**
```cpp
// 启用超时自动退出，连续超时3次后自动停止
udp_receiver->enableAutoStopOnTimeout(3);

// 启动接收
udp_receiver->start("0.0.0.0", 8080);

// 如果连续3次超时，接收器会自动停止
// 可以通过检查线程状态或超时状态来确认

// 禁用自动退出
udp_receiver->disableAutoStopOnTimeout();
```

## 使用场景

### 场景1：动态调整包长度的持续接收（推荐使用方式）

```cpp
auto udp_receiver = std::make_unique<MEMSUDP>(128);

// 注册接收回调 - 这个回调会持续被调用
udp_receiver->regRecvCallback([](const char* _data) {
    std::cout << "接收到数据包" << std::endl;
    // 处理接收到的数据
    processReceivedData(_data);
});

// 注册超时回调
udp_receiver->regTimeoutCallback([]() {
    std::cout << "接收超时，等待下一个数据包..." << std::endl;
});

// 启动UDP接收
udp_receiver->start("0.0.0.0", 8080);

// 运行时动态调整参数
// 第一阶段：接收128字节的包，超时1秒
udp_receiver->setPacketLength(128);
udp_receiver->setTimeoutMS(1000);
std::this_thread::sleep_for(std::chrono::seconds(5));

// 第二阶段：调整为512字节的包，超时2秒
udp_receiver->setPacketLength(512);
udp_receiver->setTimeoutMS(2000);
std::this_thread::sleep_for(std::chrono::seconds(5));

// 停止接收
udp_receiver->stop();
```

### 场景2：不同协议阶段的包长度切换

```cpp
auto udp_receiver = std::make_unique<MEMSUDP>(64);

udp_receiver->regRecvCallback([&](const char* _data) {
    // 根据接收到的数据判断下一阶段需要的包长度
    if (isHandshakePacket(_data)) {
        udp_receiver->setPacketLength(128);  // 切换到握手阶段
    } else if (isDataPacket(_data)) {
        udp_receiver->setPacketLength(1024); // 切换到数据传输阶段
    }
});

udp_receiver->start("0.0.0.0", 8080);
```

### 场景3：自适应超时机制

```cpp
auto udp_receiver = std::make_unique<MEMSUDP>(1024);

// 根据网络状况动态调整超时时间
if (network_is_slow) {
    udp_receiver->setTimeoutMS(10000);  // 慢网络，增加超时时间
} else {
    udp_receiver->setTimeoutMS(1000);   // 快网络，减少超时时间
}

udp_receiver->start("0.0.0.0", 8080);
```

### 场景4：超时自动退出的应用

```cpp
auto udp_receiver = std::make_unique<MEMSUDP>(1024, 5000);

// 启用超时自动退出，连续超时3次后自动停止
udp_receiver->enableAutoStopOnTimeout(3);

udp_receiver->regRecvCallback([](const char* _data) {
    std::cout << "处理接收到的数据" << std::endl;
    // 处理数据...
});

udp_receiver->regTimeoutCallback([]() {
    std::cout << "超时发生，等待下一个数据包..." << std::endl;
});

// 启动接收
udp_receiver->start("0.0.0.0", 8080);

// 程序会自动在连续3次超时后停止，无需手动干预
// 这对于需要自动恢复或故障检测的应用很有用
```

### 场景5：阻塞式特定接收（特殊用途）

```cpp
// 注意：这是阻塞调用，适用于特殊场景
std::vector<char> control_packet;
if (udp_receiver->receiveWithTimeout(control_packet, 64, 1000)) {
    // 处理控制包
    processControlPacket(control_packet);
}
```

## 重要说明

### 核心工作原理

修改后的 `MEMSUDP` 类通过以下方式支持动态参数调整：

1. **持续接收模式**：使用 `start()` 启动后，`dataProcess()` 方法会在独立线程中持续运行
2. **动态缓冲区调整**：每次接收循环都会检查 `pkt_length_` 是否发生变化，如有变化会自动调整接收缓冲区
3. **实时超时更新**：每次接收操作都使用当前的 `timeout_count_ms_` 值设置超时时间
4. **回调机制**：接收到符合长度要求的数据包时，会调用所有注册的回调函数

### 正确的使用方式

**✅ 推荐做法：**
```cpp
// 1. 创建实例
auto udp_receiver = std::make_unique<MEMSUDP>(initial_length);

// 2. 注册回调函数
udp_receiver->regRecvCallback([](const char* data) {
    // 处理接收到的数据
});

// 3. 启动持续接收
udp_receiver->start("0.0.0.0", port);

// 4. 运行时动态调整参数
udp_receiver->setPacketLength(new_length);
udp_receiver->setTimeoutMS(new_timeout);

// 5. 停止接收
udp_receiver->stop();
```

**❌ 错误理解：**
- 认为 `setPacketLength()` 只能接收一帧数据
- 认为需要重新调用 `start()` 才能应用新参数
- 认为 `receiveWithTimeout()` 是主要的接收方法

## 注意事项

1. **线程安全性**：`setPacketLength()` 和 `setTimeoutMS()` 方法在运行时调用是安全的，参数会在下一次接收循环中生效。

2. **动态调整时机**：参数调整会在下一次接收操作开始时生效，不会中断当前正在进行的接收操作。

3. **阻塞式接收**：`receiveWithTimeout()` 是阻塞调用，主要用于特殊场景，不是主要的接收方式。

4. **缓冲区管理**：系统会自动管理接收缓冲区的大小，无需手动处理。

5. **错误处理**：所有新增方法都包含完善的错误处理和日志记录，便于调试和监控。

6. **性能考虑**：动态调整参数的开销很小，但建议避免过于频繁的调整。

## 兼容性

所有新增功能都保持了与现有代码的完全兼容性：

- 原有的构造函数 `MEMSUDP(std::size_t _len)` 继续正常工作
- 原有的 `setTimeoutMS()` 方法保持不变
- 所有现有的回调机制和异步接收功能不受影响

## 编译要求

- C++17 或更高版本
- Boost.Asio 库
- 支持智能指针和现代C++特性的编译器

## 示例代码

完整的使用示例请参考 `mems_udp_example.cpp` 文件，其中包含了各种使用场景的详细演示。
