﻿cmake_minimum_required(VERSION 3.20)
project(common)

# add object library
add_library(common_obj OBJECT)

file(
  GLOB_RECURSE
  COMMON_CPP
  ${CMAKE_CURRENT_SOURCE_DIR}/ui/*.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/ui/*.h
  ${CMAKE_CURRENT_SOURCE_DIR}/property/*.h
  ${CMAKE_CURRENT_SOURCE_DIR}/property/*.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/node_models/*.h
  ${CMAKE_CURRENT_SOURCE_DIR}/node_models/*.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/visualization/*.h
  ${CMAKE_CURRENT_SOURCE_DIR}/visualization/*.cpp)

# 添加非示例的cpp文件
file(GLOB COMMON_MAIN_CPP ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp)
# 排除示例文件和测试文件
list(FILTER COMMON_MAIN_CPP EXCLUDE REGEX ".*_example\\.cpp$")
list(FILTER COMMON_MAIN_CPP EXCLUDE REGEX ".*_demo\\.cpp$")
list(FILTER COMMON_MAIN_CPP EXCLUDE REGEX ".*_test\\.cpp$")
list(APPEND COMMON_CPP ${COMMON_MAIN_CPP})

target_link_libraries(
  common_obj
  PUBLIC Qt5::Core
         Qt5::Widgets
         Qt5::Xml
         Qt5::Svg
         Boost::boost
         QtNodes::QtNodes
         BT::behaviortree_cpp
         QCodeEditor::QCodeEditor  # 暂时注释掉
         sol2::sol2
         TopInclude
         mems_communication
         Qt5::QJsonModel)
target_include_directories(
  common_obj PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}> $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}>
                    $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include> $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/lib>
                    $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include> src)

# 添加预处理器定义，指定 tsl 头文件的位置
target_compile_definitions(common_obj PUBLIC TSL_INCLUDE_DIR="${CMAKE_SOURCE_DIR}/include")
target_sources(common_obj PUBLIC ${COMMON_CPP})
set_target_properties(common_obj PROPERTIES POSITION_INDEPENDENT_CODE ON)

# add interface library
add_library(common INTERFACE)
add_library(common::common ALIAS common)
target_include_directories(
  common INTERFACE $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}> $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}>
                   $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>
                   $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include/3rd_party/q_json_model/include> src)

# 添加预处理器定义，指定 tsl 头文件的位置
target_compile_definitions(common INTERFACE TSL_INCLUDE_DIR="${CMAKE_SOURCE_DIR}/include")
target_link_libraries(
  common
  INTERFACE Qt5::Core
            Qt5::Widgets
            Qt5::Xml
            Qt5::Svg
            Boost::boost
            QtNodes::QtNodes
            BT::behaviortree_cpp
            QCodeEditor::QCodeEditor  # 暂时注释掉
            sol2::sol2
            TopInclude
            mems_communication
            Qt5::QJsonModel)
target_sources(common INTERFACE $<TARGET_OBJECTS:common_obj>)
set_target_properties(common PROPERTIES POSITION_INDEPENDENT_CODE ON)

# 设置 Windows 特定的编译选项和资源文件
if(CMAKE_SYSTEM_NAME MATCHES "Windows")
  set(WINRC ${CMAKE_SOURCE_DIR}/resource/exe_icon.rc)
  target_sources(common INTERFACE ${WINRC})
  target_compile_options(common INTERFACE /O2 /utf-8)
endif()

# 设置 Linux 特定的编译选项和链接库
if(CMAKE_SYSTEM_NAME MATCHES "Linux")
  target_link_libraries(common INTERFACE -lpthread)
  target_compile_options(common INTERFACE -fPIC -Wall -O3 -g)
endif()
