/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the RoboSense License
 * Agreement. Please see the LICENSE file for details.
 *****************************************************************************/

#ifndef VISUALIZATION_DISPLAY_WIDGET_H
#define VISUALIZATION_DISPLAY_WIDGET_H

#include "visualization_manager.h"
#include <QLabel>
#include <QScrollArea>
#include <QStackedWidget>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QWidget>

namespace robosense::lidar {

/**
 * @brief VisualizationDisplayWidget 类用于管理可视化组件的显示区域
 * 
 * 该类负责：
 * 1. 提供可视化组件的显示区域
 * 2. 管理多个可视化组件的标签页切换
 * 3. 当没有可视化时，显示默认提示信息
 * 4. 集成到主窗口的停靠窗口中
 */
class VisualizationDisplayWidget : public QWidget
{
  Q_OBJECT

public:
  explicit VisualizationDisplayWidget(QWidget* _parent = nullptr);
  ~VisualizationDisplayWidget() override;

  VisualizationDisplayWidget(const VisualizationDisplayWidget&)            = delete;
  VisualizationDisplayWidget& operator=(const VisualizationDisplayWidget&) = delete;
  VisualizationDisplayWidget(VisualizationDisplayWidget&&)                 = delete;
  VisualizationDisplayWidget& operator=(VisualizationDisplayWidget&&)      = delete;

  // 获取可视化显示区域
  [[nodiscard]] QWidget* getVisualizationArea() const;

public Q_SLOTS:
  /**
   * @brief 当可视化显示时调用
   * @param _node_id 节点ID
   * @param _widget_type 组件类型
   */
  void onVisualizationShown(const QString& _node_id, const QString& _widget_type);

  /**
   * @brief 当可视化隐藏时调用
   * @param _node_id 节点ID
   */
  void onVisualizationHidden(const QString& _node_id);

  /**
   * @brief 当可视化更新时调用
   * @param _node_id 节点ID
   */
  void onVisualizationUpdated(const QString& _node_id);

  /**
   * @brief 清除所有可视化，显示默认状态
   */
  void clearAllVisualizations();

  /**
   * @brief 显示默认页面
   */
  void showDefaultPage();

Q_SIGNALS:
  /**
   * @brief 当可视化标签页被选择时发出信号
   * @param _node_id 节点ID
   */
  void visualizationTabSelected(const QString& _node_id);

private Q_SLOTS:
  void onTabChanged(int _index);
  void onTabCloseRequested(int _index);

private:
  void setupUi();
  void setupDefaultPage();

  QVBoxLayout* main_layout_;
  QStackedWidget* stacked_widget_;
  QTabWidget* tab_widget_;
  QScrollArea* visualization_area_;
  QWidget* default_page_;
  QLabel* default_label_;

  // 标签页管理
  QMap<QString, int> node_tab_map_;  // node_id -> tab_index
  QMap<int, QString> tab_node_map_;  // tab_index -> node_id
};

}  // namespace robosense::lidar

#endif  // VISUALIZATION_DISPLAY_WIDGET_H
