#include "numeric_gauge_widget.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QGridLayout>
#include <QGroupBox>
#include <QHBoxLayout>

namespace robosense::lidar {

// NumericGaugeWidget 实现
NumericGaugeWidget::NumericGaugeWidget()
  : main_widget_(nullptr)
  , main_layout_(nullptr)
  , title_label_(nullptr)
  , title_("数值监控")
  , min_value_(0.0)
  , max_value_(100.0)
  , value_format_("%.2f")
  , show_labels_(true)
{
  setupWidget();
}

QWidget* NumericGaugeWidget::getWidget()
{
  return main_widget_;
}

void NumericGaugeWidget::updateData(const IVisualizationData& _data)
{
  if (_data.getDataType() != "numeric")
  {
    LOG_ERROR("[NumericGaugeWidget] 不支持的数据类型: {}", _data.getDataType().toStdString());
    return;
  }

  QVariant data_variant = _data.getData();
  if (!data_variant.canConvert<QVariantMap>())
  {
    LOG_ERROR("[NumericGaugeWidget] 数据格式错误，期望QVariantMap");
    return;
  }

  QVariantMap numeric_data = data_variant.toMap();

  // 更新每个数值的仪表盘
  for (auto it = numeric_data.begin(); it != numeric_data.end(); ++it)
  {
    const QString& name = it.key();
    double value = it.value().toDouble();
    updateGaugeValue(name, value, min_value_, max_value_);
  }

  LOG_DEBUG("[NumericGaugeWidget] 更新数据: {} 个数值", numeric_data.size());
}

void NumericGaugeWidget::configure(const QVariantMap& _config)
{
  if (_config.contains("title"))
  {
    title_ = _config["title"].toString();
    title_label_->setText(title_);
  }

  if (_config.contains("min_value"))
  {
    min_value_ = _config["min_value"].toDouble();
  }

  if (_config.contains("max_value"))
  {
    max_value_ = _config["max_value"].toDouble();
  }

  if (_config.contains("value_format"))
  {
    value_format_ = _config["value_format"].toString();
  }

  if (_config.contains("show_labels"))
  {
    show_labels_ = _config["show_labels"].toBool();
  }

  // 更新所有现有仪表盘的范围
  for (auto it = gauge_bars_.begin(); it != gauge_bars_.end(); ++it)
  {
    QProgressBar* gauge = it.value();
    gauge->setRange(static_cast<int>(min_value_), static_cast<int>(max_value_));
  }

  LOG_DEBUG("[NumericGaugeWidget] 配置更新: title={}, range=[{}, {}]", 
            title_.toStdString(), min_value_, max_value_);
}

QString NumericGaugeWidget::getWidgetType() const
{
  return "NumericGauge";
}

QStringList NumericGaugeWidget::getSupportedDataTypes() const
{
  return {"numeric", "voltage", "current", "temperature", "pressure"};
}

void NumericGaugeWidget::setupWidget()
{
  main_widget_ = new QWidget();
  main_layout_ = new QVBoxLayout(main_widget_);

  // 标题
  title_label_ = new QLabel(title_, main_widget_);
  title_label_->setAlignment(Qt::AlignCenter);
  title_label_->setStyleSheet("QLabel { font-weight: bold; font-size: 16px; margin: 10px; }");
  main_layout_->addWidget(title_label_);

  // 仪表盘容器
  QGroupBox* gauge_group = new QGroupBox("数值仪表盘", main_widget_);
  QGridLayout* gauge_layout = new QGridLayout(gauge_group);
  main_layout_->addWidget(gauge_group);

  main_layout_->addStretch();
}

void NumericGaugeWidget::updateGaugeValue(const QString& _name, double _value, double _min_value, double _max_value)
{
  // 如果仪表盘不存在，创建新的
  if (!gauge_bars_.contains(_name))
  {
    // 创建仪表盘
    QProgressBar* gauge = new QProgressBar(main_widget_);
    gauge->setRange(static_cast<int>(_min_value), static_cast<int>(_max_value));
    gauge->setTextVisible(true);
    gauge->setOrientation(Qt::Horizontal);
    gauge->setStyleSheet(
      "QProgressBar {"
      "  border: 2px solid grey;"
      "  border-radius: 5px;"
      "  text-align: center;"
      "  font-weight: bold;"
      "}"
      "QProgressBar::chunk {"
      "  background-color: #05B8CC;"
      "  border-radius: 3px;"
      "}");

    // 创建标签
    QLabel* name_label = new QLabel(_name, main_widget_);
    name_label->setAlignment(Qt::AlignCenter);
    name_label->setStyleSheet("QLabel { font-weight: bold; }");

    QLabel* value_label = new QLabel(main_widget_);
    value_label->setAlignment(Qt::AlignCenter);
    value_label->setStyleSheet("QLabel { font-size: 14px; color: #333; }");

    // 添加到布局
    QGroupBox* gauge_group = qobject_cast<QGroupBox*>(main_layout_->itemAt(1)->widget());
    QGridLayout* gauge_layout = qobject_cast<QGridLayout*>(gauge_group->layout());

    int row_count = gauge_layout->rowCount();
    gauge_layout->addWidget(name_label, row_count, 0);
    gauge_layout->addWidget(gauge, row_count, 1);
    gauge_layout->addWidget(value_label, row_count, 2);

    // 保存引用
    gauge_bars_[_name] = gauge;
    value_labels_[_name] = value_label;

    LOG_DEBUG("[NumericGaugeWidget] 创建新仪表盘: {}", _name.toStdString());
  }

  // 更新值
  QProgressBar* gauge = gauge_bars_[_name];
  QLabel* value_label = value_labels_[_name];

  gauge->setValue(static_cast<int>(_value));
  
  QString value_text = QString(value_format_).arg(_value);
  value_label->setText(value_text);

  // 根据值的范围设置颜色
  double percentage = (_value - _min_value) / (_max_value - _min_value);
  QString color;
  if (percentage < 0.3)
  {
    color = "#4CAF50";  // 绿色
  }
  else if (percentage < 0.7)
  {
    color = "#FF9800";  // 橙色
  }
  else
  {
    color = "#F44336";  // 红色
  }

  gauge->setStyleSheet(
    QString("QProgressBar {"
            "  border: 2px solid grey;"
            "  border-radius: 5px;"
            "  text-align: center;"
            "  font-weight: bold;"
            "}"
            "QProgressBar::chunk {"
            "  background-color: %1;"
            "  border-radius: 3px;"
            "}").arg(color));
}

// NumericTableWidget 实现
NumericTableWidget::NumericTableWidget()
  : main_widget_(nullptr)
  , main_layout_(nullptr)
  , title_label_(nullptr)
  , title_("数值表格")
  , value_format_("%.2f")
  , decimal_places_(2)
{
  setupWidget();
}

QWidget* NumericTableWidget::getWidget()
{
  return main_widget_;
}

void NumericTableWidget::updateData(const IVisualizationData& _data)
{
  if (_data.getDataType() != "numeric")
  {
    LOG_ERROR("[NumericTableWidget] 不支持的数据类型: {}", _data.getDataType().toStdString());
    return;
  }

  QVariant data_variant = _data.getData();
  if (!data_variant.canConvert<QVariantMap>())
  {
    LOG_ERROR("[NumericTableWidget] 数据格式错误，期望QVariantMap");
    return;
  }

  QVariantMap numeric_data = data_variant.toMap();
  updateTableData(numeric_data);

  LOG_DEBUG("[NumericTableWidget] 更新数据: {} 个数值", numeric_data.size());
}

void NumericTableWidget::configure(const QVariantMap& _config)
{
  if (_config.contains("title"))
  {
    title_ = _config["title"].toString();
    title_label_->setText(title_);
  }

  if (_config.contains("value_format"))
  {
    value_format_ = _config["value_format"].toString();
  }

  if (_config.contains("decimal_places"))
  {
    decimal_places_ = _config["decimal_places"].toInt();
  }

  LOG_DEBUG("[NumericTableWidget] 配置更新: title={}, format={}", 
            title_.toStdString(), value_format_.toStdString());
}

QString NumericTableWidget::getWidgetType() const
{
  return "NumericTable";
}

QStringList NumericTableWidget::getSupportedDataTypes() const
{
  return {"numeric", "voltage", "current", "temperature", "pressure"};
}

void NumericTableWidget::setupWidget()
{
  main_widget_ = new QWidget();
  main_layout_ = new QVBoxLayout(main_widget_);

  // 标题
  title_label_ = new QLabel(title_, main_widget_);
  title_label_->setAlignment(Qt::AlignCenter);
  title_label_->setStyleSheet("QLabel { font-weight: bold; font-size: 16px; margin: 10px; }");
  main_layout_->addWidget(title_label_);

  // 表格容器
  QGroupBox* table_group = new QGroupBox("数值列表", main_widget_);
  QGridLayout* table_layout = new QGridLayout(table_group);
  
  // 添加表头
  QLabel* name_header = new QLabel("名称", table_group);
  QLabel* value_header = new QLabel("数值", table_group);
  name_header->setStyleSheet("QLabel { font-weight: bold; background-color: #f0f0f0; padding: 5px; }");
  value_header->setStyleSheet("QLabel { font-weight: bold; background-color: #f0f0f0; padding: 5px; }");
  
  table_layout->addWidget(name_header, 0, 0);
  table_layout->addWidget(value_header, 0, 1);
  
  main_layout_->addWidget(table_group);
  main_layout_->addStretch();
}

void NumericTableWidget::updateTableData(const QVariantMap& _values)
{
  QGroupBox* table_group = qobject_cast<QGroupBox*>(main_layout_->itemAt(1)->widget());
  QGridLayout* table_layout = qobject_cast<QGridLayout*>(table_group->layout());

  // 清除现有数据行（保留表头）
  for (auto it = value_labels_.begin(); it != value_labels_.end(); ++it)
  {
    QLabel* label = it.value();
    table_layout->removeWidget(label);
    label->deleteLater();
  }
  value_labels_.clear();

  // 添加新数据
  int row = 1;  // 从第1行开始（第0行是表头）
  for (auto it = _values.begin(); it != _values.end(); ++it, ++row)
  {
    const QString& name = it.key();
    double value = it.value().toDouble();

    // 创建名称标签
    QLabel* name_label = new QLabel(name, table_group);
    name_label->setStyleSheet("QLabel { padding: 5px; border-bottom: 1px solid #ddd; }");

    // 创建数值标签
    QLabel* value_label = new QLabel(table_group);
    QString value_text = QString::number(value, 'f', decimal_places_);
    value_label->setText(value_text);
    value_label->setStyleSheet("QLabel { padding: 5px; border-bottom: 1px solid #ddd; font-family: monospace; }");

    // 添加到布局
    table_layout->addWidget(name_label, row, 0);
    table_layout->addWidget(value_label, row, 1);

    // 保存引用
    value_labels_[name] = value_label;
  }
}

}  // namespace robosense::lidar
