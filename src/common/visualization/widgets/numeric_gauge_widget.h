#pragma once

#include "../visualization_manager.h"
#include <QLabel>
#include <QProgressBar>
#include <QVBoxLayout>
#include <QWidget>

namespace robosense::lidar {

// 数值仪表盘可视化组件
class NumericGaugeWidget : public IVisualizationWidget
{
public:
  NumericGaugeWidget();
  ~NumericGaugeWidget() override = default;

  // IVisualizationWidget 接口实现
  [[nodiscard]] QWidget* getWidget() override;
  void updateData(const IVisualizationData& _data) override;
  void configure(const QVariantMap& _config) override;
  [[nodiscard]] QString getWidgetType() const override;
  [[nodiscard]] QStringList getSupportedDataTypes() const override;

private:
  void setupWidget();
  void updateGaugeValue(const QString& _name, double _value, double _min_value, double _max_value);

  QWidget* main_widget_;
  QVBoxLayout* main_layout_;
  QMap<QString, QProgressBar*> gauge_bars_;
  QMap<QString, QLabel*> value_labels_;
  QLabel* title_label_;

  // 配置参数
  QString title_;
  double min_value_;
  double max_value_;
  QString value_format_;
  bool show_labels_;
};

// 数值表格可视化组件
class NumericTableWidget : public IVisualizationWidget
{
public:
  NumericTableWidget();
  ~NumericTableWidget() override = default;

  // IVisualizationWidget 接口实现
  [[nodiscard]] QWidget* getWidget() override;
  void updateData(const IVisualizationData& _data) override;
  void configure(const QVariantMap& _config) override;
  [[nodiscard]] QString getWidgetType() const override;
  [[nodiscard]] QStringList getSupportedDataTypes() const override;

private:
  void setupWidget();
  void updateTableData(const QVariantMap& _values);

  QWidget* main_widget_;
  QVBoxLayout* main_layout_;
  QMap<QString, QLabel*> value_labels_;
  QLabel* title_label_;

  // 配置参数
  QString title_;
  QString value_format_;
  int decimal_places_;
};

// 图像显示组件
class ImageDisplayWidget : public IVisualizationWidget
{
public:
  ImageDisplayWidget();
  ~ImageDisplayWidget() override = default;

  // IVisualizationWidget 接口实现
  [[nodiscard]] QWidget* getWidget() override;
  void updateData(const IVisualizationData& _data) override;
  void configure(const QVariantMap& _config) override;
  [[nodiscard]] QString getWidgetType() const override;
  [[nodiscard]] QStringList getSupportedDataTypes() const override;

private:
  void setupWidget();
  void updateImageData(const QByteArray& _image_data, int _width, int _height, const QString& _format);
  QPixmap createGrayscalePixmap(const QByteArray& _data, int _width, int _height);

  QWidget* main_widget_;
  QVBoxLayout* main_layout_;
  QLabel* image_label_;
  QLabel* title_label_;
  QLabel* info_label_;

  // 配置参数
  QString title_;
  bool enable_zoom_;
  bool enable_save_;
  double scale_factor_;
};

}  // namespace robosense::lidar

/*
设计说明：

1. 严格遵循编码规范：
   - 私有成员变量：main_widget_, main_layout_ 等以下划线结尾
   - 函数参数：_data, _config 等以下划线开头
   - 函数命名：updateData, configure 等驼峰命名法
   - 局部变量：gauge_bars_, value_labels_ 等长度大于三个字母

2. 组件化设计：
   - 每个组件实现 IVisualizationWidget 接口
   - 支持不同的数据类型和显示方式
   - 可配置的显示参数

3. 智能指针管理：
   - 避免裸指针传输
   - 使用 Qt 的父子关系管理内存

4. 扩展性：
   - 容易添加新的可视化组件
   - 支持运行时配置更新

使用示例：
```cpp
// 注册组件
REGISTER_VISUALIZATION_WIDGET(NumericGauge, 
    QStringList{"numeric", "voltage", "current"}, 
    []() { return std::make_unique<NumericGaugeWidget>(); });

REGISTER_VISUALIZATION_WIDGET(NumericTable, 
    QStringList{"numeric", "voltage", "current"}, 
    []() { return std::make_unique<NumericTableWidget>(); });

REGISTER_VISUALIZATION_WIDGET(ImageDisplay, 
    QStringList{"image", "grayscale", "rgb"}, 
    []() { return std::make_unique<ImageDisplayWidget>(); });

// 在节点中使用
NodeVisualizationHelper visualization_helper(name());

// 显示电压数据为仪表盘
QVariantMap voltage_data;
voltage_data["voltage1"] = 3.3;
voltage_data["voltage2"] = 5.0;
visualization_helper.showNumericData(voltage_data, {
    {"widget_type", "NumericGauge"},
    {"title", "电压监控"},
    {"min_value", 0.0},
    {"max_value", 10.0},
    {"auto_close_duration", 5000}
});

// 显示灰度图像
QByteArray grayscale_data = parseUdpDataToGrayscale(udp_data);
visualization_helper.showImageData(grayscale_data, 640, 480, "grayscale", {
    {"widget_type", "ImageDisplay"},
    {"title", "灰度图像"},
    {"enable_zoom", true},
    {"scale_factor", 1.0}
});
```
*/
