#include "visualization_display_widget.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QApplication>
#include <QHBoxLayout>
#include <QIcon>

namespace robosense::lidar {

VisualizationDisplayWidget::VisualizationDisplayWidget(QWidget* _parent)
  : QWidget(_parent)
  , main_layout_(nullptr)
  , stacked_widget_(nullptr)
  , tab_widget_(nullptr)
  , visualization_area_(nullptr)
  , default_page_(nullptr)
  , default_label_(nullptr)
{
  setupUi();
  setupDefaultPage();

  // 连接可视化管理器的信号
  connect(&UnifiedVisualizationManager::instance(), &UnifiedVisualizationManager::visualizationShown,
          this, &VisualizationDisplayWidget::onVisualizationShown);

  connect(&UnifiedVisualizationManager::instance(), &UnifiedVisualizationManager::visualizationHidden,
          this, &VisualizationDisplayWidget::onVisualizationHidden);

  connect(&UnifiedVisualizationManager::instance(), &UnifiedVisualizationManager::visualizationUpdated,
          this, &VisualizationDisplayWidget::onVisualizationUpdated);

  // 设置可视化管理器的显示区域
  UnifiedVisualizationManager::instance().setDisplayArea(visualization_area_);

  LOG_INFO("[VisualizationDisplayWidget] 可视化显示组件初始化完成");
}

VisualizationDisplayWidget::~VisualizationDisplayWidget() = default;

QWidget* VisualizationDisplayWidget::getVisualizationArea() const
{
  return visualization_area_;
}

void VisualizationDisplayWidget::onVisualizationShown(const QString& _node_id, const QString& _widget_type)
{
  // 如果已经存在该节点的标签页，切换到该标签页
  if (node_tab_map_.contains(_node_id))
  {
    int tab_index = node_tab_map_[_node_id];
    tab_widget_->setCurrentIndex(tab_index);
    LOG_DEBUG("[VisualizationDisplayWidget] 切换到现有标签页: {} ({})", _node_id.toStdString(), tab_index);
  }
  else
  {
    // 创建新的标签页
    QString tab_title = QString("%1 (%2)").arg(_node_id, _widget_type);
    QWidget* tab_content = new QWidget();
    tab_content->setLayout(new QVBoxLayout());

    int tab_index = tab_widget_->addTab(tab_content, tab_title);
    tab_widget_->setCurrentIndex(tab_index);

    // 更新映射
    node_tab_map_[_node_id] = tab_index;
    tab_node_map_[tab_index] = _node_id;

    LOG_INFO("[VisualizationDisplayWidget] 创建新标签页: {} -> {} ({})", 
             _node_id.toStdString(), tab_title.toStdString(), tab_index);
  }

  // 切换到标签页视图
  stacked_widget_->setCurrentWidget(tab_widget_);
}

void VisualizationDisplayWidget::onVisualizationHidden(const QString& _node_id)
{
  if (!node_tab_map_.contains(_node_id))
  {
    return;
  }

  int tab_index = node_tab_map_[_node_id];

  // 移除标签页
  QWidget* tab_widget = tab_widget_->widget(tab_index);
  tab_widget_->removeTab(tab_index);
  if (tab_widget)
  {
    tab_widget->deleteLater();
  }

  // 更新映射
  node_tab_map_.remove(_node_id);
  tab_node_map_.remove(tab_index);

  // 更新其他标签页的索引映射
  QMap<QString, int> new_node_tab_map;
  QMap<int, QString> new_tab_node_map;

  for (int i = 0; i < tab_widget_->count(); ++i)
  {
    QString tab_text = tab_widget_->tabText(i);
    // 从标签文本中提取节点ID（格式：node_id (widget_type)）
    QString node_id = tab_text.split(" (").first();
    new_node_tab_map[node_id] = i;
    new_tab_node_map[i] = node_id;
  }

  node_tab_map_ = new_node_tab_map;
  tab_node_map_ = new_tab_node_map;

  // 如果没有标签页了，显示默认页面
  if (tab_widget_->count() == 0)
  {
    showDefaultPage();
  }

  LOG_INFO("[VisualizationDisplayWidget] 移除标签页: {} ({})", _node_id.toStdString(), tab_index);
}

void VisualizationDisplayWidget::onVisualizationUpdated(const QString& _node_id)
{
  LOG_DEBUG("[VisualizationDisplayWidget] 可视化更新: {}", _node_id.toStdString());
  // 可视化更新时不需要特殊处理，组件会自动更新
}

void VisualizationDisplayWidget::clearAllVisualizations()
{
  // 清除所有标签页
  while (tab_widget_->count() > 0)
  {
    QWidget* tab_widget = tab_widget_->widget(0);
    tab_widget_->removeTab(0);
    if (tab_widget)
    {
      tab_widget->deleteLater();
    }
  }

  // 清除映射
  node_tab_map_.clear();
  tab_node_map_.clear();

  // 显示默认页面
  showDefaultPage();

  LOG_INFO("[VisualizationDisplayWidget] 清除所有可视化");
}

void VisualizationDisplayWidget::showDefaultPage()
{
  stacked_widget_->setCurrentWidget(default_page_);
  LOG_DEBUG("[VisualizationDisplayWidget] 显示默认页面");
}

void VisualizationDisplayWidget::onTabChanged(int _index)
{
  if (tab_node_map_.contains(_index))
  {
    QString node_id = tab_node_map_[_index];
    emit visualizationTabSelected(node_id);
    LOG_DEBUG("[VisualizationDisplayWidget] 标签页切换: {} -> {}", _index, node_id.toStdString());
  }
}

void VisualizationDisplayWidget::onTabCloseRequested(int _index)
{
  if (tab_node_map_.contains(_index))
  {
    QString node_id = tab_node_map_[_index];
    UnifiedVisualizationManager::instance().hideVisualization(node_id);
    LOG_INFO("[VisualizationDisplayWidget] 用户关闭标签页: {} -> {}", _index, node_id.toStdString());
  }
}

void VisualizationDisplayWidget::setupUi()
{
  main_layout_ = new QVBoxLayout(this);
  main_layout_->setContentsMargins(0, 0, 0, 0);

  // 创建堆叠窗口
  stacked_widget_ = new QStackedWidget(this);

  // 创建标签页组件
  tab_widget_ = new QTabWidget(this);
  tab_widget_->setTabsClosable(true);
  tab_widget_->setMovable(true);

  // 连接标签页信号
  connect(tab_widget_, &QTabWidget::currentChanged, this, &VisualizationDisplayWidget::onTabChanged);
  connect(tab_widget_, &QTabWidget::tabCloseRequested, this, &VisualizationDisplayWidget::onTabCloseRequested);

  // 创建可视化显示区域
  visualization_area_ = new QScrollArea(this);
  visualization_area_->setWidgetResizable(true);
  visualization_area_->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
  visualization_area_->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

  // 将组件添加到堆叠窗口
  stacked_widget_->addWidget(tab_widget_);

  main_layout_->addWidget(stacked_widget_);
}

void VisualizationDisplayWidget::setupDefaultPage()
{
  default_page_ = new QWidget(this);
  QVBoxLayout* default_layout = new QVBoxLayout(default_page_);

  default_label_ = new QLabel("暂无可视化数据\n\n"
                             "当节点执行可视化操作时，\n"
                             "相关内容将在此处显示。", default_page_);
  default_label_->setAlignment(Qt::AlignCenter);
  default_label_->setStyleSheet("QLabel { color: #888888; font-size: 14px; }");

  default_layout->addStretch();
  default_layout->addWidget(default_label_);
  default_layout->addStretch();

  stacked_widget_->addWidget(default_page_);

  // 默认显示默认页面
  showDefaultPage();
}

}  // namespace robosense::lidar
