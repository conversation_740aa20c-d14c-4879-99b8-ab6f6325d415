#include "rsfsc_log/rsfsc_log_macro.h"
#include "visualization_manager.h"
#include "widgets/numeric_gauge_widget.h"
#include <iostream>

namespace robosense::lidar
{

// 初始化基础可视化组件
void initializeBasicVisualizationWidgets()
{
  std::cout << "🚀 [VisualizationInitializer] 开始注册基础可视化组件" << std::endl;
  LOG_INFO("[VisualizationInitializer] 开始注册基础可视化组件");

  // 注册数值仪表盘组件
  UnifiedVisualizationManager::instance().registerVisualizationWidget(
    "NumericGauge", QStringList { "numeric", "voltage", "current", "temperature", "sensor_data" },
    []() { return std::make_unique<NumericGaugeWidget>(); });

  LOG_INFO("[VisualizationInitializer] 注册NumericGauge组件，支持数据类型: numeric, voltage, current, temperature, "
           "sensor_data");

  // 注册数值表格组件（使用NumericGauge作为替代）
  UnifiedVisualizationManager::instance().registerVisualizationWidget(
    "NumericTable", QStringList { "numeric", "voltage", "current", "temperature", "sensor_data" },
    []() { return std::make_unique<NumericGaugeWidget>(); });

  LOG_INFO("[VisualizationInitializer] 注册NumericTable组件（使用NumericGauge实现），支持数据类型: numeric, voltage, "
           "current, temperature, sensor_data");

  LOG_INFO("[VisualizationInitializer] 基础可视化组件注册完成");
}

}  // namespace robosense::lidar
