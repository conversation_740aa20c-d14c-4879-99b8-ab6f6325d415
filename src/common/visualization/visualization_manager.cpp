#include "visualization_manager.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QApplication>
#include <QThread>
#include <QVBoxLayout>

namespace robosense::lidar
{

// UnifiedVisualizationManager 实现
UnifiedVisualizationManager& UnifiedVisualizationManager::instance()
{
  static UnifiedVisualizationManager instance_;
  return instance_;
}

void UnifiedVisualizationManager::registerVisualizationWidget(const QString& _widget_type,
                                                              const QStringList& _supported_data_types,
                                                              VisualizationWidgetFactory _factory)
{
  widget_factories_[_widget_type][_supported_data_types] = std::move(_factory);
  LOG_INFO("[VisualizationManager] 注册可视化组件: {} 支持数据类型: {}", _widget_type.toStdString(),
           _supported_data_types.join(", ").toStdString());
}

void UnifiedVisualizationManager::showVisualization(std::shared_ptr<IVisualizationData> _data,
                                                    const QVariantMap& _display_config)
{
  if (!_data)
  {
    LOG_ERROR("[VisualizationManager] 可视化数据为空");
    return;
  }

  const QString node_id   = _data->getNodeId();
  const QString data_type = _data->getDataType();

  // 确保在主线程中执行
  if (QThread::currentThread() != QApplication::instance()->thread())
  {
    QTimer::singleShot(0, [this, _data, _display_config]() { showVisualization(_data, _display_config); });
    return;
  }

  // 如果已存在，先隐藏
  if (active_visualizations_.find(node_id) != active_visualizations_.end())
  {
    hideVisualization(node_id);
  }

  // 选择合适的组件类型
  QString widget_type = selectBestWidget(data_type, _display_config);
  if (widget_type.isEmpty())
  {
    LOG_ERROR("[VisualizationManager] 未找到支持数据类型 {} 的可视化组件", data_type.toStdString());
    return;
  }

  // 创建可视化组件
  auto widget_factory = widget_factories_[widget_type].begin();
  if (widget_factory == widget_factories_[widget_type].end())
  {
    LOG_ERROR("[VisualizationManager] 未找到组件工厂: {}", widget_type.toStdString());
    return;
  }

  auto visualization_widget = widget_factory.value()();
  if (!visualization_widget)
  {
    LOG_ERROR("[VisualizationManager] 创建可视化组件失败: {}", widget_type.toStdString());
    return;
  }

  // 配置组件
  visualization_widget->configure(_display_config);
  visualization_widget->updateData(*_data);

  // 创建定时器（如果需要自动关闭）
  QTimer* auto_close_timer = nullptr;
  if (_display_config.contains("auto_close_duration"))
  {
    int duration = _display_config["auto_close_duration"].toInt();
    if (duration > 0)
    {
      auto_close_timer = new QTimer(this);
      auto_close_timer->setSingleShot(true);
      connect(auto_close_timer, &QTimer::timeout, [this, node_id]() { onVisualizationTimeout(node_id); });
      auto_close_timer->start(duration);
    }
  }

  // 添加到显示区域
  if (display_area_)
  {
    QWidget* widget_ptr = visualization_widget->getWidget();
    if (widget_ptr)
    {
      display_area_->layout()->addWidget(widget_ptr);
      widget_ptr->show();
    }
  }

  // 保存信息
  auto visualization_info               = std::make_unique<VisualizationInfo>();
  visualization_info->widget_           = std::move(visualization_widget);
  visualization_info->data_             = _data;
  visualization_info->auto_close_timer_ = auto_close_timer;
  visualization_info->config_           = _display_config;
  visualization_info->widget_type_      = widget_type;

  active_visualizations_[node_id] = std::move(visualization_info);

  LOG_INFO("[VisualizationManager] 显示可视化: {} 类型: {}", node_id.toStdString(), widget_type.toStdString());
  emit visualizationShown(node_id, widget_type);
}

void UnifiedVisualizationManager::updateVisualization(const QString& _node_id,
                                                      std::shared_ptr<IVisualizationData> _data)
{
  if (active_visualizations_.find(_node_id) == active_visualizations_.end())
  {
    LOG_DEBUG("[VisualizationManager] 节点 {} 的可视化不存在，创建新的", _node_id.toStdString());
    showVisualization(_data);
    return;
  }

  // 确保在主线程中执行
  if (QThread::currentThread() != QApplication::instance()->thread())
  {
    QTimer::singleShot(0, [this, _node_id, _data]() { updateVisualization(_node_id, _data); });
    return;
  }

  auto& visualization_info = active_visualizations_[_node_id];
  visualization_info->widget_->updateData(*_data);
  visualization_info->data_ = _data;

  // 重新设置自动关闭定时器
  if (visualization_info->auto_close_timer_)
  {
    int duration = visualization_info->config_["auto_close_duration"].toInt();
    if (duration > 0)
    {
      visualization_info->auto_close_timer_->start(duration);
    }
  }

  LOG_DEBUG("[VisualizationManager] 更新可视化: {}", _node_id.toStdString());
  emit visualizationUpdated(_node_id);
}

void UnifiedVisualizationManager::hideVisualization(const QString& _node_id)
{
  if (active_visualizations_.find(_node_id) == active_visualizations_.end())
  {
    return;
  }

  // 确保在主线程中执行
  if (QThread::currentThread() != QApplication::instance()->thread())
  {
    QTimer::singleShot(0, [this, _node_id]() { hideVisualization(_node_id); });
    return;
  }

  auto& visualization_info = active_visualizations_[_node_id];

  // 清理定时器
  if (visualization_info->auto_close_timer_)
  {
    visualization_info->auto_close_timer_->stop();
    visualization_info->auto_close_timer_->deleteLater();
  }

  // 从显示区域移除
  if (visualization_info->widget_)
  {
    QWidget* widget_ptr = visualization_info->widget_->getWidget();
    if (widget_ptr && display_area_)
    {
      display_area_->layout()->removeWidget(widget_ptr);
      widget_ptr->hide();
      widget_ptr->setParent(nullptr);
    }
  }

  active_visualizations_.erase(_node_id);

  LOG_INFO("[VisualizationManager] 隐藏可视化: {}", _node_id.toStdString());
  emit visualizationHidden(_node_id);
}

QStringList UnifiedVisualizationManager::getActiveVisualizations() const
{
  QStringList result;
  for (const auto& pair : active_visualizations_)
  {
    result.append(pair.first);
  }
  return result;
}

void UnifiedVisualizationManager::setDisplayArea(QWidget* _display_area)
{
  display_area_ = _display_area;
  if (display_area_ && !display_area_->layout())
  {
    display_area_->setLayout(new QVBoxLayout());
  }
}

void UnifiedVisualizationManager::onVisualizationTimeout(const QString& _node_id)
{
  LOG_INFO("[VisualizationManager] 可视化超时自动关闭: {}", _node_id.toStdString());
  hideVisualization(_node_id);
}

QString UnifiedVisualizationManager::selectBestWidget(const QString& _data_type, const QVariantMap& _config)
{
  // 如果配置中指定了组件类型，优先使用
  if (_config.contains("widget_type"))
  {
    QString preferred_type = _config["widget_type"].toString();
    if (widget_factories_.contains(preferred_type))
    {
      return preferred_type;
    }
  }

  // 查找支持该数据类型的组件
  for (auto it = widget_factories_.begin(); it != widget_factories_.end(); ++it)
  {
    const QString& widget_type = it.key();
    for (auto factory_it = it.value().begin(); factory_it != it.value().end(); ++factory_it)
    {
      const QStringList& supported_types = factory_it.key();
      if (supported_types.contains(_data_type))
      {
        return widget_type;
      }
    }
  }

  return QString();
}

// NumericVisualizationData 实现
NumericVisualizationData::NumericVisualizationData(const QString& _node_id, const QVariantMap& _values) :
  node_id_(_node_id), values_(_values)
{}

QString NumericVisualizationData::getDataType() const { return "numeric"; }

QVariant NumericVisualizationData::getData() const { return values_; }

QVariantMap NumericVisualizationData::getMetadata() const { return metadata_; }

QString NumericVisualizationData::getNodeId() const { return node_id_; }

void NumericVisualizationData::setMetadata(const QVariantMap& _metadata) { metadata_ = _metadata; }

// ImageVisualizationData 实现
ImageVisualizationData::ImageVisualizationData(const QString& _node_id,
                                               const QByteArray& _image_data,
                                               int _width,
                                               int _height,
                                               const QString& _format) :
  node_id_(_node_id), image_data_(_image_data), width_(_width), height_(_height), format_(_format)
{}

QString ImageVisualizationData::getDataType() const { return "image"; }

QVariant ImageVisualizationData::getData() const
{
  QVariantMap image_data;
  image_data["image_data"] = image_data_;
  image_data["width"]      = width_;
  image_data["height"]     = height_;
  image_data["format"]     = format_;
  return image_data;
}

QVariantMap ImageVisualizationData::getMetadata() const { return metadata_; }

QString ImageVisualizationData::getNodeId() const { return node_id_; }

void ImageVisualizationData::setMetadata(const QVariantMap& _metadata) { metadata_ = _metadata; }

// NodeVisualizationHelper 实现
NodeVisualizationHelper::NodeVisualizationHelper(const QString& _node_id) : node_id_(_node_id) {}

void NodeVisualizationHelper::showNumericData(const QVariantMap& _values, const QVariantMap& _config)
{
  auto numeric_data = std::make_shared<NumericVisualizationData>(node_id_, _values);
  UnifiedVisualizationManager::instance().showVisualization(numeric_data, _config);
}

void NodeVisualizationHelper::showImageData(const QByteArray& _image_data,
                                            int _width,
                                            int _height,
                                            const QString& _format,
                                            const QVariantMap& _config)
{
  auto image_data = std::make_shared<ImageVisualizationData>(node_id_, _image_data, _width, _height, _format);
  UnifiedVisualizationManager::instance().showVisualization(image_data, _config);
}

void NodeVisualizationHelper::hideVisualization()
{
  UnifiedVisualizationManager::instance().hideVisualization(node_id_);
}

}  // namespace robosense::lidar
