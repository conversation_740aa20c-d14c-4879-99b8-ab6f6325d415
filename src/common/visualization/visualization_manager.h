#pragma once

#include <QMap>
#include <QObject>
#include <QString>
#include <QTimer>
#include <QVariant>
#include <QWidget>
#include <functional>
#include <memory>
#include <unordered_map>

namespace robosense::lidar
{

// 可视化数据接口
class IVisualizationData
{
public:
  virtual ~IVisualizationData() = default;

  [[nodiscard]] virtual QString getDataType() const     = 0;
  [[nodiscard]] virtual QVariant getData() const        = 0;
  [[nodiscard]] virtual QVariantMap getMetadata() const = 0;
  [[nodiscard]] virtual QString getNodeId() const       = 0;
};

// 可视化组件接口
class IVisualizationWidget
{
public:
  virtual ~IVisualizationWidget() = default;

  [[nodiscard]] virtual QWidget* getWidget()                      = 0;
  virtual void updateData(const IVisualizationData& _data)        = 0;
  virtual void configure(const QVariantMap& _config)              = 0;
  [[nodiscard]] virtual QString getWidgetType() const             = 0;
  [[nodiscard]] virtual QStringList getSupportedDataTypes() const = 0;
};

// 可视化组件工厂函数类型
using VisualizationWidgetFactory = std::function<std::unique_ptr<IVisualizationWidget>()>;

// 统一可视化管理器
class UnifiedVisualizationManager : public QWidget
{
  Q_OBJECT

public:
  static UnifiedVisualizationManager& instance();

  // 注册可视化组件工厂
  void registerVisualizationWidget(const QString& _widget_type,
                                   const QStringList& _supported_data_types,
                                   VisualizationWidgetFactory _factory);

  // 显示可视化
  void showVisualization(std::shared_ptr<IVisualizationData> _data, const QVariantMap& _display_config = {});

  // 更新可视化
  void updateVisualization(const QString& _node_id, std::shared_ptr<IVisualizationData> _data);

  // 隐藏可视化
  void hideVisualization(const QString& _node_id);

  // 获取当前活跃的可视化
  [[nodiscard]] QStringList getActiveVisualizations() const;

  // 设置显示区域
  void setDisplayArea(QWidget* _display_area);

Q_SIGNALS:
  void visualizationShown(const QString& _node_id, const QString& _widget_type);
  void visualizationHidden(const QString& _node_id);
  void visualizationUpdated(const QString& _node_id);

private Q_SLOTS:
  void onVisualizationTimeout(const QString& _node_id);

private:
  struct VisualizationInfo
  {
    std::unique_ptr<IVisualizationWidget> widget_;
    std::shared_ptr<IVisualizationData> data_;
    QTimer* auto_close_timer_;
    QVariantMap config_;
    QString widget_type_;
  };

  // 选择最合适的可视化组件
  [[nodiscard]] QString selectBestWidget(const QString& _data_type, const QVariantMap& _config);

  QMap<QString, QMap<QStringList, VisualizationWidgetFactory>>
    widget_factories_;  // widget_type -> supported_types -> factory
  std::unordered_map<QString, std::unique_ptr<VisualizationInfo>> active_visualizations_;  // node_id -> info
  QWidget* display_area_;
};

// 数值可视化数据
class NumericVisualizationData : public IVisualizationData
{
public:
  NumericVisualizationData(const QString& _node_id, const QVariantMap& _values);

  [[nodiscard]] QString getDataType() const override;
  [[nodiscard]] QVariant getData() const override;
  [[nodiscard]] QVariantMap getMetadata() const override;
  [[nodiscard]] QString getNodeId() const override;

  void setMetadata(const QVariantMap& _metadata);

private:
  QString node_id_;
  QVariantMap values_;
  QVariantMap metadata_;
};

// 图像可视化数据
class ImageVisualizationData : public IVisualizationData
{
public:
  ImageVisualizationData(const QString& _node_id,
                         const QByteArray& _image_data,
                         int _width,
                         int _height,
                         const QString& _format = "grayscale");

  [[nodiscard]] QString getDataType() const override;
  [[nodiscard]] QVariant getData() const override;
  [[nodiscard]] QVariantMap getMetadata() const override;
  [[nodiscard]] QString getNodeId() const override;

  void setMetadata(const QVariantMap& _metadata);

private:
  QString node_id_;
  QByteArray image_data_;
  int width_;
  int height_;
  QString format_;
  QVariantMap metadata_;
};

// 节点可视化助手
class NodeVisualizationHelper
{
public:
  explicit NodeVisualizationHelper(const QString& _node_id);

  // 显示数值数据
  void showNumericData(const QVariantMap& _values, const QVariantMap& _config = {});

  // 显示图像数据
  void showImageData(const QByteArray& _image_data,
                     int _width,
                     int _height,
                     const QString& _format     = "grayscale",
                     const QVariantMap& _config = {});

  // 隐藏可视化
  void hideVisualization();

private:
  QString node_id_;
};

}  // namespace robosense::lidar

// 可视化组件注册宏
#define REGISTER_VISUALIZATION_WIDGET(_widget_type, _data_types, _factory_func)                                       \
  static bool registered_##_widget_type = []() {                                                                      \
    robosense::lidar::UnifiedVisualizationManager::instance().registerVisualizationWidget(#_widget_type, _data_types, \
                                                                                          _factory_func);             \
    return true;                                                                                                      \
  }();

/*
编码规范遵循：

1. 私有成员变量：
   - node_id_, values_, metadata_ 等都以下划线结尾
   - 全部小写，单词间用下划线分隔

2. 函数命名：
   - showVisualization, updateVisualization 等采用驼峰命名法
   - 首字母小写

3. 函数参数：
   - _node_id, _values, _config 等都以下划线开头
   - 全部小写，单词间用下划线分隔

4. 局部变量：
   - widget_factories_, active_visualizations_ 等长度大于三个字母

5. 现代C++标准：
   - 使用 std::unique_ptr, std::shared_ptr 智能指针
   - 使用 [[nodiscard]] 属性
   - 避免裸指针传输

使用示例：
```cpp
// 注册可视化组件
REGISTER_VISUALIZATION_WIDGET(GaugeWidget, 
    QStringList{"numeric", "voltage", "current"}, 
    []() { return std::make_unique<GaugeVisualizationWidget>(); });

// 在节点中使用
NodeVisualizationHelper visualization_helper(name());

QVariantMap voltage_data;
voltage_data["voltage1"] = 3.3;
voltage_data["voltage2"] = 5.0;

visualization_helper.showNumericData(voltage_data, {
    {"widget_type", "gauge"},
    {"title", "电压监控"},
    {"auto_close_duration", 5000}
});
```
*/
