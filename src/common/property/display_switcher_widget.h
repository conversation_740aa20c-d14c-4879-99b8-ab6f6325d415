/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the RoboSense License
 * Agreement. Please see the LICENSE file for details.
 *****************************************************************************/

#ifndef DISPLAY_SWITCHER_WIDGET_H
#define DISPLAY_SWITCHER_WIDGET_H

#include "property_view.h"
#include <QLabel>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <QWidget>
#include <QtNodes/NodeDelegateModel>
#include <unordered_map>

namespace robosense::lidar
{

class BtNodeModel;

/**
 * @brief DisplaySwitcherWidget 类用于管理节点属性的侧边栏显示
 * 
 * 该类负责：
 * 1. 管理多个节点的 PropertyView 显示切换
 * 2. 当节点被选择时，显示对应的属性面板
 * 3. 当节点被删除时，清理对应的属性面板
 * 4. 当没有节点选择时，显示默认提示信息
 */
class DisplaySwitcherWidget : public QWidget
{
  Q_OBJECT

public:
  explicit DisplaySwitcherWidget(QWidget* _parent = nullptr);
  ~DisplaySwitcherWidget() override;

  DisplaySwitcherWidget(const DisplaySwitcherWidget&)            = delete;
  DisplaySwitcherWidget& operator=(const DisplaySwitcherWidget&) = delete;
  DisplaySwitcherWidget(DisplaySwitcherWidget&&)                 = delete;
  DisplaySwitcherWidget& operator=(DisplaySwitcherWidget&&)      = delete;

public Q_SLOTS:
  /**
   * @brief 当节点被选择时调用，显示对应的属性面板
   * @param _node_id 节点ID
   * @param _node_model 节点模型指针
   */
  void onNodeSelected(std::size_t _node_id, BtNodeModel* _node_model);

  /**
   * @brief 当节点被创建时调用，为新节点准备属性面板
   * @param _node_id 节点ID
   * @param _node_model 节点模型指针
   */
  void onNodeCreated(std::size_t _node_id, BtNodeModel* _node_model);

  /**
   * @brief 当节点被删除时调用，清理对应的属性面板
   * @param _node_id 节点ID
   */
  void onNodeDeleted(std::size_t _node_id);

  /**
   * @brief 清除所有节点的属性面板，显示默认状态
   */
  void clearAllNodes();

  /**
   * @brief 刷新当前显示的属性面板
   */
  void refreshCurrentView();

Q_SIGNALS:
  /**
   * @brief 当属性值发生变化时发出信号
   * @param _node_id 节点ID
   * @param _group 属性组名
   * @param _key 属性键名
   * @param _value 新的属性值
   */
  void propertyValueChanged(std::size_t _node_id,
                            const std::string& _group,
                            const std::string& _key,
                            const QVariant& _value);

private Q_SLOTS:
  /**
   * @brief 处理属性值变化的内部槽函数
   * @param _group 属性组名
   * @param _key 属性键名
   * @param _value 新的属性值
   */
  void onPropertyValueChanged(const std::string& _group, const std::string& _key, const QVariant& _value);

private:
  /**
   * @brief 初始化UI组件
   */
  void setupUi();

  /**
   * @brief 创建默认的提示页面
   * @return 默认页面的widget指针
   */
  QWidget* createDefaultPage();

  /**
   * @brief 为指定节点创建属性视图
   * @param _node_id 节点ID
   * @param _node_model 节点模型指针
   * @return 创建的PropertyView指针
   */
  PropertyView* createPropertyViewForNode(std::size_t _node_id, BtNodeModel* _node_model);

  /**
   * @brief 显示指定节点的属性面板
   * @param _node_id 节点ID
   */
  void showNodeProperties(std::size_t _node_id);

  /**
   * @brief 显示默认页面
   */
  void showDefaultPage();

private:
  // UI组件
  QVBoxLayout* main_layout_;        ///< 主布局
  QStackedWidget* stacked_widget_;  ///< 堆叠widget，用于切换显示
  QWidget* default_page_;           ///< 默认页面
  QLabel* default_label_;           ///< 默认提示标签

  // 数据管理
  std::unordered_map<std::size_t, PropertyView*> property_views_;  ///< 节点ID到PropertyView的映射
  std::unordered_map<std::size_t, BtNodeModel*> node_models_;      ///< 节点ID到BtNodeModel的映射
  std::size_t current_node_id_;                                    ///< 当前显示的节点ID
  bool has_current_node_;                                          ///< 是否有当前选择的节点

  // 常量
  static constexpr std::size_t INVALID_NODE_ID = std::numeric_limits<std::size_t>::max();
};

}  // namespace robosense::lidar

#endif  // DISPLAY_SWITCHER_WIDGET_H
