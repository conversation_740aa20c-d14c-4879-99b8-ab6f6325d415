﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef PROPERTY_MODEL_H
#define PROPERTY_MODEL_H

#include "3rd_party/tsl/ordered_map.h"
#include <QFileDialog>
#include <QFormLayout>
#include <QHBoxLayout>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QLabel>
#include <QLineEdit>
#include <QReadWriteLock>
#include <QToolButton>
#include <QVariant>
#include <QtCore/QObject>
#include <memory>
#include <string>
#include <unordered_map>

class QTreeWidgetItem;
class QTreeWidget;

namespace robosense::lidar
{

// 属性项基类
class PropertyItem : public QObject
{
  Q_OBJECT
public:
  explicit PropertyItem(const std::string& _name, QTreeWidgetItem* _parent = nullptr) :
    name_(_name), parent_(_parent), widget_(nullptr)
  {}
  ~PropertyItem() override = default;

  virtual QWidget* createEditor()                    = 0;
  virtual void setValue(const QVariant& _value)      = 0;
  virtual void forceSetValue(const QVariant& _value) = 0;  // 强制设置值，忽略焦点状态
  [[nodiscard]] virtual QVariant value() const       = 0;

  [[nodiscard]] const std::string& _name() const noexcept { return name_; }
  [[nodiscard]] QTreeWidgetItem* parent() const noexcept { return parent_; }
  void setParent(QTreeWidgetItem* _parent) noexcept { parent_ = _parent; }
  [[nodiscard]] QWidget* widget() const noexcept { return widget_; }

  // 连接UI控件的信号
  virtual void itemConnect() = 0;

Q_SIGNALS:
  // 当UI控件值变化时发出信号
  void signalValueChanged(const QVariant& _value);

protected:
  std::string name_;
  QTreeWidgetItem* parent_;
  QWidget* widget_;
};

// 整数属性项
class IntItem : public PropertyItem
{
  Q_OBJECT
public:
  IntItem(const std::string& _name,
          int _defaultValue        = 0,
          int _min                 = std::numeric_limits<int>::min(),
          int _max                 = std::numeric_limits<int>::max(),
          int _step                = 1,
          QTreeWidgetItem* _parent = nullptr) :
    PropertyItem(_name, _parent), default_value_(_defaultValue), min_(_min), max_(_max), step_(_step)
  {}

  QWidget* createEditor() override;

  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;

  [[nodiscard]] QVariant value() const override;

  void itemConnect() override;

public:
  int default_value_;

public:
  int min_;
  int max_;
  int step_;
};

// 浮点数属性项
class DoubleItem : public PropertyItem
{
  Q_OBJECT
public:
  DoubleItem(const std::string& _name,
             double _defaultValue     = 0.0,
             double _min              = -std::numeric_limits<double>::max(),
             double _max              = std::numeric_limits<double>::max(),
             double _step             = 0.1,
             int _decimals            = 2,
             QTreeWidgetItem* _parent = nullptr) :
    PropertyItem(_name, _parent),
    default_value_(_defaultValue),
    min_(_min),
    max_(_max),
    step_(_step),
    decimals_(_decimals)
  {}

  QWidget* createEditor() override;

  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;

  [[nodiscard]] QVariant value() const override;

  void itemConnect() override;

public:
  double default_value_;
  double min_;
  double max_;
  double step_;
  int decimals_;
};

// 字符串属性项
class StringItem : public PropertyItem
{
  Q_OBJECT
public:
  explicit StringItem(const std::string& _name,
                      const std::string& _defaultValue = "",
                      int _height_factor               = 1,
                      QTreeWidgetItem* _parent         = nullptr) :
    PropertyItem(_name, _parent), default_value_(_defaultValue), height_factor_(_height_factor)
  {}

  QWidget* createEditor() override;

  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;

  [[nodiscard]] QVariant value() const override;

  void itemConnect() override;

public:
  std::string default_value_;
  int height_factor_;  // 高度倍数因子，默认为1，大于1时自动启用多行模式
};

// 布尔属性项
class BoolItem : public PropertyItem
{
  Q_OBJECT
public:
  explicit BoolItem(const std::string& _name, bool _defaultValue = false, QTreeWidgetItem* _parent = nullptr) :
    PropertyItem(_name, _parent), default_value_(_defaultValue)
  {}

  QWidget* createEditor() override;

  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;

  [[nodiscard]] QVariant value() const override;

  void itemConnect() override;

public:
  bool default_value_;
};

// 枚举属性项
class EnumItem : public PropertyItem
{
  Q_OBJECT
public:
  EnumItem(const std::string& _name,
           const QStringList& _options,
           int _default_index       = 0,
           QTreeWidgetItem* _parent = nullptr) :
    PropertyItem(_name, _parent), options_(_options), default_index_(_default_index)
  {}

  QWidget* createEditor() override;

  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;

  [[nodiscard]] QVariant value() const override;

  [[nodiscard]] QString currentText() const;

  void itemConnect() override;

public:
  QStringList options_;
  int default_index_;
};

// 文件路径属性项
class FilePathPropertyItem : public PropertyItem
{
  Q_OBJECT
public:
  FilePathPropertyItem(const std::string& _name,
                       const std::string& _default_value = "",
                       const std::string& _filter        = "所有文件 (*)",
                       QTreeWidgetItem* _parent          = nullptr);

  QWidget* createEditor() override;
  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;
  [[nodiscard]] QVariant value() const override;
  void itemConnect() override;

public:
  QString default_value_;
  QString filter_;
};

// 文件夹路径属性项
class DirectoryPathPropertyItem : public PropertyItem
{
  Q_OBJECT
public:
  explicit DirectoryPathPropertyItem(const std::string& _name,
                                     const std::string& _default_value = "",
                                     QTreeWidgetItem* _parent          = nullptr);

  QWidget* createEditor() override;
  void setValue(const QVariant& _value) override;
  void forceSetValue(const QVariant& _value) override;
  [[nodiscard]] QVariant value() const override;
  void itemConnect() override;

public:
  QString default_value_;
};

// 属性组，用于创建层次结构
class PropertyGroup
{
public:
  explicit PropertyGroup(const std::string& _name) : name_(_name) {}
  ~PropertyGroup() = default;

  // Copy constructor
  PropertyGroup(const PropertyGroup& _other) = default;

  // Copy assignment operator
  PropertyGroup& operator=(const PropertyGroup& _other)
  {
    if (this != &_other)
    {
      name_ = _other.name_;
    }
    return *this;
  }

  // Move constructor
  PropertyGroup(PropertyGroup&& _other) noexcept : name_(std::move(_other.name_)) {}

  // Move assignment operator
  PropertyGroup& operator=(PropertyGroup&& _other) noexcept
  {
    if (this != &_other)
    {
      name_ = std::move(_other.name_);
    }
    return *this;
  }

  [[nodiscard]] const std::string& _name() const noexcept { return name_; }
  void setName(const std::string& _name) noexcept { name_ = _name; }

private:
  std::string name_;
};

// 属性模型，管理所有属性项
class PropertyModel : public QObject
{
  Q_OBJECT
public:
  using Ptr                 = std::shared_ptr<PropertyModel>;
  using Groups              = tsl::ordered_map<std::string, std::shared_ptr<PropertyGroup>>;
  using Properties          = tsl::ordered_map<std::string, std::shared_ptr<PropertyItem>>;
  PropertyModel()           = default;
  ~PropertyModel() override = default;

  PropertyModel(const PropertyModel&)            = delete;
  PropertyModel& operator=(const PropertyModel&) = delete;
  PropertyModel(PropertyModel&&)                 = delete;
  PropertyModel& operator=(PropertyModel&&)      = delete;

  template <typename T, typename... Args>
  void registerProperty(const std::string& _group, const std::string& _key, Args&&... _args)
  {
    QWriteLocker locker(&lock_);
    if (groups_.find(_group) == groups_.end())
    {
      groups_.emplace(_group, std::make_shared<PropertyGroup>(_group));
    }
    std::string full_key  = (_group + "." + _key);
    auto property_item    = std::make_shared<T>(_key, std::forward<Args>(_args)...);
    properties_[full_key] = property_item;

    // 连接属性项的valueChanged信号到模型的处理函数
    QObject::connect(
      property_item.get(), &PropertyItem::signalValueChanged, this,
      [this, _group, _key](const QVariant& _value) { this->handleUIValueChanged(_group, _key, _value); });

    Q_EMIT signalModelChanged();
  }

  bool unregisterProperty(const std::string& _group, const std::string& _key);

  bool unregisterGroup(const std::string& _group);

  template <typename T>
  T getValue(const std::string& _group, const std::string& _key) const
  {
    std::string full_key = (_group + "." + _key);

    // 优先从存储的值中读取
    auto stored_iter = stored_values_.find(full_key);
    if (stored_iter != stored_values_.end())
    {
      return stored_iter->second.template value<T>();
    }

    // 如果没有存储的值，从UI控件读取
    auto iter = properties_.find(full_key);
    if (iter != properties_.end())
    {
      return iter->second->value().template value<T>();
    }
    return T {};
  }

  template <typename T>
  void setValue(const std::string& _group, const std::string& _key, const T& _value)
  {
    std::string full_key = (_group + "." + _key);
    auto iter            = properties_.find(full_key);
    if (iter != properties_.end())
    {
      // 同时更新存储的值和UI控件
      stored_values_[full_key] = QVariant::fromValue(_value);
      iter->second->setValue(QVariant::fromValue(_value));
      Q_EMIT signalValueChanged(_group, _key, QVariant::fromValue(_value));
    }
  }

  // 强制设置值，忽略焦点状态（用于恢复操作）
  template <typename T>
  void forceSetValue(const std::string& _group, const std::string& _key, const T& _value)
  {
    std::string full_key = (_group + "." + _key);
    auto iter            = properties_.find(full_key);
    if (iter != properties_.end())
    {
      // 先存储值到内部状态
      stored_values_[full_key] = QVariant::fromValue(_value);
      // 然后设置到UI控件
      iter->second->forceSetValue(QVariant::fromValue(_value));
      Q_EMIT signalValueChanged(_group, _key, QVariant::fromValue(_value));
    }
  }

  [[nodiscard]] const Groups& groups() const;
  [[nodiscard]] const Properties& properties() const;

  // 获取属性描述
  [[nodiscard]] std::string getPropertyDescription(const std::string& _group, const std::string& _key) const;

  // 设置属性描述
  void setPropertyDescription(const std::string& _group, const std::string& _key, const std::string& _description);

  // 处理UI控件值变化
  void handleUIValueChanged(const std::string& _group, const std::string& _key, const QVariant& _value);

Q_SIGNALS:
  void signalValueChanged(const std::string& _group, const std::string& _key, const QVariant& _value);
  void signalModelChanged();

private:
  Groups groups_;
  Properties properties_;
  tsl::ordered_map<std::string, std::string> property_descriptions_;  // 存储属性描述
  tsl::ordered_map<std::string, QVariant> stored_values_;             // 存储属性值，用于恢复操作
  mutable QReadWriteLock lock_;
};

}  // namespace robosense::lidar

#endif  // PROPERTY_MODEL_H
