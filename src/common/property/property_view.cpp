﻿
/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "property_view.h"
#include <QApplication>
#include <QCheckBox>
#include <QComboBox>
#include <QDialogButtonBox>
#include <QDoubleSpinBox>
#include <QFormLayout>
#include <QHeaderView>
#include <QLineEdit>
#include <QMessageBox>
#include <QPushButton>
#include <QSettings>
#include <QStackedWidget>
#include <QTreeWidget>
#include <QVBoxLayout>
#include <QtCore/QTextCodec>
#include <boost/algorithm/string.hpp>
#include <climits>
#include <qchar.h>
#include <qobject.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

PropertyView::PropertyView(QWidget* _parent, bool _hide_single_group, const QString& _name) :
  QWidget(_parent), widget_tree_(new QTreeWidget(this)), hide_single_group_(_hide_single_group)
{
  setWindowTitle(_name);
  setupUi();
  setupButtons();
}

PropertyView::~PropertyView() = default;

void PropertyView::setupUi()
{
  // 设置树形控件的基本属性
  widget_tree_->setColumnCount(2);
  widget_tree_->header()->setSectionResizeMode(QHeaderView::ResizeToContents);  //自适应列宽
  widget_tree_->header()->setDefaultAlignment(Qt::AlignCenter);                 //表头居中
  widget_tree_->setAnimated(true);  // 用来控制树形控件中节点展开/收起时是否启用动画效果
  widget_tree_->setEditTriggers(QAbstractItemView::AllEditTriggers);  // 启用所有编辑方式
  widget_tree_->setHeaderLabels({ "名称", "参数值" });                //表头名称
  widget_tree_->setStyleSheet(("QTreeWidget::item {border:1px solid #E8E8E8}"
                               "QTreeWidget::item::selected {background-color:#4682B4}"));

  // 调整列宽
  widget_tree_->header()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
  widget_tree_->header()->setSectionResizeMode(1, QHeaderView::Stretch);
}

void PropertyView::setupButtons()
{
  auto* main_layout = new QVBoxLayout(this);

  // 添加树形控件到布局中
  main_layout->addWidget(widget_tree_);

  // 创建按钮布局
  auto* button_layout = new QHBoxLayout();

  // 添加属性按钮
  add_button_ = new QPushButton("添加属性");
  connect(add_button_, &QPushButton::clicked, this, &PropertyView::slotAddPropertyClicked);
  button_layout->addWidget(add_button_);

  // 删除属性按钮
  delete_button_ = new QPushButton("删除属性");
  connect(delete_button_, &QPushButton::clicked, this, &PropertyView::slotDeletePropertyClicked);
  button_layout->addWidget(delete_button_);

  // 保存按钮
  save_button_ = new QPushButton("保存");
  connect(save_button_, &QPushButton::clicked, this, &PropertyView::slotSaveClicked);
  button_layout->addWidget(save_button_);

  // 加载按钮
  load_button_ = new QPushButton("加载");
  connect(load_button_, &QPushButton::clicked, this, &PropertyView::slotLoadClicked);
  button_layout->addWidget(load_button_);

  // 应用按钮
  apply_button_ = new QPushButton("应用");
  connect(apply_button_, &QPushButton::clicked, this, &PropertyView::slotApplyClicked);
  button_layout->addWidget(apply_button_);

  // 将按钮布局添加到主布局
  main_layout->addLayout(button_layout);

  this->setLayout(main_layout);
}

QTreeWidgetItem* PropertyView::findOrCreateGroupItem(const std::string& _group)
{
  auto iter = group_items_.find(_group);
  if (iter != group_items_.end())
  {
    return iter->second;
  }

  // 创建新的组项
  auto* group_item = new QTreeWidgetItem(widget_tree_);
  group_item->setText(0, QString::fromStdString(_group));
  group_item->setExpanded(true);
  group_item->setFlags(group_item->flags() & ~Qt::ItemIsEditable);

  // 存储组项
  group_items_[_group] = group_item;
  return group_item;
}

void PropertyView::registerInt(const std::string& _group,
                               const std::string& _key,
                               int _default_value,
                               int _min,
                               int _max,
                               int _step)
{
  auto func = [this, &_group, &_key, _default_value, _min, _max, _step]() {
    model_->registerProperty<IntItem>(_group, _key, _default_value, _min, _max, _step);
  };
  registerAndCreateUi(_group, _key, func);
}
void PropertyView::registerDouble(const std::string& _group,
                                  const std::string& _key,
                                  double _default_value,
                                  double _min,
                                  double _max,
                                  double _step,
                                  int _decimals)
{
  auto func = [this, &_group, &_key, _default_value, _min, _max, _step, _decimals]() {
    model_->registerProperty<DoubleItem>(_group, _key, _default_value, _min, _max, _step, _decimals);
  };
  registerAndCreateUi(_group, _key, func);
}
void PropertyView::registerString(const std::string& _group, const std::string& _key, const std::string& _default_value)
{
  auto func = [this, &_group, &_key, &_default_value]() {
    model_->registerProperty<StringItem>(_group, _key, _default_value);
  };
  registerAndCreateUi(_group, _key, func);
}

void PropertyView::registerString(const std::string& _group,
                                  const std::string& _key,
                                  const std::string& _default_value,
                                  int _height_factor)
{
  auto func = [this, &_group, &_key, &_default_value, _height_factor]() {
    model_->registerProperty<StringItem>(_group, _key, _default_value, _height_factor);
  };
  registerAndCreateUi(_group, _key, func);
}
void PropertyView::registerBool(const std::string& _group, const std::string& _key, bool _default_value)
{
  auto func = [this, &_group, &_key, _default_value]() {
    model_->registerProperty<BoolItem>(_group, _key, _default_value);
  };
  registerAndCreateUi(_group, _key, func);
}
void PropertyView::registerEnum(const std::string& _group,
                                const std::string& _key,
                                const QStringList& _options,
                                int _default_index)
{
  auto func = [this, &_group, &_key, &_options, _default_index]() {
    model_->registerProperty<EnumItem>(_group, _key, _options, _default_index);
  };
  registerAndCreateUi(_group, _key, func);
}

void PropertyView::registerFilePath(const std::string& _group,
                                    const std::string& _key,
                                    const std::string& _default_value,
                                    const std::string& _filter)
{
  auto func = [this, &_group, &_key, &_default_value, &_filter]() {
    model_->registerProperty<FilePathPropertyItem>(_group, _key, _default_value, _filter);
  };
  registerAndCreateUi(_group, _key, func);
}

void PropertyView::registerDirectoryPath(const std::string& _group,
                                         const std::string& _key,
                                         const std::string& _default_value)
{
  auto func = [this, &_group, &_key, &_default_value]() {
    model_->registerProperty<DirectoryPathPropertyItem>(_group, _key, _default_value);
  };
  registerAndCreateUi(_group, _key, func);
}
template <typename RegisterFunc, typename... Args>
void PropertyView::registerAndCreateUi(const std::string& _group,
                                       const std::string& _key,
                                       RegisterFunc& _func,
                                       Args&&... _args)
{
  _func(std::forward<Args>(_args)...);
  auto* group_item    = findOrCreateGroupItem(_group);
  auto* property_item = new QTreeWidgetItem(group_item);
  property_item->setText(0, QString::fromStdString(_key));
  property_item->setFlags((property_item->flags() | Qt::ItemIsEnabled | Qt::ItemIsSelectable) & ~Qt::ItemIsEditable);
  std::string full_key = _group + "." + _key;

  // 获取并设置属性的描述作为工具提示
  std::string description = model_->getPropertyDescription(_group, _key);
  if (!description.empty())
  {
    property_item->setToolTip(0, QString::fromStdString(description));
    property_item->setToolTip(1, QString::fromStdString(description));
  }

  auto* editor = model_->properties().at(full_key)->createEditor();
  widget_tree_->setItemWidget(property_item, 1, editor);
}

void PropertyView::disconnectModel()
{
  if (model_ == nullptr)
  {
    return;
  }
  QObject::disconnect(model_.get(), &PropertyModel::signalValueChanged, this, &PropertyView::slotValueChanged);
  QObject::disconnect(model_.get(), &PropertyModel::signalModelChanged, this, &PropertyView::slotModelChanged);
}

void PropertyView::connectModel()
{
  if (model_ == nullptr)
  {
    return;
  }
  QObject::connect(model_.get(), &PropertyModel::signalValueChanged, this, &PropertyView::slotValueChanged);
  QObject::connect(model_.get(), &PropertyModel::signalModelChanged, this, &PropertyView::slotModelChanged);
}

void PropertyView::refreshUi()
{
  widget_tree_->clear();
  group_items_.clear();

  // 检查是否只有一个组且需要隐藏
  bool should_hide_group = hide_single_group_ && (model_->groups().size() == 1);

  for (const auto& [group_name, group] : model_->groups())
  {
    QTreeWidgetItem* parent_item = nullptr;
    if (should_hide_group)
    {
      // 如果隐藏组，直接使用根节点作为父节点
      parent_item = widget_tree_->invisibleRootItem();
    }
    else
    {
      // 否则创建组节点
      parent_item = findOrCreateGroupItem(group_name);
    }

    for (const auto& [full_key, property] : model_->properties())
    {
      QString full_key_str = QString::fromStdString(full_key);
      QStringList parts    = full_key_str.split('.');

      if (parts.size() == 2 && parts[0].toStdString() == group_name)
      {
        const QString& key             = parts[1];
        QTreeWidgetItem* property_item = nullptr;

        if (should_hide_group)
        {
          // 如果隐藏组，直接添加到根节点
          property_item = new QTreeWidgetItem(widget_tree_);
        }
        else
        {
          property_item = new QTreeWidgetItem(parent_item);
        }

        property_item->setText(0, key);
        property_item->setFlags((property_item->flags() | Qt::ItemIsEnabled | Qt::ItemIsSelectable) &
                                ~Qt::ItemIsEditable);

        // 获取并设置属性的描述作为工具提示
        std::string description = model_->getPropertyDescription(group_name, key.toStdString());
        if (!description.empty())
        {
          property_item->setToolTip(0, QString::fromStdString(description));
          property_item->setToolTip(1, QString::fromStdString(description));
        }

        auto* editor = property->createEditor();
        widget_tree_->setItemWidget(property_item, 1, editor);

        // 重要：从PropertyModel读取存储的值并设置到新创建的控件中
        QVariant current_value = model_->getValue<QVariant>(group_name, key.toStdString());
        property->forceSetValue(current_value);

        // 验证设置后的值
        QVariant after_value = property->value();
      }
    }
  }
}

bool PropertyView::unregisterProperty(const std::string& _group, const std::string& _key)
{
  // 从模型中注销属性
  bool result = model_->unregisterProperty(_group, _key);
  if (result)
  {
    // 刷新UI以反映变化
    refreshUi();
  }
  return result;
}

bool PropertyView::unregisterGroup(const std::string& _group)
{
  // 从模型中注销整个组
  bool result = model_->unregisterGroup(_group);
  if (result)
  {
    // 刷新UI以反映变化
    refreshUi();
  }
  return result;
}

// 设置只读组列表
void PropertyView::setReadOnlyGroups(const QStringList& _groups) { read_only_groups_ = _groups; }

// 添加只读组
void PropertyView::addReadOnlyGroup(const QString& _group)
{
  if (!read_only_groups_.contains(_group))
  {
    read_only_groups_.append(_group);
  }
}

// 移除只读组
void PropertyView::removeReadOnlyGroup(const QString& _group) { read_only_groups_.removeAll(_group); }

// 检查组是否只读
bool PropertyView::isGroupReadOnly(const QString& _group) const { return read_only_groups_.contains(_group); }

void PropertyView::slotAddPropertyClicked()
{
  // 创建添加属性对话框
  QDialog dialog(this);
  dialog.setWindowTitle("添加属性");
  dialog.setMinimumWidth(300);

  // 创建表单布局
  auto* layout = new QFormLayout(&dialog);

  // 组名输入
  auto* group_edit = new QLineEdit();
  layout->addRow("组名:", group_edit);

  // 属性名输入
  auto* key_edit = new QLineEdit();
  layout->addRow("属性名:", key_edit);

  // 属性类型选择
  auto* type_combo = new QComboBox();
  type_combo->addItems({ "整数", "浮点数", "字符串", "布尔值", "枚举", "文件路径", "文件夹路径" });
  layout->addRow("类型:", type_combo);

  // 创建一个堆叠部件，根据选择的类型显示不同的输入控件
  auto* stacked_widget = new QStackedWidget();

  // 整数属性的输入控件
  auto* int_widget       = new QWidget();
  auto* int_layout       = new QFormLayout(int_widget);
  auto* default_int_edit = new QSpinBox();
  auto* min_int_edit     = new QSpinBox();
  auto* max_int_edit     = new QSpinBox();
  auto* step_int_edit    = new QSpinBox();
  default_int_edit->setRange(INT_MIN, INT_MAX);
  min_int_edit->setRange(INT_MIN, INT_MAX);
  max_int_edit->setRange(INT_MIN, INT_MAX);
  step_int_edit->setRange(1, 1000);
  step_int_edit->setValue(1);
  int_layout->addRow("默认值:", default_int_edit);
  int_layout->addRow("最小值:", min_int_edit);
  int_layout->addRow("最大值:", max_int_edit);
  int_layout->addRow("步长:", step_int_edit);
  stacked_widget->addWidget(int_widget);

  // 浮点数属性的输入控件
  auto* double_widget       = new QWidget();
  auto* double_layout       = new QFormLayout(double_widget);
  auto* default_double_edit = new QDoubleSpinBox();
  auto* min_double_edit     = new QDoubleSpinBox();
  auto* max_double_edit     = new QDoubleSpinBox();
  auto* step_double_edit    = new QDoubleSpinBox();
  auto* decimals_edit       = new QSpinBox();
  default_double_edit->setRange(-1000000, 1000000);
  min_double_edit->setRange(-1000000, 1000000);
  max_double_edit->setRange(-1000000, 1000000);
  step_double_edit->setRange(0.01, 100);
  step_double_edit->setValue(0.1);
  decimals_edit->setRange(0, 10);
  decimals_edit->setValue(2);
  double_layout->addRow("默认值:", default_double_edit);
  double_layout->addRow("最小值:", min_double_edit);
  double_layout->addRow("最大值:", max_double_edit);
  double_layout->addRow("步长:", step_double_edit);
  double_layout->addRow("小数位数:", decimals_edit);
  stacked_widget->addWidget(double_widget);

  // 字符串属性的输入控件
  auto* string_widget       = new QWidget();
  auto* string_layout       = new QFormLayout(string_widget);
  auto* default_string_edit = new QLineEdit();
  auto* height_factor_edit  = new QSpinBox();
  auto* height_info_label   = new QLabel("注意：高度倍数大于1时将自动启用多行模式，支持滚动条和换行");
  height_factor_edit->setRange(1, 10);  // 设置高度倍数范围为1-10
  height_factor_edit->setValue(1);      // 默认为1倍高度
  height_factor_edit->setToolTip("设置控件高度为标准高度的倍数，大于1时自动启用多行模式");
  height_info_label->setWordWrap(true);
  string_layout->addRow("默认值:", default_string_edit);
  string_layout->addRow("高度倍数:", height_factor_edit);
  string_layout->addRow(height_info_label);
  stacked_widget->addWidget(string_widget);

  // 布尔属性的输入控件
  auto* bool_widget       = new QWidget();
  auto* bool_layout       = new QFormLayout(bool_widget);
  auto* default_bool_edit = new QCheckBox();
  bool_layout->addRow("默认值:", default_bool_edit);
  stacked_widget->addWidget(bool_widget);

  // 枚举属性的输入控件
  auto* enum_widget        = new QWidget();
  auto* enum_layout        = new QFormLayout(enum_widget);
  auto* options_edit       = new QLineEdit();
  auto* default_index_edit = new QSpinBox();
  options_edit->setPlaceholderText("选项1,选项2,选项3...");
  default_index_edit->setRange(0, 100);
  enum_layout->addRow("选项(逗号分隔):", options_edit);
  enum_layout->addRow("默认索引:", default_index_edit);
  stacked_widget->addWidget(enum_widget);

  // 文件路径属性的输入控件
  auto* file_path_widget       = new QWidget();
  auto* file_path_layout       = new QFormLayout(file_path_widget);
  auto* default_file_path_edit = new QLineEdit();
  auto* filter_edit            = new QLineEdit();
  filter_edit->setText("所有文件 (*)");
  filter_edit->setPlaceholderText("例如: 图片文件 (*.png *.jpg)");
  file_path_layout->addRow("默认路径:", default_file_path_edit);
  file_path_layout->addRow("文件过滤器:", filter_edit);
  stacked_widget->addWidget(file_path_widget);

  // 文件夹路径属性的输入控件
  auto* dir_path_widget       = new QWidget();
  auto* dir_path_layout       = new QFormLayout(dir_path_widget);
  auto* default_dir_path_edit = new QLineEdit();
  dir_path_layout->addRow("默认路径:", default_dir_path_edit);
  stacked_widget->addWidget(dir_path_widget);

  // 根据类型选择显示对应的输入控件
  connect(type_combo, QOverload<int>::of(&QComboBox::currentIndexChanged), stacked_widget,
          &QStackedWidget::setCurrentIndex);
  layout->addRow(stacked_widget);

  // 添加确定和取消按钮
  auto* button_box = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
  connect(button_box, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
  connect(button_box, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);
  layout->addRow(button_box);

  // 显示对话框
  if (dialog.exec() == QDialog::Accepted)
  {
    auto group = group_edit->text().toStdString();
    auto key   = key_edit->text().toStdString();
    int type   = type_combo->currentIndex();

    // 检查该组是否允许添加
    if (isGroupReadOnly(group_edit->text()))
    {
      QMessageBox::warning(this, "操作受限", QString("组 '%1' 不允许添加新属性").arg(group_edit->text()));
      return;
    }

    // 检查输入是否有效
    if (group.empty() || key.empty())
    {
      QMessageBox::warning(this, "输入错误", "组名和属性名不能为空");
      return;
    }

    // 根据类型注册不同的属性
    switch (type)
    {
    case 0:  // 整数
      registerInt(group, key, default_int_edit->value(), min_int_edit->value(), max_int_edit->value(),
                  step_int_edit->value());
      break;
    case 1:  // 浮点数
      registerDouble(group, key, default_double_edit->value(), min_double_edit->value(), max_double_edit->value(),
                     step_double_edit->value(), decimals_edit->value());
      break;
    case 2:  // 字符串
      registerString(group, key, default_string_edit->text().toStdString(), height_factor_edit->value());
      break;
    case 3:  // 布尔值
      registerBool(group, key, default_bool_edit->isChecked());
      break;
    case 4:  // 枚举
    {
      QStringList options = options_edit->text().split(',', Qt::SkipEmptyParts);

      if (options.isEmpty())
      {
        QMessageBox::warning(this, "输入错误", "枚举选项不能为空");
        return;
      }
      registerEnum(group, key, options, default_index_edit->value());
    }
    break;
    case 5:  // 文件路径
      registerFilePath(group, key, default_file_path_edit->text().toStdString(), filter_edit->text().toStdString());
      break;
    case 6:  // 文件夹路径
      registerDirectoryPath(group, key, default_dir_path_edit->text().toStdString());
      break;
    default: QMessageBox::warning(this, "类型错误", "未知的属性类型"); break;
    }

    // 刷新UI
    refreshUi();
  }
}

void PropertyView::slotDeletePropertyClicked()
{
  // 获取当前选中的项
  QTreeWidgetItem* selected_item = widget_tree_->currentItem();
  if (selected_item == nullptr)
  {
    QMessageBox::warning(this, "选择错误", "请先选择要删除的属性");
    return;
  }

  // 检查是否选中的是组项还是属性项
  QTreeWidgetItem* parent_item = selected_item->parent();
  QString group;

  if (parent_item == nullptr)  // 选中的是组项
  {
    group = selected_item->text(0);
  }
  else  // 选中的是属性项
  {
    group = parent_item->text(0);
  }

  // 检查该组是否允许删除
  if (isGroupReadOnly(group))
  {
    QMessageBox::warning(this, "操作受限", QString("组 '%1' 不允许删除").arg(group));
    return;
  }

  if (parent_item == nullptr)  // 选中的是组项
  {
    // 确认是否删除整个组
    QMessageBox::StandardButton reply = QMessageBox::question(
      this, "确认删除", QString("确定要删除组 '%1' 及其所有属性吗?").arg(group), QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes)
    {
      unregisterGroup(group.toStdString());
    }
  }
  else  // 选中的是属性项
  {
    QString key = selected_item->text(0);

    // 确认是否删除属性
    QMessageBox::StandardButton reply = QMessageBox::question(
      this, "确认删除", QString("确定要删除属性 '%1.%2' 吗?").arg(group).arg(key), QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes)
    {
      unregisterProperty(group.toStdString(), key.toStdString());
    }
  }
}

bool PropertyView::saveToJson(std::string& _json) const
{
  // 使用nlohmann::ordered_json实现
  return saveToOrderedJson(_json);
}

bool PropertyView::saveToJson(const QString& _file_path) const
{
  std::string json_str;
  if (!saveToJson(json_str))
  {
    return false;
  }

  QFile file(_file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("无法打开文件进行写入: {}", _file_path);
    return false;
  }
  file.write(json_str.c_str(), static_cast<qint64>(json_str.size()));
  file.close();
  return true;
}

bool PropertyView::loadFromJson(const std::string& _json)
{
  // 使用nlohmann::ordered_json实现
  return loadFromOrderedJson(_json);
}

bool PropertyView::loadFromJson(const QString& _file_path)
{
  // 打开并读取文件
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("PropertyView::loadFromJson: 无法打开文件进行读取: {}", _file_path);
    return false;
  }

  QByteArray data = file.readAll();
  file.close();

  return loadFromJson(data.toStdString());
}

void PropertyView::setButtonHidden(const BtnHidden _hid)
{
  add_button_->setHidden(_hid.add_btn_hidden);
  delete_button_->setHidden(_hid.delete_btn_hidden);
  save_button_->setHidden(_hid.save_btn_hidden);
  load_button_->setHidden(_hid.load_btn_hidden);
  apply_button_->setHidden(_hid.apply_btn_hidden);
}

void PropertyView::setModel(const PropertyModel::Ptr& _model)
{
  disconnectModel();
  model_ = _model;
  connectModel();
  refreshUi();
}

void PropertyView::setModel(const PropertyModel::Ptr&& _model)
{
  disconnectModel();
  model_ = _model;
  connectModel();
  refreshUi();
}

void PropertyView::slotSaveClicked()
{
  QString file_path = QFileDialog::getSaveFileName(this, "保存属性", "", "JSON文件 (*.json)");
  if (!file_path.isEmpty())
  {
    if (saveToJson(file_path))
    {
      QMessageBox::information(this, "保存成功", "属性已成功保存到文件");
    }
  }
}

void PropertyView::slotLoadClicked()
{
  QString file_path = QFileDialog::getOpenFileName(this, "加载属性", "", "JSON文件 (*.json)");
  if (!file_path.isEmpty())
  {
    if (loadFromJson(file_path))
    {
      QMessageBox::information(this, "加载成功", "属性已成功从文件加载");
    }
  }
}

void PropertyView::slotValueChanged(const std::string& _group, const std::string& _key, const QVariant& _value)
{
  // 当模型中的属性值变化时，更新UI中的显示
  std::string full_key = _group + "." + _key;
  auto iter            = model_->properties().find(full_key);
  if (iter == model_->properties().end())
  {
    return;
  }

  // 找到对应的属性项
  auto* groupItem = findOrCreateGroupItem(_group);
  for (int i = 0; i < groupItem->childCount(); ++i)
  {
    auto* propertyItem = groupItem->child(i);
    if (propertyItem->text(0).toStdString() != _key)
    {
      continue;
    }

    // 找到对应的属性项，更新其值
    auto* widget = widget_tree_->itemWidget(propertyItem, 1);
    if (nullptr != widget)
    {
      // 更新控件的值
      iter->second->setValue(_value);
    }
    break;
  }
}

void PropertyView::slotModelChanged()
{
  // 当模型结构变化时，刷新整个UI
  refreshUi();
}

void PropertyView::saveUserValues()
{
  if (!model_)
  {
    return;
  }

  QSettings settings("RoboSense", this->windowTitle());
  settings.setIniCodec(QTextCodec::codecForName("UTF_8"));
  settings.beginGroup("PropertyEditorUserValues");
  for (const auto& [fullKey, property] : model_->properties())
  {
    settings.setValue(QString::fromStdString(fullKey), property->value());
  }
  settings.endGroup();
}

void PropertyView::loadUserValues()
{
  if (!model_)
  {
    return;
  }

  QSettings settings("RoboSense", this->windowTitle());
  settings.setIniCodec(QTextCodec::codecForName("UTF_8"));
  settings.beginGroup("PropertyEditorUserValues");
  for (const auto& [fullKey, property] : model_->properties())
  {
    QVariant value = settings.value(QString::fromStdString(fullKey), property->value());
    property->setValue(value);
  }
  settings.endGroup();
}

void PropertyView::slotApplyClicked()
{
  saveUserValues();
  QMessageBox::information(this, "应用成功", "属性已应用并保存");
}

bool PropertyView::saveToOrderedJson(std::string& _json) const
{
  if (!model_)
  {
    LOG_ERROR("PropertyView::saveToOrderedJson: model_ is nullptr");
    return false;
  }

  nlohmann::ordered_json root_object;
  nlohmann::ordered_json groups_object;

  // 遍历所有组和属性
  for (const auto& [name, group] : model_->groups())
  {
    nlohmann::ordered_json group_object;
    auto group_name = name;

    // 遍历该组下的所有属性
    for (const auto& [full_key, property] : model_->properties())
    {
      QString full_key_str = QString::fromStdString(full_key);
      QStringList parts    = full_key_str.split('.');

      if (parts.size() == 2 && parts[0].toStdString() == group_name)
      {
        const std::string key = parts[1].toStdString();
        QVariant value        = property->value();
        nlohmann::ordered_json prop_obj;

        // 判断类型
        if (auto* int_prop = dynamic_cast<IntItem*>(property.get()))
        {
          prop_obj["type"]  = "int";
          prop_obj["value"] = value.toInt();
          prop_obj["min"]   = int_prop->min_;
          prop_obj["max"]   = int_prop->max_;
          prop_obj["step"]  = int_prop->step_;
        }
        else if (auto* double_prop = dynamic_cast<DoubleItem*>(property.get()))
        {
          prop_obj["type"]     = "double";
          prop_obj["value"]    = value.toDouble();
          prop_obj["min"]      = double_prop->min_;
          prop_obj["max"]      = double_prop->max_;
          prop_obj["step"]     = double_prop->step_;
          prop_obj["decimals"] = double_prop->decimals_;
        }
        else if (auto* str_prop = dynamic_cast<StringItem*>(property.get()))
        {
          prop_obj["type"]  = "string";
          prop_obj["value"] = value.toString().toStdString();
        }
        else if (auto* bool_prop = dynamic_cast<BoolItem*>(property.get()))
        {
          prop_obj["type"]  = "bool";
          prop_obj["value"] = value.toBool();
        }
        else if (auto* enum_prop = dynamic_cast<EnumItem*>(property.get()))
        {
          prop_obj["type"]  = "enum";
          prop_obj["value"] = value.toInt();

          nlohmann::json::array_t options_arr;
          for (const auto& opt : enum_prop->options_)
          {
            options_arr.emplace_back(opt.toStdString());
          }
          prop_obj["options"]       = options_arr;
          prop_obj["default_index"] = enum_prop->default_index_;
        }
        else if (auto* file_path_prop = dynamic_cast<FilePathPropertyItem*>(property.get()))
        {
          prop_obj["type"]   = "file_path";
          prop_obj["value"]  = value.toString().toStdString();
          prop_obj["filter"] = file_path_prop->filter_.toStdString();
        }
        else if (auto* dir_path_prop = dynamic_cast<DirectoryPathPropertyItem*>(property.get()))
        {
          prop_obj["type"]  = "directory_path";
          prop_obj["value"] = value.toString().toStdString();
        }
        else
        {
          prop_obj["type"]  = "unknown";
          prop_obj["value"] = value.toString().toStdString();
        }
        group_object[key] = prop_obj;
      }
    }

    if (!group_object.empty())
    {
      groups_object[group_name] = group_object;
    }
  }

  root_object["groups"] = groups_object;
  _json                 = root_object.dump(2);  // 使用2个空格缩进，使JSON更易读
  return true;
}

bool PropertyView::loadFromOrderedJson(const std::string& _json)
{
  if (!model_)
  {
    LOG_ERROR("PropertyView::loadFromOrderedJson: model_ is nullptr");
    return false;
  }

  LOG_DEBUG("PropertyView::loadFromOrderedJson: {}", _json);

  try
  {
    // 解析JSON字符串
    nlohmann::ordered_json json_object = nlohmann::ordered_json::parse(_json);

    // 检查是否有嵌套的parameters.groups结构（从DataSerializer加载的情况）
    if (json_object.contains("parameters") && json_object["parameters"].is_object() &&
        json_object["parameters"].contains("groups") && json_object["parameters"]["groups"].is_object())
    {
      // 使用parameters.groups
      json_object = json_object["parameters"];
      LOG_INFO("PropertyView::loadFromOrderedJson: 检测到嵌套的parameters.groups结构");
    }

    if (!json_object.contains("groups") || !json_object["groups"].is_object())
    {
      LOG_ERROR("PropertyView::loadFromOrderedJson: JSON对象缺少有效的groups对象");
      return false;
    }

    nlohmann::ordered_json groups_object = json_object["groups"];

    // 处理每个组
    for (const auto& [group_name, group_value] : groups_object.items())
    {
      if (!group_value.is_object())
      {
        continue;
      }

      // 处理组中的每个属性
      for (const auto& [key, prop_value] : group_value.items())
      {
        if (!prop_value.is_object())
        {
          continue;
        }

        std::string type = prop_value.value("type", "");

        // 根据属性类型注册不同的属性
        if (type == "int")
        {
          int val  = prop_value.value("value", 0);
          int min  = prop_value.value("min", std::numeric_limits<int>::min());
          int max  = prop_value.value("max", std::numeric_limits<int>::max());
          int step = prop_value.value("step", 1);
          registerInt(group_name, key, val, min, max, step);
        }
        else if (type == "double")
        {
          double val   = prop_value.value("value", 0.0);
          double min   = prop_value.value("min", -std::numeric_limits<double>::max());
          double max   = prop_value.value("max", std::numeric_limits<double>::max());
          double step  = prop_value.value("step", 0.1);
          int decimals = prop_value.value("decimals", 2);
          registerDouble(group_name, key, val, min, max, step, decimals);
        }
        else if (type == "string")
        {
          std::string val = prop_value.value("value", "");
          registerString(group_name, key, val);
        }
        else if (type == "bool")
        {
          bool val = prop_value.value("value", false);
          registerBool(group_name, key, val);
        }
        else if (type == "enum")
        {
          int val          = prop_value.value("value", 0);
          auto options_arr = prop_value.value("options", nlohmann::json::array());
          QStringList options;
          for (const auto& option_value : options_arr)
          {
            options.append(QString::fromStdString(option_value));
          }
          int default_index = prop_value.value("default_index", 0);
          registerEnum(group_name, key, options, default_index);
          setValue(group_name, key, val);
        }
        else if (type == "file_path")
        {
          std::string val    = prop_value.value("value", "");
          std::string filter = prop_value.value("filter", "所有文件 (*)");
          registerFilePath(group_name, key, val, filter);
        }
        else if (type == "directory_path")
        {
          std::string val = prop_value.value("value", "");
          registerDirectoryPath(group_name, key, val);
        }
      }
    }

    // 刷新UI以显示新加载的属性
    refreshUi();
    return true;
  }
  catch (const nlohmann::json::exception& e)
  {
    LOG_ERROR("PropertyView::loadFromOrderedJson: JSON解析错误: {}", e.what());
    return false;
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("PropertyView::loadFromOrderedJson: 异常: {}", e.what());
    return false;
  }
}

bool PropertyView::loadFromJsonExcludeGroups(const std::string& _json, const std::vector<std::string>& _exclude_groups)
{
  if (!model_)
  {
    LOG_ERROR("PropertyView::loadFromJsonExcludeGroups: model_ is nullptr");
    return false;
  }

  try
  {
    // 解析JSON字符串
    nlohmann::ordered_json json_object = nlohmann::ordered_json::parse(_json);

    // 检查是否有嵌套的parameters.groups结构
    if (json_object.contains("parameters") && json_object["parameters"].is_object() &&
        json_object["parameters"].contains("groups") && json_object["parameters"]["groups"].is_object())
    {
      json_object = json_object["parameters"];
    }

    if (!json_object.contains("groups") || !json_object["groups"].is_object())
    {
      LOG_ERROR("PropertyView::loadFromJsonExcludeGroups: JSON对象缺少有效的groups对象");
      return false;
    }

    // 创建一个新的groups对象，排除指定的组
    nlohmann::ordered_json filtered_json;
    filtered_json["groups"] = nlohmann::ordered_json::object();

    for (const auto& [group_name, group_value] : json_object["groups"].items())
    {
      // 检查组名是否在排除列表中
      bool should_exclude = false;
      for (const auto& exclude_group : _exclude_groups)
      {
        if (group_name == exclude_group)
        {
          should_exclude = true;
          break;
        }
      }

      // 如果不在排除列表中，则添加到过滤后的JSON中
      if (!should_exclude)
      {
        filtered_json["groups"][group_name] = group_value;
      }
    }

    // 使用过滤后的JSON加载属性
    return loadFromJson(filtered_json.dump());
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("PropertyView::loadFromJsonExcludeGroups: 异常: {}", e.what());
    return false;
  }
}

}  // namespace robosense::lidar
