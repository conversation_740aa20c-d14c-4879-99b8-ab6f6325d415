/*****************************************************************************
 * Copyright (c) 2024 RoboSense
 * All rights reserved.
 *
 * This software is licensed under the terms of the RoboSense License
 * Agreement. Please see the LICENSE file for details.
 *****************************************************************************/

#include "display_switcher_widget.h"
#include "node_models/bt_node_model.h"
#include <QLabel>
#include <QVBoxLayout>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

DisplaySwitcherWidget::DisplaySwitcherWidget(QWidget* _parent) :
  QWidget(_parent),
  main_layout_(nullptr),
  stacked_widget_(nullptr),
  default_page_(nullptr),
  default_label_(nullptr),
  current_node_id_(INVALID_NODE_ID),
  has_current_node_(false)
{
  setupUi();
}

DisplaySwitcherWidget::~DisplaySwitcherWidget() { clearAllNodes(); }

void DisplaySwitcherWidget::setupUi()
{
  // 创建主布局
  main_layout_ = new QVBoxLayout(this);
  main_layout_->setContentsMargins(0, 0, 0, 0);
  main_layout_->setSpacing(0);

  // 创建堆叠widget
  stacked_widget_ = new QStackedWidget(this);
  main_layout_->addWidget(stacked_widget_);

  // 创建并添加默认页面
  default_page_ = createDefaultPage();
  stacked_widget_->addWidget(default_page_);

  // 初始显示默认页面
  showDefaultPage();

  setLayout(main_layout_);
}

QWidget* DisplaySwitcherWidget::createDefaultPage()
{
  auto* page   = new QWidget();
  auto* layout = new QVBoxLayout(page);

  default_label_ = new QLabel("请选择一个节点以查看其属性");
  default_label_->setAlignment(Qt::AlignCenter);
  default_label_->setStyleSheet("QLabel {"
                                "  color: #666666;"
                                "  font-size: 14px;"
                                "  padding: 20px;"
                                "}");

  layout->addStretch();
  layout->addWidget(default_label_);
  layout->addStretch();

  page->setLayout(layout);
  return page;
}

void DisplaySwitcherWidget::onNodeSelected(std::size_t _node_id, BtNodeModel* _node_model)
{
  if (_node_model == nullptr)
  {
    LOG_WARN("DisplaySwitcherWidget::onNodeSelected - node_model is nullptr for node_id: {}", _node_id);
    showDefaultPage();
    return;
  }

  LOG_DEBUG("DisplaySwitcherWidget::onNodeSelected - node_id: {}, node_name: {}", _node_id,
            _node_model->registrationName().toStdString());

  // 如果节点还没有PropertyView，先创建一个
  if (property_views_.find(_node_id) == property_views_.end())
  {
    onNodeCreated(_node_id, _node_model);
  }

  // 显示对应的属性面板
  showNodeProperties(_node_id);
}

void DisplaySwitcherWidget::onNodeCreated(std::size_t _node_id, BtNodeModel* _node_model)
{
  if (_node_model == nullptr)
  {
    LOG_ERROR("DisplaySwitcherWidget::onNodeCreated - node_model is nullptr for node_id: {}", _node_id);
    return;
  }

  LOG_DEBUG("DisplaySwitcherWidget::onNodeCreated - node_id: {}, node_name: {}", _node_id,
            _node_model->registrationName().toStdString());

  // 如果已经存在，先清理
  if (property_views_.find(_node_id) != property_views_.end())
  {
    onNodeDeleted(_node_id);
  }

  // 创建新的PropertyView
  PropertyView* property_view = createPropertyViewForNode(_node_id, _node_model);
  if (property_view == nullptr)
  {
    LOG_ERROR("DisplaySwitcherWidget::onNodeCreated - Failed to create PropertyView for node_id: {}", _node_id);
    return;
  }

  // 存储映射关系
  property_views_[_node_id] = property_view;
  node_models_[_node_id]    = _node_model;

  // 添加到堆叠widget
  stacked_widget_->addWidget(property_view);

  LOG_DEBUG("DisplaySwitcherWidget::onNodeCreated - Successfully created PropertyView for node_id: {}", _node_id);
}

void DisplaySwitcherWidget::onNodeDeleted(std::size_t _node_id)
{
  LOG_DEBUG("DisplaySwitcherWidget::onNodeDeleted - node_id: {}", _node_id);

  // 查找并移除PropertyView
  auto property_view_it = property_views_.find(_node_id);
  if (property_view_it != property_views_.end())
  {
    PropertyView* property_view = property_view_it->second;

    // 从堆叠widget中移除
    stacked_widget_->removeWidget(property_view);

    // 删除PropertyView
    property_view->deleteLater();

    // 从映射中移除
    property_views_.erase(property_view_it);
  }

  // 移除节点模型映射
  auto node_model_it = node_models_.find(_node_id);
  if (node_model_it != node_models_.end())
  {
    node_models_.erase(node_model_it);
  }

  // 如果删除的是当前显示的节点，切换到默认页面
  if (has_current_node_ && current_node_id_ == _node_id)
  {
    showDefaultPage();
  }

  LOG_DEBUG("DisplaySwitcherWidget::onNodeDeleted - Successfully deleted PropertyView for node_id: {}", _node_id);
}

void DisplaySwitcherWidget::clearAllNodes()
{
  LOG_DEBUG("DisplaySwitcherWidget::clearAllNodes - Clearing all nodes");

  // 清理所有PropertyView
  for (auto& [node_id, property_view] : property_views_)
  {
    stacked_widget_->removeWidget(property_view);
    property_view->deleteLater();
  }

  // 清空映射
  property_views_.clear();
  node_models_.clear();

  // 显示默认页面
  showDefaultPage();

  LOG_DEBUG("DisplaySwitcherWidget::clearAllNodes - All nodes cleared");
}

PropertyView* DisplaySwitcherWidget::createPropertyViewForNode(std::size_t _node_id, BtNodeModel* _node_model)
{
  if (_node_model == nullptr)
  {
    LOG_ERROR("DisplaySwitcherWidget::createPropertyViewForNode - node_model is nullptr");
    return nullptr;
  }

  PropertyModel* property_model = _node_model->propertyModel();
  if (property_model == nullptr)
  {
    LOG_ERROR("DisplaySwitcherWidget::createPropertyViewForNode - property_model is nullptr for node_id: {}", _node_id);
    return nullptr;
  }

  LOG_DEBUG("DisplaySwitcherWidget::createPropertyViewForNode - node_id: {}, PropertyModel address: {}", _node_id,
            static_cast<void*>(property_model));

  // 创建PropertyView，隐藏单个组，隐藏所有按钮
  auto* property_view = new PropertyView(this, true, _node_model->registrationName());

  // 设置属性模型 - 使用 PropertyModel::Ptr 类型
  PropertyModel::Ptr model_ptr(property_model, [](PropertyModel*) {});  // 空删除器，因为PropertyModel由BtNodeModel管理
  property_view->setModel(model_ptr);

  // 隐藏所有按钮
  property_view->setButtonHidden(PropertyView::BtnHidden { true, true, true, true, true });

  // 连接属性值变化信号 - 连接到PropertyModel的信号
  connect(property_model, &PropertyModel::signalValueChanged, this,
          [this, _node_id](const std::string& _group, const std::string& _key, const QVariant& _value) {
            // 直接发出信号，不修改 current_node_id_
            emit propertyValueChanged(_node_id, _group, _key, _value);
          });

  // 连接属性恢复完成信号 - 当节点属性恢复完成时刷新UI
  connect(_node_model, &BtNodeModel::propertiesRestored, this, [this, property_view, _node_id]() {
    LOG_DEBUG(
      "DisplaySwitcherWidget::createPropertyViewForNode - 收到属性恢复完成信号，刷新PropertyView for node_id: {}",
      _node_id);
    property_view->refreshUi();
  });

  return property_view;
}

void DisplaySwitcherWidget::showNodeProperties(std::size_t _node_id)
{
  auto it = property_views_.find(_node_id);
  if (it == property_views_.end())
  {
    LOG_WARN("DisplaySwitcherWidget::showNodeProperties - PropertyView not found for node_id: {}", _node_id);
    showDefaultPage();
    return;
  }

  PropertyView* property_view = it->second;
  stacked_widget_->setCurrentWidget(property_view);

  current_node_id_  = _node_id;
  has_current_node_ = true;

  LOG_DEBUG("DisplaySwitcherWidget::showNodeProperties - Showing properties for node_id: {}", _node_id);
}

void DisplaySwitcherWidget::showDefaultPage()
{
  stacked_widget_->setCurrentWidget(default_page_);
  current_node_id_  = INVALID_NODE_ID;
  has_current_node_ = false;

  LOG_DEBUG("DisplaySwitcherWidget::showDefaultPage - Showing default page");
}

void DisplaySwitcherWidget::refreshCurrentView()
{
  if (has_current_node_)
  {
    auto it = property_views_.find(current_node_id_);
    if (it != property_views_.end())
    {
      it->second->refreshUi();
      LOG_DEBUG("DisplaySwitcherWidget::refreshCurrentView - Refreshed view for node_id: {}", current_node_id_);
    }
  }
}

void DisplaySwitcherWidget::onPropertyValueChanged(const std::string& _group,
                                                   const std::string& _key,
                                                   const QVariant& _value)
{
  if (has_current_node_)
  {
    LOG_DEBUG("DisplaySwitcherWidget::onPropertyValueChanged - node_id: {}, group: {}, key: {}", current_node_id_,
              _group, _key);

    emit propertyValueChanged(current_node_id_, _group, _key, _value);
  }
}

}  // namespace robosense::lidar
