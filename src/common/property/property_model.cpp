﻿
/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "property_model.h"
#include <QCheckBox>
#include <QComboBox>
#include <QDialogButtonBox>
#include <QFormLayout>
#include <QHeaderView>
#include <QLineEdit>
#include <QPlainTextEdit>
#include <QSpinBox>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <qchar.h>
#include <rsfsc_log/rsfsc_log_macro.h>
// QCodeEditor
#include <QCXXHighlighter>
#include <QCodeEditor>
#include <QGLSLCompleter>
#include <QGLSLHighlighter>
#include <QJSONHighlighter>
#include <QLuaCompleter>
#include <QLuaHighlighter>
#include <QPythonCompleter>
#include <QPythonHighlighter>
#include <QSyntaxStyle>
#include <QXMLHighlighter>
namespace robosense::lidar
{

namespace
{
// 创建新的高亮器实例的工厂函数
QStyleSyntaxHighlighter* createHighlighter(const QString& _type)
{
  if (_type == "C++")
  {
    return new QCXXHighlighter();
  }
  if (_type == "GLSL")
  {
    return new QGLSLHighlighter();
  }
  if (_type == "XML")
  {
    return new QXMLHighlighter();
  }
  if (_type == "JSON")
  {
    return new QJSONHighlighter();
  }
  if (_type == "LUA")
  {
    return new QLuaHighlighter();
  }
  if (_type == "Python")
  {
    return new QPythonHighlighter();
  }
  return nullptr;
}

// 创建新的补全器实例的工厂函数
QCompleter* createCompleter(const QString& _type)
{
  if (_type == "GLSL")
  {
    return new QGLSLCompleter();
  }
  if (_type == "LUA")
  {
    return new QLuaCompleter();
  }
  if (_type == "Python")
  {
    return new QPythonCompleter();
  }
  return nullptr;
}

// 保留原有的全局函数以兼容现有代码（但不再使用共享实例）
QVector<QPair<QString, QStyleSyntaxHighlighter*>> gHighLighter()
{
  static QVector<QPair<QString, QStyleSyntaxHighlighter*>> HIGH_LIGHTERS = {
    { "None", nullptr }, { "C++", nullptr },  // 不再创建共享实例
    { "GLSL", nullptr }, { "XML", nullptr }, { "JSON", nullptr }, { "LUA", nullptr }, { "Python", nullptr },
  };
  return HIGH_LIGHTERS;
}

QVector<QPair<QString, QCompleter*>> gCompleter()
{
  static QVector<QPair<QString, QCompleter*>> COMPLETERS = {
    { "None", nullptr },
    { "GLSL", nullptr },  // 不再创建共享实例
    { "LUA", nullptr },
    { "Python", nullptr },
  };
  return COMPLETERS;
}
}  // namespace

QWidget* IntItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  auto* spin_box = new QSpinBox();
  spin_box->setRange(min_, max_);
  spin_box->setSingleStep(step_);
  spin_box->setValue(default_value_);

  layout->addWidget(spin_box);
  container->setLayout(layout);
  widget_ = container;

  // 连接信号
  itemConnect();

  return container;
}

void IntItem::setValue(const QVariant& _value)
{
  if (widget_ != nullptr)
  {
    if (auto* spin_box = widget_->findChild<QSpinBox*>())
    {
      spin_box->setValue(_value.toInt());
    }
  }
}

void IntItem::forceSetValue(const QVariant& _value)
{
  // IntItem 不需要检查焦点状态，直接调用 setValue
  setValue(_value);
}

[[nodiscard]] QVariant IntItem::value() const
{
  if (widget_ != nullptr)
  {
    if (auto* spin_box = widget_->findChild<QSpinBox*>())
    {
      return spin_box->value();
    }
  }
  return default_value_;
}

QWidget* DoubleItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  auto* spin_box = new QDoubleSpinBox();
  spin_box->setRange(min_, max_);
  spin_box->setSingleStep(step_);
  spin_box->setDecimals(decimals_);
  spin_box->setValue(default_value_);

  layout->addWidget(spin_box);
  container->setLayout(layout);
  widget_ = container;

  // 连接信号
  itemConnect();

  return container;
}

void DoubleItem::setValue(const QVariant& _value)
{
  if (widget_ != nullptr)
  {
    if (auto* spin_box = widget_->findChild<QDoubleSpinBox*>())
    {
      spin_box->setValue(_value.toDouble());
    }
  }
}

void DoubleItem::forceSetValue(const QVariant& _value)
{
  // DoubleItem 不需要检查焦点状态，直接调用 setValue
  setValue(_value);
}

[[nodiscard]] QVariant DoubleItem::value() const
{
  if (widget_ != nullptr)
  {
    if (auto* spin_box = widget_->findChild<QDoubleSpinBox*>())
    {
      return spin_box->value();
    }
  }
  return default_value_;
}

QWidget* StringItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  // 标准控件高度为22像素，乘以高度因子
  constexpr int kStandardHeight = 22;
  int adjusted_height           = kStandardHeight * height_factor_;

  if (height_factor_ > 1)
  {
    // 多行模式：使用QPlainTextEdit，支持多行文本、滚动条和换行
    auto* text_edit = new QCodeEditor();

    auto* highlighter = createHighlighter("LUA");
    auto* completer   = createCompleter("LUA");

    if (highlighter != nullptr)
    {
      text_edit->setHighlighter(highlighter);
    }
    if (completer != nullptr)
    {
      text_edit->setCompleter(completer);
    }

    text_edit->setPlainText(QString::fromStdString(default_value_));

    // 设置QPlainTextEdit的大小策略
    text_edit->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 设置最小高度
    text_edit->setMinimumHeight(adjusted_height);

    // 启用自动换行
    text_edit->setWordWrapMode(QTextOption::WrapAtWordBoundaryOrAnywhere);

    // 设置垂直滚动条策略
    text_edit->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 设置水平滚动条策略
    text_edit->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    layout->addWidget(text_edit);
  }
  else
  {
    // 单行模式：使用QLineEdit
    auto* line_edit = new QLineEdit();
    line_edit->setText(QString::fromStdString(default_value_));

    // 设置QLineEdit的大小策略
    line_edit->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 设置固定高度
    line_edit->setFixedHeight(adjusted_height);

    layout->addWidget(line_edit);
  }

  container->setLayout(layout);
  widget_ = container;

  // 连接信号
  itemConnect();

  return container;
}

void StringItem::setValue(const QVariant& _value)
{
  if (widget_ == nullptr)
  {
    return;
  }

  if (height_factor_ > 1)
  {
    // 多行模式：使用QPlainTextEdit
    auto* text_edit = widget_->findChild<QCodeEditor*>();
    // 只有当控件没有焦点时才更新文本
    // 这样可以避免在用户输入过程中干扰光标位置
    if (text_edit == nullptr || text_edit->hasFocus())
    {
      return;
    }

    // 防止在设置文本时卡住
    QString new_text = _value.toString();
    if (text_edit->toPlainText() != new_text)
    {
      text_edit->setPlainText(new_text);
    }
  }
  else
  {
    // 单行模式：使用QLineEdit
    auto* line_edit = widget_->findChild<QLineEdit*>();
    // 只有当控件没有焦点时才更新文本
    // 这样可以避免在用户输入过程中干扰光标位置
    if (line_edit == nullptr || line_edit->hasFocus())
    {
      return;
    }

    QString new_text = _value.toString();
    if (line_edit->text() != new_text)
    {
      line_edit->setText(new_text);
    }
  }
}

void StringItem::forceSetValue(const QVariant& _value)
{
  if (widget_ == nullptr)
  {
    return;
  }

  if (height_factor_ > 1)
  {
    // 多行模式：使用QPlainTextEdit
    auto* text_edit = widget_->findChild<QCodeEditor*>();
    if (text_edit == nullptr)
    {
      return;
    }

    // 强制更新文本，忽略焦点状态
    QString new_text = _value.toString();
    if (text_edit->toPlainText() != new_text)
    {
      text_edit->setPlainText(new_text);
    }
  }
  else
  {
    // 单行模式：使用QLineEdit
    auto* line_edit = widget_->findChild<QLineEdit*>();
    if (line_edit == nullptr)
    {
      return;
    }

    // 强制更新文本，忽略焦点状态
    QString new_text = _value.toString();
    if (line_edit->text() != new_text)
    {
      line_edit->setText(new_text);
    }
  }
}

[[nodiscard]] QVariant StringItem::value() const
{
  QVariant result;
  if (widget_ != nullptr)
  {
    if (height_factor_ > 1)
    {
      // 多行模式：使用QPlainTextEdit
      if (auto* text_edit = widget_->findChild<QCodeEditor*>())
      {
        result = text_edit->toPlainText();
      }
    }
    else
    {
      // 单行模式：使用QLineEdit
      if (auto* line_edit = widget_->findChild<QLineEdit*>())
      {
        result = line_edit->text();
      }
    }
  }

  if (result.isNull())
  {
    result = QString::fromStdString(default_value_);
  }

  return result;
}

QWidget* BoolItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  auto* check_box = new QCheckBox();
  check_box->setChecked(default_value_);

  layout->addWidget(check_box);
  container->setLayout(layout);
  widget_ = container;

  // 连接信号
  itemConnect();

  return container;
}

void BoolItem::setValue(const QVariant& _value)
{
  if (widget_ != nullptr)
  {
    if (auto* check_box = widget_->findChild<QCheckBox*>())
    {
      check_box->setChecked(_value.toBool());
    }
  }
}

void BoolItem::forceSetValue(const QVariant& _value)
{
  // BoolItem 不需要检查焦点状态，直接调用 setValue
  setValue(_value);
}

[[nodiscard]] QVariant BoolItem::value() const
{
  if (widget_ != nullptr)
  {
    if (auto* check_box = widget_->findChild<QCheckBox*>())
    {
      return check_box->isChecked();
    }
  }
  return default_value_;
}

QWidget* EnumItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  auto* combo_box = new QComboBox();
  combo_box->addItems(options_);
  combo_box->setCurrentIndex(default_index_);

  // 设置QComboBox的大小策略，使其高度与其他编辑器控件一致
  combo_box->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
  // 设置固定高度，与其他编辑器控件保持一致
  constexpr int kFixedHeight = 22;
  combo_box->setFixedHeight(kFixedHeight);

  layout->addWidget(combo_box);
  container->setLayout(layout);
  widget_ = container;

  // 连接信号
  itemConnect();

  return container;
}

void EnumItem::setValue(const QVariant& _value)
{
  if (widget_ != nullptr)
  {
    if (auto* combo_box = widget_->findChild<QComboBox*>())
    {
      combo_box->setCurrentIndex(_value.toInt());
    }
  }
}

void EnumItem::forceSetValue(const QVariant& _value)
{
  // EnumItem 不需要检查焦点状态，直接调用 setValue
  setValue(_value);
}

[[nodiscard]] QVariant EnumItem::value() const
{
  if (widget_ != nullptr)
  {
    if (auto* combo_box = widget_->findChild<QComboBox*>())
    {
      return combo_box->currentIndex();
    }
  }
  return default_index_;
}

[[nodiscard]] QString EnumItem::currentText() const
{
  if (widget_ != nullptr)
  {
    if (auto* combo_box = widget_->findChild<QComboBox*>())
    {
      return combo_box->currentText();
    }
  }
  return options_.value(default_index_);
}

bool PropertyModel::unregisterProperty(const std::string& _group, const std::string& _key)
{
  // QWriteLocker locker(&lock_);
  std::string full_key = (_group + "." + _key);
  auto iter            = properties_.find(full_key);
  if (iter != properties_.end())
  {
    properties_.erase(iter);
    // 检查该组是否还有其他属性
    bool has_properties_in_group = std::any_of(properties_.begin(), properties_.end(), [&](const auto& param) {
      QString key_str   = QString::fromStdString(param.first);
      QStringList parts = key_str.split('.');
      return parts.size() == 2 && parts[0].toStdString() == _group;
    });
    if (!has_properties_in_group)
    {
      groups_.erase(_group);
    }
    Q_EMIT signalModelChanged();
    return true;
  }
  return false;
}

bool PropertyModel::unregisterGroup(const std::string& _group)
{
  // QWriteLocker locker(&lock_);
  bool erased = false;
  for (auto it = properties_.begin(); it != properties_.end();)
  {
    QString keyStr    = QString::fromStdString(it->first);
    QStringList parts = keyStr.split('.');
    if (parts.size() == 2 && parts[0].toStdString() == _group)
    {
      it     = properties_.erase(it);
      erased = true;
    }
    else
    {
      ++it;
    }
  }
  groups_.erase(_group);

  if (erased)
  {
    emit signalModelChanged();
  }
  return erased;
}

const PropertyModel::Groups& PropertyModel::groups() const { return groups_; }

const PropertyModel::Properties& PropertyModel::properties() const { return properties_; }

std::string PropertyModel::getPropertyDescription(const std::string& _group, const std::string& _key) const
{
  // QReadLocker locker(&lock_);
  std::string full_key = (_group + "." + _key);
  auto iter            = property_descriptions_.find(full_key);
  if (iter != property_descriptions_.end())
  {
    return iter->second;
  }
  return "";
}

void PropertyModel::setPropertyDescription(const std::string& _group,
                                           const std::string& _key,
                                           const std::string& _description)
{
  // QWriteLocker locker(&lock_);
  std::string full_key             = (_group + "." + _key);
  property_descriptions_[full_key] = _description;
}

void PropertyModel::handleUIValueChanged(const std::string& _group, const std::string& _key, const QVariant& _value)
{
  // QWriteLocker locker(&lock_);
  std::string full_key = (_group + "." + _key);
  auto iter            = properties_.find(full_key);
  if (iter != properties_.end())
  {
    // 验证值是否有效
    bool is_valid = true;
    QString error_message;

    // 根据属性类型进行验证
    if (auto* int_prop = dynamic_cast<IntItem*>(iter->second.get()))
    {
      int value = _value.toInt();
      if (value < int_prop->min_ || value > int_prop->max_)
      {
        is_valid      = false;
        error_message = QString("整数值必须在 %1 到 %2 之间").arg(int_prop->min_).arg(int_prop->max_);
      }
    }
    else if (auto* double_prop = dynamic_cast<DoubleItem*>(iter->second.get()))
    {
      double value = _value.toDouble();
      if (value < double_prop->min_ || value > double_prop->max_)
      {
        is_valid      = false;
        error_message = QString("浮点数值必须在 %1 到 %2 之间").arg(double_prop->min_).arg(double_prop->max_);
      }
    }
    else if (auto* string_prop = dynamic_cast<StringItem*>(iter->second.get()))
    {
      QString value = _value.toString();
      // 对于某些特殊组，可以添加额外的验证
      if (_group == "唯一名称" && _key == "name")
      {
        // 名称不能为空
        if (value.isEmpty())
        {
          is_valid      = false;
          error_message = "名称不能为空";
        }
        // 名称不能包含特殊字符
        else if (value.contains(QRegExp(R"([\\/:\*\?"<>\|])")))
        {
          is_valid      = false;
          error_message = "名称不能包含特殊字符: \\ / : * ? \" < > |";
        }
      }
    }

    if (!is_valid)
    {
      LOG_ERROR("属性值验证失败: {}", error_message);
      return;
    }

    // 更新属性值并发出信号
    iter->second->setValue(_value);
    Q_EMIT signalValueChanged(_group, _key, _value);
  }
}

void IntItem::itemConnect()
{
  if (widget_ != nullptr)
  {
    if (auto* spin_box = widget_->findChild<QSpinBox*>())
    {
      QObject::connect(spin_box, QOverload<int>::of(&QSpinBox::valueChanged), this,
                       [this](int _value) { Q_EMIT signalValueChanged(QVariant(_value)); });
    }
  }
}

void DoubleItem::itemConnect()
{
  if (widget_ != nullptr)
  {
    if (auto* spin_box = widget_->findChild<QDoubleSpinBox*>())
    {
      QObject::connect(spin_box, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this,
                       [this](double _value) { Q_EMIT signalValueChanged(QVariant(_value)); });
    }
  }
}

void StringItem::itemConnect()
{
  if (widget_ == nullptr)
  {
    return;
  }

  if (height_factor_ > 1)
  {
    // 多行模式：使用QPlainTextEdit
    auto* text_edit = widget_->findChild<QCodeEditor*>();
    if (text_edit == nullptr)
    {
      return;
    }

    // 使用textChanged信号，当文本内容变化时触发
    QObject::connect(text_edit, &QCodeEditor::textChanged, this, [this, text_edit]() {
      // 发送文本变化信号
      Q_EMIT signalValueChanged(QVariant(text_edit->toPlainText()));
    });
  }
  else
  {
    // 单行模式：使用QLineEdit
    auto* line_edit = widget_->findChild<QLineEdit*>();
    if (line_edit == nullptr)
    {
      return;
    }

    // 使用editingFinished信号，只有当用户完成编辑时才触发
    QObject::connect(line_edit, &QLineEdit::editingFinished, this,
                     [this, line_edit]() { Q_EMIT signalValueChanged(QVariant(line_edit->text())); });
  }
}

void BoolItem::itemConnect()
{
  if (widget_ != nullptr)
  {
    if (auto* check_box = widget_->findChild<QCheckBox*>())
    {
      QObject::connect(check_box, &QCheckBox::stateChanged, this,
                       [this](int _state) { Q_EMIT signalValueChanged(QVariant(_state != Qt::Unchecked)); });
    }
  }
}

void EnumItem::itemConnect()
{
  if (widget_ != nullptr)
  {
    if (auto* combo_box = widget_->findChild<QComboBox*>())
    {
      QObject::connect(combo_box, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
                       [this](int _index) { Q_EMIT signalValueChanged(QVariant(_index)); });
    }
  }
}

// FilePathPropertyItem 实现
FilePathPropertyItem::FilePathPropertyItem(const std::string& _name,
                                           const std::string& _default_value,
                                           const std::string& _filter,
                                           QTreeWidgetItem* _parent) :
  PropertyItem(_name, _parent),
  default_value_(QString::fromStdString(_default_value)),
  filter_(QString::fromStdString(_filter))
{}

QWidget* FilePathPropertyItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  auto* line_edit = new QLineEdit();
  line_edit->setText(default_value_);
  line_edit->setReadOnly(true);

  auto* browse_button = new QToolButton();
  browse_button->setText("...");
  browse_button->setToolTip("选择文件");

  QObject::connect(browse_button, &QToolButton::clicked, [this, line_edit]() {
    QString file_path = QFileDialog::getOpenFileName(
      line_edit->parentWidget(), "选择文件",
      line_edit->text().isEmpty() ? QDir::homePath() : QFileInfo(line_edit->text()).absolutePath(), filter_);

    if (file_path.isEmpty())
    {
      return;
    }

    line_edit->setText(file_path);
    Q_EMIT signalValueChanged(file_path);
  });

  layout->addWidget(line_edit, 1);
  layout->addWidget(browse_button);
  container->setLayout(layout);
  widget_ = container;
  return container;
}

void FilePathPropertyItem::setValue(const QVariant& _value)
{
  if (widget_ == nullptr)
  {
    return;
  }

  auto* line_edit = widget_->findChild<QLineEdit*>();
  if (line_edit == nullptr)
  {
    return;
  }

  QString new_text = _value.toString();
  if (line_edit->text() != new_text)
  {
    line_edit->setText(new_text);
  }
}

void FilePathPropertyItem::forceSetValue(const QVariant& _value)
{
  // FilePathPropertyItem 不需要检查焦点状态，直接调用 setValue
  setValue(_value);
}

QVariant FilePathPropertyItem::value() const
{
  if (widget_ == nullptr)
  {
    return default_value_;
  }

  auto* line_edit = widget_->findChild<QLineEdit*>();
  if (line_edit == nullptr)
  {
    return default_value_;
  }

  return line_edit->text();
}

void FilePathPropertyItem::itemConnect()
{
  // 文件路径属性项的信号连接已在createEditor中实现
}

// DirectoryPathPropertyItem 实现
DirectoryPathPropertyItem::DirectoryPathPropertyItem(const std::string& _name,
                                                     const std::string& _default_value,
                                                     QTreeWidgetItem* _parent) :
  PropertyItem(_name, _parent), default_value_(QString::fromStdString(_default_value))
{}

QWidget* DirectoryPathPropertyItem::createEditor()
{
  auto* container = new QWidget();
  auto* layout    = new QHBoxLayout(container);
  layout->setContentsMargins(0, 0, 0, 0);

  auto* line_edit = new QLineEdit();
  line_edit->setText(default_value_);
  line_edit->setReadOnly(true);

  auto* browse_button = new QToolButton();
  browse_button->setText("...");
  browse_button->setToolTip("选择文件夹");

  QObject::connect(browse_button, &QToolButton::clicked, [this, line_edit]() {
    QString dir_path = QFileDialog::getExistingDirectory(
      line_edit->parentWidget(), "选择文件夹", line_edit->text().isEmpty() ? QDir::homePath() : line_edit->text(),
      QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);

    if (dir_path.isEmpty())
    {
      return;
    }

    line_edit->setText(dir_path);
    Q_EMIT signalValueChanged(dir_path);
  });

  layout->addWidget(line_edit, 1);
  layout->addWidget(browse_button);
  container->setLayout(layout);
  widget_ = container;
  return container;
}

void DirectoryPathPropertyItem::setValue(const QVariant& _value)
{
  if (widget_ == nullptr)
  {
    return;
  }

  auto* line_edit = widget_->findChild<QLineEdit*>();
  if (line_edit == nullptr)
  {
    return;
  }

  QString new_text = _value.toString();
  if (line_edit->text() != new_text)
  {
    line_edit->setText(new_text);
  }
}

void DirectoryPathPropertyItem::forceSetValue(const QVariant& _value)
{
  // DirectoryPathPropertyItem 不需要检查焦点状态，直接调用 setValue
  setValue(_value);
}

QVariant DirectoryPathPropertyItem::value() const
{
  if (widget_ == nullptr)
  {
    return default_value_;
  }

  auto* line_edit = widget_->findChild<QLineEdit*>();
  if (line_edit == nullptr)
  {
    return default_value_;
  }

  return line_edit->text();
}

void DirectoryPathPropertyItem::itemConnect()
{
  // 文件夹路径属性项的信号连接已在createEditor中实现
}

}  // namespace robosense::lidar
