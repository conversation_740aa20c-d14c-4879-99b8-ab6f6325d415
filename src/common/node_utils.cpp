﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "node_utils.h"
#include "QtNodes/internal/Definitions.hpp"
#include "node_models/bt_node_model.h"
#include <QDomDocument>
#include <QMessageBox>
#include <QtNodes/DataFlowGraphModel>
#include <QtNodes/NodeDelegateModel>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

Expected<QtNodes::NodeId, std::string> Utils::findRoot(QtNodes::DataFlowGraphModel* _graph_model)
{
  QtNodes::NodeId root_candidate_opt = QtNodes::InvalidNodeId;
  int root_count                     = 0;

  auto all_node_ids_in_model = _graph_model->allNodeIds();  // 获取所有节点 ID
  if (all_node_ids_in_model.empty())
  {
    return makeError("findRoot Error: No nodes found in the model.");
  }

  for (QtNodes::NodeId nodeId : all_node_ids_in_model)
  {
    // 使用 model 获取 NodeDataModel
    const auto* node_model = _graph_model->delegateModel<QtNodes::NodeDelegateModel>(nodeId);

    if (nullptr != node_model)
    {
      // 检查是否有输入连接 (使用模型接口)
      bool has_input_connection    = false;
      unsigned int num_input_ports = node_model->nPorts(QtNodes::PortType::In);

      for (unsigned int i = 0; i < num_input_ports; ++i)
      {
        // 获取连接到此输入端口的 ConnectionId 列表
        auto connections = _graph_model->connections(nodeId, QtNodes::PortType::In, i);
        if (!connections.empty())
        {
          has_input_connection = true;
          break;
        }
      }

      if (!has_input_connection)
      {
        if (root_count == 0)
        {
          root_candidate_opt = nodeId;
        }
        root_count++;
      }
      else
      {
        LOG_DEBUG("Node {} is Root type but has incoming connections.", nodeId);
      }
    }
  }

  if (root_count == 0)
  {
    return makeError("findRoot Error: No Root node found.");
  }
  if (root_count > 1)
  {
    return makeError(fmt::format("findRoot Error: Multiple Root nodes found ({}).", root_count));
  }

  return root_candidate_opt;
}

std::vector<QtNodes::NodeId> Utils::getChildren(QtNodes::DataFlowGraphModel* _graph_model,
                                                const QtNodes::NodeId& _parent_node_id,
                                                bool _ordered)
{
  std::vector<QtNodes::NodeId> children_ids;

  auto* parent_model = _graph_model->delegateModel<BtNodeModel>(_parent_node_id);
  if (parent_model == nullptr)
  {
    LOG_ERROR("Parent node model is nullptr");
    return children_ids;
  }

  constexpr QtNodes::PortIndex PARENT_OUTPUT_PORT_INDEX = 0;
  if (parent_model->nPorts(QtNodes::PortType::Out) <= PARENT_OUTPUT_PORT_INDEX)
  {
    LOG_TRACE("parent node has no output ports");
    return children_ids;
  }

  // 获取子节点
  unsigned int num_output_ports = parent_model->nPorts(QtNodes::PortType::Out);
  LOG_WARN("num_output_ports : {}, registration_id : {}, nodeType : {}, name : {}", num_output_ports,
           parent_model->model() ? parent_model->model()->registration_id : QString("unknown"),
           magic_enum::enum_name(parent_model->nodeType()), parent_model->name());
  if (num_output_ports <= PARENT_OUTPUT_PORT_INDEX)
  {
    return children_ids;
  }

  for (unsigned int i = 0; i < num_output_ports; ++i)
  {
    // 获取从这个输出端口出发的所有 ConnectionId
    auto out_connections = _graph_model->connections(_parent_node_id, QtNodes::PortType::Out, i);

    for (const auto& conn_id : out_connections)
    {
      auto input_node_id = conn_id.inNodeId;
      children_ids.emplace_back(input_node_id);
    }
  }

  // Sort children based on visual position if requested
  if (_ordered && children_ids.size() > 1)
  {
    auto layout = PortLayout::Horizontal;

    if (PortLayout::Horizontal != layout)
    {  // Sort by X position for vertical layout
      std::sort(children_ids.begin(), children_ids.end(),
                [&](const QtNodes::NodeId& _id_a, const QtNodes::NodeId& _id_b) {
                  // Get node geometry using NodeRole::Position and NodeRole::Size
                  auto pos_a  = _graph_model->nodeData(_id_a, QtNodes::NodeRole::Position).toPointF();
                  auto size_a = _graph_model->nodeData(_id_a, QtNodes::NodeRole::Size).toSizeF();
                  auto pos_b  = _graph_model->nodeData(_id_b, QtNodes::NodeRole::Position).toPointF();
                  auto size_b = _graph_model->nodeData(_id_b, QtNodes::NodeRole::Size).toSizeF();

                  // Calculate center X
                  double center_x_a = pos_a.x() + (size_a.width() * 0.5);
                  double center_x_b = pos_b.x() + (size_b.width() * 0.5);
                  return center_x_a < center_x_b;
                });
    }
    else
    {  // Sort by Y position for horizontal layout (default assumption)
      std::sort(children_ids.begin(), children_ids.end(),
                [&](const QtNodes::NodeId& _id_a, const QtNodes::NodeId& _id_b) {
                  auto pos_a  = _graph_model->nodeData(_id_a, QtNodes::NodeRole::Position).toPointF();
                  auto size_a = _graph_model->nodeData(_id_a, QtNodes::NodeRole::Size).toSizeF();
                  auto pos_b  = _graph_model->nodeData(_id_b, QtNodes::NodeRole::Position).toPointF();
                  auto size_b = _graph_model->nodeData(_id_b, QtNodes::NodeRole::Size).toSizeF();

                  // Calculate center Y
                  double center_y_a = pos_a.y() + (size_a.height() * 0.5);
                  double center_y_b = pos_b.y() + (size_b.height() * 0.5);
                  return center_y_a < center_y_b;
                });
    }
  }

  return children_ids;
}

AbsBehaviorTree Utils::buildTreeFromScene(QtNodes::DataFlowGraphModel* _graph_model, QtNodes::NodeId _root_id)
{
  if (!_graph_model->nodeExists(_root_id))
  {
    auto res = Utils::findRoot(_graph_model);
    if (!res.has_value())
    {
      LOG_ERROR(res.error());
      return {};
    }

    _root_id = res.value();
  }

  if (!_graph_model->nodeExists(_root_id))
  {
    if (!_graph_model->allNodeIds().empty())
    {
      LOG_ERROR("Error: cannot build tree unless there's a single root node");
    }
    return {};
  }

  AbsBehaviorTree tree;

  std::function<void(AbstractTreeNode*, QtNodes::NodeId)> pushRecursively;

  pushRecursively = [&](AbstractTreeNode* parent, QtNodes::NodeId node_id) {
    AbstractTreeNode abs_node;

    auto* data_model = _graph_model->delegateModel<BtNodeModel>(node_id);
    if (!data_model)
    {
      LOG_ERROR("Invalid BtNodeModel at node id: {}", static_cast<int>(node_id));
      return;
    }

    // 获取NodeModel的共享指针
    abs_node.model         = data_model->model();
    abs_node.instance_name = data_model->instanceName();
    abs_node.graphic_node  = node_id;
    abs_node.ports_mapping = data_model->getCurrentPortMapping();

    // pos/size 是 UI 属性，GraphModel 中没有，可跳过或默认值
    abs_node.pos  = QPointF(0, 0);
    abs_node.size = QSizeF(100, 50);  // 可选默认值，或通过额外接口提供

    AbstractTreeNode* added_node = tree.addNode(parent, std::move(abs_node));

    auto children = Utils::getChildren(_graph_model, node_id, true);

    for (QtNodes::NodeId child_id : children)
    {
      pushRecursively(added_node, child_id);
    }
  };

  pushRecursively(nullptr, _root_id);

  return tree;
}

AbsBehaviorTree Utils::buildTreeFromXML(const QDomElement& _bt_root, const NodeModels& _models)
{
  AbsBehaviorTree tree;

  if (_bt_root.tagName() != "BehaviorTree")
  {
    throw std::runtime_error("Expecting a node called <BehaviorTree>");
  }

  //-------------------------------------
  std::function<void(AbstractTreeNode * parent, QDomElement)> recursiveStep;
  recursiveStep = [&](AbstractTreeNode* parent, const QDomElement& xml_node) {
    // The nodes with a ID used that QString to insert into the registry()
    QString modelID = xml_node.tagName();
    if (xml_node.hasAttribute("ID"))
    {
      modelID = xml_node.attribute("ID");
    }

    AbstractTreeNode tree_node;

    std::string model_id_str = modelID.toStdString();
    auto model_it            = _models.find(model_id_str);
    if (model_it == _models.end())
    {
      throw std::runtime_error((QString("This model has not been registered: ") + modelID).toStdString());
    }
    // 直接使用共享指针
    tree_node.model = model_it->second;

    if (xml_node.hasAttribute("name"))
    {
      tree_node.instance_name = (xml_node.attribute("name"));
    }
    else
    {
      tree_node.instance_name = modelID;
    }

    auto attributes = xml_node.attributes();
    for (int attr = 0; attr < attributes.size(); attr++)
    {
      auto attribute = attributes.item(attr).toAttr();
      if (attribute.name() != "ID" && attribute.name() != "name")
      {
        tree_node.ports_mapping.insert({ attribute.name(), attribute.value() });
      }
    }

    auto* added_node = tree.addNode(parent, std::move(tree_node));

    for (QDomElement child = xml_node.firstChildElement(); !child.isNull(); child = child.nextSiblingElement())
    {
      recursiveStep(added_node, child);
    }
  };

  auto first_child = _bt_root.firstChildElement();
  if (first_child.tagName() == "Root")
  {
    QMessageBox::question(nullptr, "Fix your file!", "Please remove the node <Root> from your <BehaviorTree>",
                          QMessageBox::Ok);
    first_child = first_child.firstChildElement();
  }

  // start recursion
  recursiveStep(nullptr, first_child);

  return tree;
}

namespace
{
void recursiveNodeReorder(AbsBehaviorTree& _tree, PortLayout _layout)
{
  const qreal LEVEL_SPACING = 80;
  const qreal NODE_SPACING  = 40;

  std::vector<QPointF> layer_cursor;
  std::vector<std::vector<AbstractTreeNode*> > nodes_by_level(1);

  auto calculateRecommendedPos = [&](AbstractTreeNode* node, unsigned /*current_layer*/) -> qreal {
    qreal recommended_pos = NODE_SPACING * 0.5;
    if (_layout == PortLayout::Vertical)
    {
      recommended_pos += (node->pos.x() + node->size.width() * 0.5);
      for (int index : node->children_index)
      {
        const auto& child_node = _tree.nodes()[index];
        recommended_pos -= (child_node.size.width() + NODE_SPACING) * 0.5;
      }
    }
    else
    {
      recommended_pos += node->pos.y() + node->size.height() * 0.5;
      for (int index : node->children_index)
      {
        const auto& child_node = _tree.nodes()[index];
        recommended_pos -= (child_node.size.height() + NODE_SPACING) * 0.5;
      }
    }
    return recommended_pos;
  };

  auto adjustLayerCursor = [&](unsigned current_layer, qreal recommended_pos) {
    if (_layout == PortLayout::Vertical)
    {
      if (current_layer >= layer_cursor.size())
      {
        QPointF new_cursor(recommended_pos, layer_cursor[current_layer - 1].y() + LEVEL_SPACING);
        layer_cursor.emplace_back(new_cursor);
        nodes_by_level.emplace_back();
      }
      else
      {
        recommended_pos = std::max(recommended_pos, layer_cursor[current_layer].x());
        layer_cursor[current_layer].setX(recommended_pos);
      }
    }
    else
    {
      if (current_layer >= layer_cursor.size())
      {
        QPointF new_cursor(layer_cursor[current_layer - 1].x() + LEVEL_SPACING, recommended_pos);
        layer_cursor.emplace_back(new_cursor);
        nodes_by_level.emplace_back();
      }
      else
      {
        recommended_pos = std::max(recommended_pos, layer_cursor[current_layer].y());
        layer_cursor[current_layer].setY(recommended_pos);
      }
    }
  };

  std::function<void(unsigned, AbstractTreeNode*)> recursiveStep = [&](unsigned current_layer, AbstractTreeNode* node) {
    node->pos = layer_cursor[current_layer];
    nodes_by_level[current_layer].emplace_back(node);

    if (node->children_index.empty())
    {
      return;
    }

    qreal recommended_pos = calculateRecommendedPos(node, current_layer);
    current_layer++;
    adjustLayerCursor(current_layer, recommended_pos);

    for (int index : node->children_index)
    {
      AbstractTreeNode* child_node = _tree.node(index);
      recursiveStep(current_layer, child_node);
      if (_layout == PortLayout::Vertical)
      {
        layer_cursor[current_layer] += QPointF(child_node->size.width() + NODE_SPACING, 0);
      }
      else
      {
        layer_cursor[current_layer] += QPointF(0, child_node->size.height() + NODE_SPACING);
      }
    }
  };

  auto* root_node = _tree.rootNode();
  layer_cursor.emplace_back(-root_node->size.width() * 0.5, -root_node->size.height() * 0.5);
  recursiveStep(0, root_node);

  auto adjustNodePositions = [&](unsigned level, qreal& offset) {
    for (auto* node : nodes_by_level[level])
    {
      if (_layout == PortLayout::Vertical)
      {
        node->pos.setY(offset);
      }
      else
      {
        node->pos.setX(offset);
      }
    }
  };

  if (_layout == PortLayout::Vertical)
  {
    qreal offset = root_node->size.height() + LEVEL_SPACING;
    for (unsigned i = 1; i < nodes_by_level.size(); i++)
    {
      qreal max_height = 0;
      for (auto* node : nodes_by_level[i])
      {
        max_height = std::max(max_height, node->size.height());
      }
      adjustNodePositions(i, offset);
      offset += max_height + LEVEL_SPACING;
    }
  }
  else
  {
    qreal offset = root_node->size.width() + LEVEL_SPACING;
    for (unsigned i = 1; i < nodes_by_level.size(); i++)
    {
      qreal max_width = 0;
      for (auto* node : nodes_by_level[i])
      {
        max_width = std::max(max_width, node->size.width());
      }
      adjustNodePositions(i, offset);
      offset += max_width + LEVEL_SPACING;
    }
  }
}
}  // namespace

void Utils::nodeReorder(QtNodes::DataFlowGraphModel* _graph_model, AbsBehaviorTree& _tree)
{
  if (_graph_model == nullptr)
  {
    throw std::runtime_error("Graph model is null");
  }

  for (const auto& abs_node : _tree.nodes())
  {
    if (!_graph_model->nodeExists(abs_node.graphic_node))
    {
      throw std::runtime_error("One or more nodes haven't been created yet");
    }
  }

  if (_tree.nodesCount() == 0)
  {
    return;
  }

  recursiveNodeReorder(_tree, PortLayout::Horizontal);  // TODO: 这里暂时使用PortLayout::Horizontal

  for (const auto& abs_node : _tree.nodes())
  {
    const auto& node_id  = abs_node.graphic_node;
    QVariant pos_variant = QVariant::fromValue(abs_node.pos);
    _graph_model->setNodeData(node_id, QtNodes::NodeRole::Position, pos_variant);
  }
}

// std::pair<QtNodes::NodeStyle, QtNodes::ConnectionStyle> getStyleFromStatus(BT::NodeStatus status,
//                                                                            BT::NodeStatus prev_status)
// {
//   QtNodes::NodeStyle node_style;
//   QtNodes::ConnectionStyle conn_style;

//   float penWidth = 3.0;

//   conn_style.HoveredColor = Qt::transparent;

//   //printf("status=%d, old=%d\n", status, prev_status);

//   if (status == BT::NodeStatus::IDLE)
//   {
//     if (prev_status != BT::NodeStatus::IDLE)
//     {
//       node_style.PenWidth *= penWidth;
//       node_style.HoveredPenWidth = node_style.PenWidth;

//       if (prev_status == BT::NodeStatus::SUCCESS)
//       {
//         node_style.NormalBoundaryColor = node_style.ShadowColor = QColor(100, 150, 100);
//         conn_style.NormalColor                                  = node_style.NormalBoundaryColor;
//       }
//       else if (prev_status == BT::NodeStatus::RUNNING)
//       {
//         node_style.NormalBoundaryColor = node_style.ShadowColor = QColor(150, 130, 40);
//         conn_style.NormalColor                                  = node_style.NormalBoundaryColor;
//       }
//       else if (prev_status == BT::NodeStatus::FAILURE)
//       {
//         node_style.NormalBoundaryColor = node_style.ShadowColor = QColor(150, 80, 80);
//         conn_style.NormalColor                                  = node_style.NormalBoundaryColor;
//       }
//     }

//     return { node_style, conn_style };
//   }

//   node_style.PenWidth *= penWidth;
//   node_style.HoveredPenWidth = node_style.PenWidth;

//   if (status == BT::NodeStatus::SUCCESS)
//   {
//     node_style.NormalBoundaryColor = node_style.ShadowColor = QColor(51, 200, 51);
//     node_style.ShadowColor                                  = QColor(51, 250, 51);
//     conn_style.NormalColor                                  = node_style.NormalBoundaryColor;
//   }
//   else if (status == BT::NodeStatus::RUNNING)
//   {
//     node_style.NormalBoundaryColor = node_style.ShadowColor = QColor(220, 140, 20);
//     conn_style.NormalColor                                  = node_style.NormalBoundaryColor;
//   }
//   else if (status == BT::NodeStatus::FAILURE)
//   {
//     node_style.NormalBoundaryColor = node_style.ShadowColor = QColor(250, 50, 50);
//     conn_style.NormalColor                                  = node_style.NormalBoundaryColor;
//   }

//   return { node_style, conn_style };
// }

std::set<QString> Utils::getModelsToRemove(QWidget* _parent, NodeModels& _prev_models, const NodeModels& _new_models)
{
  std::set<QString> prev_custom_models;

  if (_prev_models.size() > BuiltinNodeModels().size())
  {
    for (const auto& p_model : _prev_models)
    {
      const std::string& model_name_str = p_model.first;
      QString model_name                = QString::fromStdString(model_name_str);
      if (BuiltinNodeModels().count(model_name_str) == 0 && _new_models.count(model_name_str) == 0)
      {
        prev_custom_models.insert(model_name);
      }
    }
  }

  if (prev_custom_models.size() > 0)
  {
    int ret =
      QMessageBox::question(_parent, "Clear Palette?", "Do you want to remove the previously loaded custom nodes?",
                            static_cast<QMessageBox::StandardButtons>(QMessageBox::No | QMessageBox::Yes));
    if (ret == QMessageBox::No)
    {
      prev_custom_models.clear();
    }
  }
  return prev_custom_models;
}

void Utils::showWidget(QWidget* _widget)
{
  if (nullptr == _widget)
  {
    return;
  }

  if (_widget->isVisible())
  {
    _widget->raise();
    _widget->activateWindow();
  }
  else
  {
    _widget->show();
  }
}

void Utils::closeWidget(QWidget* _widget, bool _is_delete)
{
  if (nullptr == _widget)
  {
    return;
  }

  if (_widget->isVisible())
  {
    _widget->close();
  }

  if (_is_delete)
  {
    delete _widget;
  }
}

}  // namespace robosense::lidar
