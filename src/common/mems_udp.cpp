﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mems_udp.h"
#include <boost/asio.hpp>
#include <boost/bind.hpp>
#include <iostream>
#include <rsfsc_log/rsfsc_log_macro.h>

// NOTE learn from:
// https://www.boost.org/doc/libs/1_52_0/doc/html/boost_asio/example/timeouts/blocking_udp_client.cpp

namespace robosense
{
namespace lidar
{

MEMSUDP::MEMSUDP(std::size_t _len) : pkt_length_(_len)
{
  ptr_io_context_     = std::make_unique<boost::asio::io_context>();
  ptr_deadline_timer_ = std::make_unique<boost::asio::deadline_timer>(*ptr_io_context_);
  RSFSCLog::getInstance()->info(msg_header_ + "MEMSUDP -> construct with len: " + std::to_string(_len));
}

MEMSUDP::MEMSUDP(std::size_t _len, std::size_t _timeout_ms) : pkt_length_(_len), timeout_count_ms_(_timeout_ms)
{
  ptr_io_context_     = std::make_unique<boost::asio::io_context>();
  ptr_deadline_timer_ = std::make_unique<boost::asio::deadline_timer>(*ptr_io_context_);
  RSFSCLog::getInstance()->info(msg_header_ + "MEMSUDP -> construct with len: " + std::to_string(_len) +
                                ", timeout: " + std::to_string(_timeout_ms) + "ms");
}

MEMSUDP::~MEMSUDP()
{
  stop();
  if (nullptr != ptr_socket_)
  {
    ptr_socket_.reset();
  }
  if (nullptr != ptr_thread_)
  {
    ptr_thread_.reset();
  }
  if (nullptr != ptr_deadline_timer_)
  {
    ptr_deadline_timer_.reset();
  }
  if (nullptr != ptr_io_context_)
  {
    ptr_io_context_.reset();
  }
}

bool MEMSUDP::start(const std::pair<std::string, uint16_t>& _network_info, const std::string& _group_ip)
{
  return start(_network_info.first, _network_info.second, _group_ip);
}

bool MEMSUDP::start(const std::string& _ip, const uint16_t _port, const std::string& _group_ip)
{
  ip_         = _ip;
  port_       = _port;
  timeout_    = false;
  msg_header_ = "MEMSUDP" + std::to_string(_port) + "::";
  // NOTE start timer before start thread, otherwise timer in thread will be reset by this
  ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
  checkDeadline();  // Start the persistent actor that checks for deadline expiry.
  try
  {
    ptr_socket_ = std::make_unique<boost::asio::ip::udp::socket>(
      *ptr_io_context_, boost::asio::ip::udp::endpoint(boost::asio::ip::udp::v4(), port_));
    if (!_group_ip.empty())
    {
      //参数二为指定interface ip, 也就是本地网卡地址
      //https://stackoverflow.com/questions/8675623/boostasioipmulticastjoin-group-does-not-work
      ptr_socket_->set_option(boost::asio::ip::multicast::join_group(boost::asio::ip::make_address(_group_ip).to_v4(),
                                                                     boost::asio::ip::make_address(_ip).to_v4()));
    }
    flag_thread_run_.store(true);
    ptr_thread_ = std::make_unique<std::thread>([this]() { dataProcess(); });
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error(msg_header_ + std::string("start -> start udp failed: ") + e.what());
    return false;
  }
  RSFSCLog::getInstance()->info(msg_header_ + "start -> ip: " + ip_ + " port_: " + std::to_string(port_));
  return true;
}

bool MEMSUDP::stop()
{
  try
  {
    if (flag_thread_run_.load())
    {
      flag_thread_run_.store(false);
      // NOTE must stop timer before close
      ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
      ptr_socket_->close();
    }
    if (ptr_thread_ != nullptr && ptr_thread_->joinable())
    {
      RSFSCLog::getInstance()->info(msg_header_ + "stop -> udp thread start joining");
      ptr_thread_->join();
    }
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error(msg_header_ + std::string("stop -> stop udp failed: ") + e.what());
    return false;
  }
  RSFSCLog::getInstance()->info(msg_header_ + "stop");
  return true;
}

void MEMSUDP::regRecvCallback(const std::function<void(const char*)>& _callback)
{
  vec_recv_cb_.emplace_back(_callback);
}

void MEMSUDP::regTimeoutCallback(const std::function<void()>& _callback) { vec_timeout_cb_.emplace_back(_callback); }

static void handleReceive(const boost::system::error_code& _ec,
                          std::size_t _length,
                          boost::system::error_code* _out_ec,
                          std::size_t* _out_length)
{
  *_out_ec     = _ec;
  *_out_length = _length;
}

void MEMSUDP::dataProcess()
{
  ptr_socket_->cancel();
  std::vector<char> recv_buffer;
  std::size_t current_expected_length = pkt_length_;

  while (flag_thread_run_.load())
  {
    // 更新缓冲区大小（如果需要）
    updateBufferIfNeeded(recv_buffer, current_expected_length);

    // 执行异步接收
    auto receive_result = performAsyncReceive(recv_buffer);
    if (!receive_result.success_)
    {
      if (handleReceiveError(receive_result.error_code_))
      {
        break;  // 需要退出循环
      }
      continue;  // 继续下一次接收
    }

    // 验证接收长度
    if (!validateReceivedLength(receive_result.received_length_, current_expected_length))
    {
      continue;  // 长度不匹配，继续下一次接收
    }

    // 处理成功接收的数据
    handleSuccessfulReceive(recv_buffer);
  }
}

void MEMSUDP::checkDeadline()
{
  if (ptr_deadline_timer_->expires_at() <= boost::asio::deadline_timer::traits_type::now())
  {
    ptr_socket_->cancel();
    timeout_ = true;

    // 调用超时回调
    for (auto& timeout_callback : vec_timeout_cb_)
    {
      timeout_callback();
    }

    // 检查是否需要自动停止
    if (auto_stop_on_timeout_enabled_.load())
    {
      std::size_t current_count = current_continuous_timeout_count_.fetch_add(1) + 1;
      RSFSCLog::getInstance()->info(msg_header_ + "checkDeadline -> timeout count: " + std::to_string(current_count) +
                                    "/" + std::to_string(max_continuous_timeout_count_));

      if (current_count >= max_continuous_timeout_count_)
      {
        RSFSCLog::getInstance()->warn(msg_header_ + "checkDeadline -> max continuous timeout reached, stopping...");
        flag_thread_run_.store(false);
        return;  // 不再继续设置定时器
      }
    }

    ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
  }

  // Put the actor back to sleep.
  ptr_deadline_timer_->async_wait([this](const boost::system::error_code& _ec) {
    (void)_ec;                    //unused
    if (flag_thread_run_.load())  // 只有在还在运行时才继续
    {
      checkDeadline();
    }
  });
}

void MEMSUDP::setTimeoutMS(const std::size_t _timeout_count_ms) { timeout_count_ms_ = _timeout_count_ms; }

void MEMSUDP::setPacketLength(const std::size_t _len)
{
  pkt_length_ = _len;
  RSFSCLog::getInstance()->info(msg_header_ + "setPacketLength -> new length: " + std::to_string(_len));

  // 如果当前正在运行，需要重新调整接收缓冲区
  if (flag_thread_run_.load())
  {
    RSFSCLog::getInstance()->info(msg_header_ + "setPacketLength -> adjusting buffer size during runtime");
  }
}

std::size_t MEMSUDP::getPacketLength() const { return pkt_length_; }

std::size_t MEMSUDP::getTimeoutMS() const { return timeout_count_ms_; }

void MEMSUDP::enableAutoStopOnTimeout(std::size_t _max_timeout_count)
{
  max_continuous_timeout_count_ = _max_timeout_count;
  auto_stop_on_timeout_enabled_.store(true);
  current_continuous_timeout_count_.store(0);
  RSFSCLog::getInstance()->info(msg_header_ +
                                "enableAutoStopOnTimeout -> max count: " + std::to_string(_max_timeout_count));
}

void MEMSUDP::disableAutoStopOnTimeout()
{
  auto_stop_on_timeout_enabled_.store(false);
  current_continuous_timeout_count_.store(0);
  RSFSCLog::getInstance()->info(msg_header_ + "disableAutoStopOnTimeout -> auto stop disabled");
}

bool MEMSUDP::isAutoStopOnTimeoutEnabled() const { return auto_stop_on_timeout_enabled_.load(); }

bool MEMSUDP::receiveWithTimeout(std::vector<char>& _buffer, std::size_t _expected_len, std::size_t _timeout_ms)
{
  if (!ptr_socket_ || !flag_thread_run_.load())
  {
    RSFSCLog::getInstance()->error(msg_header_ + "receiveWithTimeout -> socket not initialized or not running");
    return false;
  }

  try
  {
    _buffer.resize(_expected_len);
    std::size_t received_length          = 0;
    boost::system::error_code error_code = boost::asio::error::would_block;

    // 设置超时时间
    ptr_deadline_timer_->expires_from_now(boost::posix_time::milliseconds(_timeout_ms));

    // 异步接收数据
    ptr_socket_->async_receive(
      boost::asio::buffer(_buffer),
      [&error_code, &received_length](const boost::system::error_code& _ec, std::size_t _length) {
        error_code      = _ec;
        received_length = _length;
      });

    // 等待操作完成或超时
    while (error_code == boost::asio::error::would_block)
    {
      ptr_io_context_->run_one();
    }

    if (error_code)
    {
      RSFSCLog::getInstance()->warn(msg_header_ + "receiveWithTimeout -> error: " + error_code.message());
      return false;
    }

    if (received_length != _expected_len)
    {
      RSFSCLog::getInstance()->warn(msg_header_ + "receiveWithTimeout -> length mismatch, expected: " +
                                    std::to_string(_expected_len) + ", received: " + std::to_string(received_length));
      _buffer.resize(received_length);
    }

    RSFSCLog::getInstance()->debug(msg_header_ + "receiveWithTimeout -> successfully received " +
                                   std::to_string(received_length) + " bytes");
    return true;
  }
  catch (const std::exception& exception)
  {
    RSFSCLog::getInstance()->error(msg_header_ + "receiveWithTimeout -> exception: " + exception.what());
    return false;
  }
}

// 辅助方法实现
bool MEMSUDP::updateBufferIfNeeded(std::vector<char>& _buffer, std::size_t& _current_length)
{
  // 检查包长度是否发生变化
  if (_current_length != pkt_length_)
  {
    _current_length = pkt_length_;
    _buffer.resize(_current_length, 0x0);
    RSFSCLog::getInstance()->info(msg_header_ +
                                  "updateBufferIfNeeded -> buffer resized to: " + std::to_string(_current_length));
    return false;  // 缓冲区已更新，可以继续
  }

  // 如果缓冲区为空，初始化
  if (_buffer.empty())
  {
    _buffer.resize(_current_length, 0x0);
  }

  return false;  // 无需特殊处理
}

MEMSUDP::ReceiveResult MEMSUDP::performAsyncReceive(std::vector<char>& _buffer)
{
  std::size_t received_length          = 0;
  boost::system::error_code error_code = boost::asio::error::would_block;

  // 设置超时时间
  ptr_deadline_timer_->expires_from_now(boost::posix_time::milliseconds(timeout_count_ms_));

  // 异步接收
  ptr_socket_->async_receive(
    boost::asio::buffer(_buffer),
    [&error_code, &received_length](const boost::system::error_code& _ec, std::size_t _length) {
      error_code      = _ec;
      received_length = _length;
    });

  // 等待操作完成
  do
  {
    ptr_io_context_->run_one();
  } while (error_code == boost::asio::error::would_block);

  return ReceiveResult(!error_code, error_code, received_length);
}

bool MEMSUDP::handleReceiveError(const boost::system::error_code& _error_code)
{
  RSFSCLog::getInstance()->warn(msg_header_ + "handleReceiveError -> error: " + _error_code.message());

  // 检查是否是超时错误
  if (_error_code == boost::asio::error::operation_aborted && auto_stop_on_timeout_enabled_.load())
  {
    std::size_t current_count = current_continuous_timeout_count_.fetch_add(1) + 1;
    RSFSCLog::getInstance()->info(msg_header_ + "handleReceiveError -> timeout count: " +
                                  std::to_string(current_count) + "/" + std::to_string(max_continuous_timeout_count_));

    if (current_count >= max_continuous_timeout_count_)
    {
      RSFSCLog::getInstance()->warn(msg_header_ + "handleReceiveError -> max timeout reached, stopping...");
      flag_thread_run_.store(false);
      return true;  // 需要退出循环
    }
  }

  return false;  // 继续循环
}

bool MEMSUDP::validateReceivedLength(std::size_t _received_length, std::size_t _expected_length)
{
  if (_received_length != _expected_length)
  {
    RSFSCLog::getInstance()->debug(
      msg_header_ + "validateReceivedLength -> length mismatch, received: " + std::to_string(_received_length) +
      ", expected: " + std::to_string(_expected_length));
    return false;
  }
  return true;
}

void MEMSUDP::handleSuccessfulReceive(const std::vector<char>& _buffer)
{
  timeout_ = false;

  // 成功接收数据，重置连续超时计数
  if (auto_stop_on_timeout_enabled_.load())
  {
    current_continuous_timeout_count_.store(0);
  }

  // 调用所有注册的回调函数
  for (auto& callback_function : vec_recv_cb_)
  {
    callback_function(_buffer.data());
  }
}

}  // namespace lidar
}  // namespace robosense