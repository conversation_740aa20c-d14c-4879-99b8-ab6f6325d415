﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef PLUGIN_MANAGER_H
#define PLUGIN_MANAGER_H

#include "common_types.h"
#include <QtCore/QDir>
#include <QtCore/QMap>
#include <QtCore/QObject>
#include <QtCore/QPluginLoader>
#include <qdir.h>
#include <unordered_map>

namespace robosense::lidar
{

/**
   * @brief 插件管理器类，负责加载和管理所有插件
   */
class PluginManager : public QObject
{
  Q_OBJECT

public:
  /**
   * @brief 获取插件管理器单例
   * @return 插件管理器实例
   */
  static PluginManager& getInstance();

  ~PluginManager() override;

  PluginManager(const PluginManager&) = delete;
  PluginManager& operator=(const PluginManager&) = delete;
  PluginManager(PluginManager&&)                 = delete;
  PluginManager& operator=(PluginManager&&) = delete;

  /**
   * @brief 加载指定目录下的所有插件
   * @param _plugin_dir 插件目录
   * @return 成功加载的插件数量
   */
  int loadPlugins(const QString& _plugin_dir);

  /**
   * @brief 获取所有已加载的插件
   * @return 插件列表
   */
  [[nodiscard]] QPluginLoader* pluginLoader(const std::string& _name) const;

  NodeInfos allNodeInfos() const;

private:
  PluginManager();

  std::unordered_map<std::string, QPluginLoader*> plugin_loaders_;
  NodeInfos all_node_infos_;
};

class PluginLoaderGuard
{
public:
  explicit PluginLoaderGuard(const QString& _file_path);
  ~PluginLoaderGuard();

  PluginLoaderGuard(const PluginLoaderGuard&) = delete;
  PluginLoaderGuard& operator=(const PluginLoaderGuard&) = delete;

  PluginLoaderGuard(PluginLoaderGuard&& _other) noexcept;
  PluginLoaderGuard& operator=(PluginLoaderGuard&& _other) noexcept;

  QPluginLoader* release();
  [[nodiscard]] QPluginLoader* get() const;

private:
  QPluginLoader* loader_ { nullptr };
};

}  // namespace robosense::lidar

#endif  // PLUGIN_MANAGER_H
