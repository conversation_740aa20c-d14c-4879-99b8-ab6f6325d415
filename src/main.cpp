﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "config.h"
#include "ui/app_event.h"
#include <QApplication>

#include <QPalette>
#include <QStyleFactory>

namespace
{
int launchWindow(int _argc, char** _argv)
{
  int exit_code = 0;
  auto init_app = [&exit_code, &_argc, &_argv]() {
    //init main window
    QApplication app(_argc, _argv);
    robosense::lidar::G_APP->getMainWin()->show();
    exit_code = QApplication::exec();
    robosense::lidar::G_APP->destroyMainWin();
  };

  init_app();

  //check if restart
  while (robosense::lidar::MainWindow::restartCode() == exit_code)
  {
    init_app();
  }

  return exit_code;
}
}  // namespace

int main(int _argc, char* _argv[])
{
  Q_INIT_RESOURCE(resource);  // https://doc.qt.io/qt-5/resources.html

  qRegisterMetaType<std::string>("std::string");
  // qRegisterMetaType<std::shared_ptr<BT::TreeNode>>("std::shared_ptr<BT::TreeNode>");
  // qRegisterMetaType<BT::NodeStatus>("BT::NodeStatus");

  return launchWindow(_argc, _argv);
}