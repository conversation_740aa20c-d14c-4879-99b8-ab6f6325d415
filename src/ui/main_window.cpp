﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"

#include "data_serializer.h"
#include "flow_editor/side_panel/side_panel_node_widget.h"
#include "flow_editor/test_flow_editor.h"
#include "node_models/bt_node_model.h"
#include "node_utils.h"
#include "plugin_manager.h"
#include "property/property_model.h"
#include "property/property_view.h"
#include "ui/app_event.h"
#include "ui/message_browser.h"
#include "ui/one_to_x_ui.h"
#include "visualization/visualization_display_widget.h"
#include <QApplication>

// 声明可视化初始化函数
namespace robosense::lidar
{
void initializeBasicVisualizationWidgets();
}
#include <QDir>
#include <QFileDialog>
#include <QLabel>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QPluginLoader>
#include <QScrollArea>
#include <QSplitter>
#include <QTimer>
#include <QVBoxLayout>
#include <QtWidgets/QAction>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDockWidget>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QTreeView>
#include <QtWidgets/QVBoxLayout>

#include <QtCore/QDir>
#include <QtCore/QEvent>
#include <QtCore/QFileInfo>
#include <memory>
#include <qwidget.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

MainWindow::MainWindow(QWidget* _parent) :
  QMainWindow(_parent), splitter_display_(new QSplitter(Qt::Orientation::Vertical, this)), current_file_path_("")
{
  initPtr();

  // 创建菜单和工具栏
  createActions();

  browser_message_ = new robosense::lidar::rsfsc_lib::MessageBrowser(QString::fromUtf8(PROJECT_NAME), this);
  // robosense::lidar::RSFSCLog::setQtLogWidget(browser_message_, "slotShowMessage");

  splitter_display_->addWidget(&ptr_->get<TestFlowEditor>());
  splitter_display_->addWidget(browser_message_);

  // 设置占比 widget1 : widget2 = 2 : 1
  splitter_display_->setStretchFactor(0, 5);
  splitter_display_->setStretchFactor(1, 3);

  QWidget* widget_main     = new QWidget;
  QHBoxLayout* layout_main = new QHBoxLayout(widget_main);
  layout_main->addWidget(splitter_display_);
  this->setCentralWidget(widget_main);

  // 设置默认的自动保存配置路径
  QDir config_dir(QApplication::applicationDirPath() + "/config");
  if (!config_dir.exists())
  {
    config_dir.mkpath(".");
  }
  auto_save_config_path_ = config_dir.absolutePath() + "/auto_save_config.json";
  LOG_INFO("设置自动保存配置路径: {}", auto_save_config_path_);

  allConnect();

  // 初始化参数设置
  initParamSettings();

  // 设置一托多UI
  setupOneToXUi();

  // 设置工作空间文件选择器
  setupWorkspaceFileSelector();

  // 初始化可视化组件
  initializeBasicVisualizationWidgets();

  createDockWindows();
}

MainWindow::~MainWindow() { cleanBeforeQuit(); }

void MainWindow::closeEvent(QCloseEvent* _event)
{
  LOG_INFO("FUC: closeEvent call back");
  QMessageBox::StandardButton ret = QMessageBox::warning(this, "警告", "<font color='red'>确定退出?</font>",
                                                         QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
  if (ret == QMessageBox::No)
  {
    _event->ignore();
    return;
  }

  // if (browser_message_->isVisible())
  // {
  //   browser_message_->close();
  // }

  Utils::closeWidget(&ptr_->get<PropertyView>(), false);
  // closeWidget(widget_about_, false);

  QMainWindow::closeEvent(_event);
}

OneToXUi* MainWindow::oneToXUi(const std::size_t& _index) const
{
  return _index < all_one_to_x_ui_.size() ? all_one_to_x_ui_[_index].get() : nullptr;
}

PropertyView* MainWindow::propertyView() const { return &ptr_->get<PropertyView>(); }

VisualizationDisplayWidget* MainWindow::visualizationDisplayWidget() const { return visualization_display_widget_; }

void MainWindow::cleanBeforeQuit()
{
  RSFSCLog::getInstance()->info("FUC: clean before quit");
  // writeSettings();
  // G_APP->destroyParam();
  delete browser_message_;

  // delete widget_statusbar_;
  RSFSCLog::getInstance()->info("cleanBeforeQuit() finish");
}

void MainWindow::initParamSettings()
{
  // 注册雷达数量参数
  auto& property_view = ptr_->get<PropertyView>();
  property_view.setButtonHidden(PropertyView::BtnHidden { false, false, true, true, true });
  DataSerializer::getInstance()->setProperty(&property_view);
  auto model = property_view.model();
  if (!model)
  {
    model = std::make_shared<PropertyModel>();
    property_view.setModel(model);
  }

  model->registerProperty<IntItem>("雷达基本配置", "雷达数量", 1, 1, 16, 1);
  model->setPropertyDescription("雷达基本配置", "雷达数量", "雷达数量");
  // 设置该组为只读（不允许添加/删除属性）
  property_view.addReadOnlyGroup("雷达基本配置");

  QFile file(auto_save_config_path_);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("无法打开配置文件: {}", auto_save_config_path_);
    return;
  }

  QByteArray json_data = file.readAll();
  file.close();

  // 解析JSON以获取雷达数量
  try
  {
    nlohmann::json json = nlohmann::json::parse(json_data.toStdString());
    if (json.contains("groups") && json["groups"].contains("雷达基本配置") &&
        json["groups"]["雷达基本配置"].contains("雷达数量") &&
        json["groups"]["雷达基本配置"]["雷达数量"].contains("value"))
    {
      lidar_count_ = json["groups"]["雷达基本配置"]["雷达数量"]["value"];
    }

    LOG_INFO("检测到雷达数量: {}", lidar_count_);
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("解析配置文件失败: {}", e.what());
    return;
  }

  // 创建雷达参数配置
  createLidarParameters(lidar_count_);

  // 加载配置
  if (!loadConfigWithLidarCount(json_data.toStdString()))
  {
    return;
  }
  // 连接雷达数量变化信号
  QObject::connect(model.get(), &PropertyModel::signalValueChanged, this,
                   [this](const std::string& _group, const std::string& _key, const QVariant& _value) {
                     LOG_INFO("属性值变化: 组={}, 键={}, 值={}", _group, _key, _value.toString());
                     // 每次属性值变化时自动保存配置
                     autoSaveConfig();

                     if (_group == "雷达基本配置" && _key == "雷达数量")
                     {
                       // 当雷达数量变化时，重启软件
                       slotRestart();
                     }
                   });
}

// 根据雷达数量创建雷达参数配置
void MainWindow::createLidarParameters(int _lidar_count)
{
  auto& property_view = ptr_->get<PropertyView>();
  auto model          = property_view.model();

  // 暂时禁用UI刷新
  model->blockSignals(true);

  // 为每个雷达创建参数组
  for (int i = 1; i <= _lidar_count; ++i)
  {
    // 创建雷达参数组名称
    std::string group_name = "雷达" + std::to_string(i) + "固定参数";

    // 注册雷达基本参数
    model->registerProperty<BoolItem>(group_name, "项目编号是否可见", true);
    model->setPropertyDescription(group_name, "项目编号是否可见", "项目编号控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "雷达安装位置是否可见", true);
    model->setPropertyDescription(group_name, "雷达安装位置是否可见", "雷达安装位置控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "雷达SN是否可见", true);
    model->setPropertyDescription(group_name, "雷达SN是否可见", "雷达SN控件是否布局显示");

    // 设置该组为只读（不允许添加/删除属性）
    property_view.addReadOnlyGroup(QString::fromStdString(group_name));

    LOG_INFO("已创建雷达参数组: {}", group_name);
  }

  // 恢复信号
  model->blockSignals(false);

  // 手动触发一次模型变化信号，更新UI
  Q_EMIT model->signalModelChanged();
}

void MainWindow::setupOneToXUi()
{
  auto& property_view = ptr_->get<PropertyView>();
  auto model          = property_view.model();
  if (!model)
  {
    LOG_ERROR("setupOneToXUi : property_view model is null");
    return;
  }

  int lidar_count = model->getValue<int>("雷达基本配置", "雷达数量");

  for (int i = 0; i < lidar_count; ++i)
  {
    all_one_to_x_ui_.emplace_back(std::make_unique<OneToXUi>(i, this));
  }
}

void MainWindow::initPtr()
{
  auto property_view     = std::make_unique<PropertyView>();
  auto flow_editor       = std::make_unique<TestFlowEditor>();
  auto side_panel_widget = std::make_unique<SidePanelWidget>();
  ptr_ = std::make_unique<MainWindowPtr>(property_view.release(), flow_editor.release(), side_panel_widget.release());
}

void MainWindow::allConnect()
{
  QObject::connect(this, &MainWindow::signalAddPluginNodes, &ptr_->get<SidePanelWidget>(),
                   &SidePanelWidget::slotAddPluginNodes);

  // 连接节点选择事件
  auto& flow_editor = ptr_->get<TestFlowEditor>();
  QObject::connect(&flow_editor, &TestFlowEditor::nodeSelected, this, &MainWindow::slotNodeSelected);

  // 连接节点创建和删除事件
  QObject::connect(&flow_editor, &TestFlowEditor::nodeCreated, this, &MainWindow::slotNodeCreated);
  QObject::connect(&flow_editor, &TestFlowEditor::nodeDeleted, this, &MainWindow::slotNodeDeleted);

  // 连接DisplaySwitcherWidget的属性变化信号
  if (display_switcher_widget_ != nullptr)
  {
    QObject::connect(
      display_switcher_widget_, &DisplaySwitcherWidget::propertyValueChanged, this,
      [this](std::size_t _node_id, const std::string& _group, const std::string& _key, const QVariant& _value) {
        LOG_DEBUG("节点属性变化: node_id={}, group={}, key={}, value={}", _node_id, _group, _key,
                  _value.toString().toStdString());
        // 这里可以添加属性变化的处理逻辑
      });
  }
}

void MainWindow::createActions()
{
  // 文件菜单
  QMenu* file_menu = menuBar()->addMenu("文件");

  new_action_ = new QAction("新建", this);
  new_action_->setShortcut(QKeySequence::New);
  QObject::connect(new_action_, &QAction::triggered, this, &MainWindow::slotNewBt);
  file_menu->addAction(new_action_);

  open_action_ = new QAction("打开", this);
  open_action_->setShortcut(QKeySequence::Open);
  QObject::connect(open_action_, &QAction::triggered, this, &MainWindow::slotOpenFile);
  file_menu->addAction(open_action_);

  select_workspace_action_ = new QAction("选择工作空间文件夹", this);
  QObject::connect(select_workspace_action_, &QAction::triggered, this, &MainWindow::slotSelectWorkspaceFolder);
  file_menu->addAction(select_workspace_action_);

  file_menu->addSeparator();

  save_action_ = new QAction("保存", this);
  save_action_->setShortcut(QKeySequence::Save);
  QObject::connect(save_action_, &QAction::triggered, this, &MainWindow::slotSaveFile);
  file_menu->addAction(save_action_);

  save_as_action_ = new QAction("另存为", this);
  save_as_action_->setShortcut(QKeySequence::SaveAs);
  QObject::connect(save_as_action_, &QAction::triggered, this, &MainWindow::slotSaveFileAs);
  file_menu->addAction(save_as_action_);

  file_menu->addSeparator();

  exit_action_ = new QAction("退出", this);
  exit_action_->setShortcut(QKeySequence::Quit);
  connect(exit_action_, &QAction::triggered, this, &QWidget::close);
  file_menu->addAction(exit_action_);

  // 工具菜单
  QMenu* tools_menu = menuBar()->addMenu("工具");

  load_plugins_action_ = new QAction("加载插件", this);
  QObject::connect(load_plugins_action_, &QAction::triggered, this, &MainWindow::slotLoadPlugins);
  tools_menu->addAction(load_plugins_action_);

  param_setting_action_ = new QAction("参数配置", this);
  QObject::connect(param_setting_action_, &QAction::triggered, this, &MainWindow::slotParamSetting);
  tools_menu->addAction(param_setting_action_);

  // 帮助菜单
  QMenu* help_menu = menuBar()->addMenu("帮助");

  about_action_ = new QAction("关于", this);
  QObject::connect(about_action_, &QAction::triggered, this, &MainWindow::slotAbout);
  help_menu->addAction(about_action_);

  // 工具栏
  QToolBar* file_tool_bar = addToolBar("文件");

  reorder_action_ = new QAction("节点对齐", this);
  QObject::connect(reorder_action_, &QAction::triggered, this, &MainWindow::slotReorderNode);

  file_tool_bar->addAction(new_action_);
  file_tool_bar->addAction(open_action_);
  file_tool_bar->addAction(save_action_);
  file_tool_bar->addAction(select_workspace_action_);
  file_tool_bar->addAction(reorder_action_);
}

void MainWindow::createDockWindows()
{
  // 节点列表停靠窗口
  nodes_dock_ = new QDockWidget("操作面板", this);
  nodes_dock_->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
  nodes_dock_->setFeatures(QDockWidget::DockWidgetMovable | QDockWidget::DockWidgetFloatable);
  auto* widget_dock      = new QWidget(nodes_dock_);
  pushbutton_start_test_ = new QPushButton(QString::fromUtf8("开始测试"), widget_dock);

  QVBoxLayout* layout_control = new QVBoxLayout(widget_dock);
  layout_control->setSpacing(0);
  ptr_->get<SidePanelWidget>().setMimeType(QString(G_MIME_TYPE));
  ptr_->get<SidePanelWidget>().initializeBasicNodes();
  ptr_->get<SidePanelWidget>().setParent(widget_dock);
  layout_control->addWidget(&ptr_->get<SidePanelWidget>());
  layout_control->addWidget(pushbutton_start_test_);
  // layout_control->addItem(new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Expanding));
  widget_dock->setLayout(layout_control);
  nodes_dock_->setWidget(widget_dock);
  addDockWidget(Qt::LeftDockWidgetArea, nodes_dock_);

  QObject::connect(pushbutton_start_test_, &QPushButton::clicked, this, &MainWindow::slotStartTest);

  // 创建控制面板停靠窗口
  ctrl_dock_ = new QDockWidget("控制面板", this);
  ctrl_dock_->setAllowedAreas(Qt::RightDockWidgetArea | Qt::LeftDockWidgetArea);
  ctrl_dock_->setFeatures(QDockWidget::DockWidgetMovable | QDockWidget::DockWidgetFloatable);

  QWidget* widget_ctrl     = new QWidget(ctrl_dock_);
  QVBoxLayout* layout_ctrl = new QVBoxLayout(widget_ctrl);
  layout_ctrl->setSpacing(5);
  layout_ctrl->setContentsMargins(5, 5, 5, 5);

  for (const auto& one_ui : all_one_to_x_ui_)
  {
    if (one_ui && one_ui->getWidgetControl() != nullptr)
    {
      layout_ctrl->addWidget(one_ui->getWidgetControl());
    }
  }
  layout_ctrl->addStretch();

  widget_ctrl->setLayout(layout_ctrl);
  ctrl_dock_->setWidget(widget_ctrl);
  addDockWidget(Qt::LeftDockWidgetArea, ctrl_dock_);

  // 创建属性面板停靠窗口
  properties_dock_ = new QDockWidget("节点属性", this);
  properties_dock_->setAllowedAreas(Qt::RightDockWidgetArea | Qt::LeftDockWidgetArea);
  properties_dock_->setFeatures(QDockWidget::DockWidgetMovable | QDockWidget::DockWidgetFloatable);

  // 创建DisplaySwitcherWidget来管理节点属性显示
  display_switcher_widget_ = new DisplaySwitcherWidget(properties_dock_);
  properties_dock_->setWidget(display_switcher_widget_);

  addDockWidget(Qt::LeftDockWidgetArea, properties_dock_);

  // 创建可视化显示停靠窗口
  visualization_dock_ = new QDockWidget("可视化显示", this);
  visualization_dock_->setAllowedAreas(Qt::RightDockWidgetArea | Qt::BottomDockWidgetArea);
  visualization_dock_->setFeatures(QDockWidget::DockWidgetMovable | QDockWidget::DockWidgetFloatable);

  // 创建VisualizationDisplayWidget来管理可视化显示
  visualization_display_widget_ = new VisualizationDisplayWidget(visualization_dock_);
  visualization_dock_->setWidget(visualization_display_widget_);

  addDockWidget(Qt::RightDockWidgetArea, visualization_dock_);
  addDockWidget(Qt::RightDockWidgetArea, nodes_dock_);
}

void MainWindow::slotNewBt()
{
  // editor_->newTree();
  current_file_path_ = "";
}

void MainWindow::slotOpenFile()
{
  QString file_path =
    QFileDialog::getOpenFileName(this, "打开测试工程文件", "", "测试工程文件 (*.rsfsc);;所有文件 (*)");
  if (file_path.isEmpty())
  {
    return;
  }

  if (!DataSerializer::getInstance()->loadFromUnifiedFile(file_path))
  {
    QMessageBox::critical(this, "错误", "无法加载测试工程文件: " + file_path);
    return;
  }

  current_file_path_ = file_path;
  statusBar()->showMessage("已加载: " + file_path);
}

void MainWindow::slotSaveFile()
{
  QString file_path;

  // 如果在工作空间模式下且有选中的文件，直接保存到当前文件
  if (!workspace_folder_path_.isEmpty() && !current_file_path_.isEmpty())
  {
    file_path = current_file_path_;
  }
  else
  {
    // 否则弹出保存对话框
    file_path = QFileDialog::getSaveFileName(this, "保存测试工程文件", "", "测试工程文件 (*.rsfsc);;所有文件 (*)");
    if (file_path.isEmpty())
    {
      return;
    }
  }

  current_file_path_ = file_path;
  if (!DataSerializer::getInstance()->saveToUnifiedFile(current_file_path_))
  {
    QMessageBox::critical(this, "错误", "无法保存rsfsc文件: " + current_file_path_);
    return;
  }

  // 同时更新自动保存的配置文件路径
  auto_save_config_path_ = current_file_path_;

  LOG_INFO("保存: {} 成功", current_file_path_);

  // 在工作空间模式下显示文件名，否则显示完整路径
  if (!workspace_folder_path_.isEmpty())
  {
    QString file_name = QFileInfo(file_path).baseName();
    statusBar()->showMessage("已保存: " + file_name);
  }
  else
  {
    statusBar()->showMessage("已保存: " + current_file_path_);
  }
}

void MainWindow::slotSaveFileAs()
{
  QString default_dir;
  QString dialog_title = "另存为测试工程文件";

  // 如果在工作空间模式下，默认保存到工作空间文件夹
  if (!workspace_folder_path_.isEmpty())
  {
    default_dir  = workspace_folder_path_;
    dialog_title = "在工作空间中另存为";
  }

  QString file_path =
    QFileDialog::getSaveFileName(this, dialog_title, default_dir, "测试工程文件 (*.rsfsc);;所有文件 (*)");
  if (file_path.isEmpty())
  {
    return;
  }

  if (!DataSerializer::getInstance()->saveToUnifiedFile(file_path))
  {
    QMessageBox::critical(this, "错误", "无法保存rsfsc文件: " + file_path);
    return;
  }

  current_file_path_     = file_path;
  auto_save_config_path_ = current_file_path_;

  LOG_INFO("另存为: {} 成功", current_file_path_);

  // 如果保存在工作空间内，刷新文件列表
  if (!workspace_folder_path_.isEmpty() && file_path.startsWith(workspace_folder_path_))
  {
    refreshFileList();

    // 自动选择新保存的文件
    QString file_name = QFileInfo(file_path).baseName();
    int index         = file_selector_combo_->findText(file_name);
    if (index >= 0)
    {
      file_selector_combo_->setCurrentIndex(index);
    }

    statusBar()->showMessage("已另存为: " + file_name);
  }
  else
  {
    statusBar()->showMessage("已另存为: " + current_file_path_);
  }
}

void MainWindow::slotLoadPlugins()
{
  QString plugin_dir = QFileDialog::getExistingDirectory(this, "选择插件目录", QApplication::applicationDirPath());
  if (plugin_dir.isEmpty())
  {
    return;
  }

  int loaded_count = PluginManager::getInstance().loadPlugins(plugin_dir);
  statusBar()->showMessage(QString("已加载 %1 个插件").arg(loaded_count));

  Q_EMIT signalAddPluginNodes(PluginManager::getInstance().allNodeInfos());
}

void MainWindow::slotParamSetting() { Utils::showWidget(&ptr_->get<PropertyView>()); }

void MainWindow::slotAbout() {}

void MainWindow::slotStartTest() { ptr_->get<TestFlowEditor>().startTest(); }

void MainWindow::slotReorderNode() { ptr_->get<TestFlowEditor>().nodeReorder(); }

void MainWindow::slotRestart()
{
  QMessageBox::information(&ptr_->get<PropertyView>(), QString::fromUtf8("提示"), QString::fromUtf8("即将重启软件."),
                           QMessageBox::Ok);
  close();
  QApplication::exit(MainWindow::restartCode());
}

void MainWindow::slotNodeSelected(std::size_t _node_id)
{
  LOG_DEBUG("节点被选择, node id: {}", _node_id);

  if (display_switcher_widget_ == nullptr)
  {
    LOG_ERROR("display_switcher_widget_ is null");
    return;
  }

  // 获取节点模型
  auto& flow_editor      = ptr_->get<TestFlowEditor>();
  auto* data_graph_model = flow_editor.getDataFlowGraphModel();
  if (data_graph_model == nullptr)
  {
    LOG_ERROR("Failed to get DataFlowGraphModel");
    return;
  }

  // 获取BtNodeModel
  auto* bt_node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
  if (bt_node_model == nullptr)
  {
    LOG_ERROR("Failed to get BtNodeModel for node id: {}", _node_id);
    return;
  }

  // 使用DisplaySwitcherWidget来显示节点属性
  display_switcher_widget_->onNodeSelected(_node_id, bt_node_model);

  LOG_DEBUG("成功为节点 {} 设置属性面板", _node_id);
}

void MainWindow::slotNodeCreated(std::size_t _node_id)
{
  LOG_DEBUG("节点被创建, node id: {}", _node_id);

  if (display_switcher_widget_ == nullptr)
  {
    LOG_ERROR("display_switcher_widget_ is null");
    return;
  }

  // 获取节点模型
  auto& flow_editor      = ptr_->get<TestFlowEditor>();
  auto* data_graph_model = flow_editor.getDataFlowGraphModel();
  if (data_graph_model == nullptr)
  {
    LOG_ERROR("Failed to get DataFlowGraphModel");
    return;
  }

  // 获取BtNodeModel
  auto* bt_node_model = data_graph_model->delegateModel<BtNodeModel>(_node_id);
  if (bt_node_model == nullptr)
  {
    LOG_ERROR("Failed to get BtNodeModel for node id: {}", _node_id);
    return;
  }

  // 通知DisplaySwitcherWidget节点被创建
  display_switcher_widget_->onNodeCreated(_node_id, bt_node_model);

  LOG_DEBUG("成功为节点 {} 创建属性面板", _node_id);
}

void MainWindow::slotNodeDeleted(std::size_t _node_id)
{
  LOG_DEBUG("节点被删除, node id: {}", _node_id);

  if (display_switcher_widget_ == nullptr)
  {
    LOG_ERROR("display_switcher_widget_ is null");
    return;
  }

  // 通知DisplaySwitcherWidget节点被删除
  display_switcher_widget_->onNodeDeleted(_node_id);

  LOG_DEBUG("成功删除节点 {} 的属性面板", _node_id);
}

void MainWindow::autoSaveConfig()
{
  // 如果正在加载配置，不进行自动保存
  if (is_loading_config_)
  {
    return;
  }

  // 如果自动保存路径为空，使用默认路径
  if (auto_save_config_path_.isEmpty())
  {
    // 获取应用程序目录下的config子目录
    QDir config_dir(QApplication::applicationDirPath() + "/config");
    if (!config_dir.exists())
    {
      config_dir.mkpath(".");
    }
    auto_save_config_path_ = config_dir.absolutePath() + "/auto_save_config.json";
    LOG_INFO("设置自动保存配置路径: {}", auto_save_config_path_);
  }

  if (!ptr_->get<PropertyView>().saveToJson(auto_save_config_path_))
  {
    LOG_ERROR("自动保存配置失败: {}", auto_save_config_path_);
    return;
  }

  LOG_INFO("自动保存配置到: {}", auto_save_config_path_);
}

bool MainWindow::loadConfig(const QString& _file_path)
{
  if (_file_path.isEmpty())
  {
    LOG_WARN("加载配置文件路径为空，将使用默认配置");
    return true;  // 返回true，使用默认配置继续执行
  }

  // 检查文件是否存在
  QFileInfo file_info(_file_path);
  if (!file_info.exists() || !file_info.isFile())
  {
    LOG_WARN("配置文件不存在: {}，将使用默认配置", _file_path);
    return true;  // 返回true，使用默认配置继续执行
  }

  // 设置加载标志，防止在加载过程中触发自动保存
  is_loading_config_ = true;

  bool result = ptr_->get<PropertyView>().loadFromJson(_file_path);

  if (result)
  {
    LOG_INFO("成功加载配置: {}", _file_path);
    current_file_path_ = _file_path;
    statusBar()->showMessage("已加载: " + _file_path);
  }
  else
  {
    LOG_ERROR("加载配置失败: {}", _file_path);
  }

  // 重置加载标志
  is_loading_config_ = false;

  return result;
}

bool MainWindow::loadConfigWithLidarCount(const std::string& _json)
{
  auto& property_view = ptr_->get<PropertyView>();

  try
  {
    // 创建要排除的组列表
    std::vector<std::string> exclude_groups;
    for (int i = lidar_count_ + 1; i <= 16; ++i)
    {
      exclude_groups.push_back("雷达" + std::to_string(i) + "固定参数");
    }

    property_view.loadFromJsonExcludeGroups(_json, exclude_groups);
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("解析配置文件失败: {}", e.what());
    return false;
  }

  return true;
}

void MainWindow::setupWorkspaceFileSelector()
{
  // 创建工作空间文件选择器的工具栏
  QToolBar* workspace_toolbar = addToolBar("工作空间");

  // 添加标签
  workspace_label_ = new QLabel("工作空间: 未选择", this);
  workspace_label_->setMinimumWidth(200);
  workspace_toolbar->addWidget(workspace_label_);

  // 添加文件选择下拉框
  file_selector_combo_ = new QComboBox(this);
  file_selector_combo_->setMinimumWidth(300);
  file_selector_combo_->setEnabled(false);  // 初始状态禁用
  workspace_toolbar->addWidget(file_selector_combo_);

  // 连接文件选择信号
  QObject::connect(file_selector_combo_, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
                   &MainWindow::slotFileSelectionChanged);

  // 当ComboBox获得焦点时，检查文件变化并自动刷新
  file_selector_combo_->installEventFilter(this);

  LOG_INFO("工作空间文件选择器初始化完成");
}

void MainWindow::slotSelectWorkspaceFolder()
{
  QString folder_path =
    QFileDialog::getExistingDirectory(this, "选择工作空间文件夹", QApplication::applicationDirPath());
  if (folder_path.isEmpty())
  {
    return;
  }

  workspace_folder_path_ = folder_path;
  workspace_label_->setText("工作空间: " + QFileInfo(folder_path).fileName());

  // 刷新文件列表
  refreshFileList();

  LOG_INFO("选择工作空间文件夹: {}", workspace_folder_path_);
  statusBar()->showMessage("工作空间: " + workspace_folder_path_);
}

void MainWindow::refreshFileList()
{
  if (workspace_folder_path_.isEmpty())
  {
    return;
  }

  // 清空当前列表
  file_selector_combo_->clear();
  rsfsc_files_.clear();

  // 扫描文件夹中的.rsfsc文件
  QDir workspace_dir(workspace_folder_path_);
  QStringList filters;
  filters << "*.rsfsc";

  QFileInfoList file_list = workspace_dir.entryInfoList(filters, QDir::Files | QDir::Readable, QDir::Name);

  if (file_list.isEmpty())
  {
    file_selector_combo_->addItem("(无.rsfsc文件)");
    file_selector_combo_->setEnabled(false);
    LOG_WARN("工作空间文件夹中未找到.rsfsc文件: {}", workspace_folder_path_);
    return;
  }

  // 添加文件到下拉框
  for (const QFileInfo& file_info : file_list)
  {
    QString file_name = file_info.baseName();  // 不包含扩展名的文件名
    QString file_path = file_info.absoluteFilePath();

    file_selector_combo_->addItem(file_name);
    rsfsc_files_.append(file_path);
  }

  file_selector_combo_->setEnabled(true);

  LOG_INFO("在工作空间中找到 {} 个.rsfsc文件", file_list.size());

  // 自动加载第一个文件
  if (!rsfsc_files_.isEmpty())
  {
    slotFileSelectionChanged(0);
  }
}

void MainWindow::slotFileSelectionChanged(int _index)
{
  if (_index < 0 || _index >= rsfsc_files_.size())
  {
    return;
  }

  QString selected_file = rsfsc_files_[_index];
  QString file_name     = file_selector_combo_->itemText(_index);

  LOG_INFO("切换到文件: {} ({})", file_name, selected_file);

  // 设置加载状态，防止触发自动保存
  is_loading_config_ = true;

  // 使用DataSerializer加载文件，这会触发完整的UI更新
  if (DataSerializer::getInstance()->loadFromUnifiedFile(selected_file))
  {
    current_file_path_ = selected_file;
    statusBar()->showMessage("已加载: " + file_name);
    LOG_INFO("成功切换到文件: {}", selected_file);
  }
  else
  {
    QMessageBox::critical(this, "错误", "无法加载文件: " + file_name);
    LOG_ERROR("切换文件失败: {}", selected_file);
  }

  // 恢复加载状态
  is_loading_config_ = false;
}

bool MainWindow::eventFilter(QObject* _watched, QEvent* _event)
{
  // 当ComboBox获得焦点时，自动检查文件变化
  if (_watched == file_selector_combo_ && _event->type() == QEvent::FocusIn)
  {
    if (!workspace_folder_path_.isEmpty())
    {
      // 检查文件夹中的文件是否有变化
      QDir workspace_dir(workspace_folder_path_);
      QStringList filters;
      filters << "*.rsfsc";

      QFileInfoList current_files = workspace_dir.entryInfoList(filters, QDir::Files | QDir::Readable, QDir::Name);

      // 如果文件数量或文件名有变化，自动刷新
      if (current_files.size() != rsfsc_files_.size())
      {
        LOG_INFO("检测到工作空间文件数量变化，自动刷新文件列表");
        refreshFileList();
      }
      else
      {
        // 检查文件名是否有变化
        bool files_changed = false;
        for (int i = 0; i < current_files.size(); ++i)
        {
          QString current_path = current_files[i].absoluteFilePath();
          if (i >= rsfsc_files_.size() || current_path != rsfsc_files_[i])
          {
            files_changed = true;
            break;
          }
        }

        if (files_changed)
        {
          LOG_INFO("检测到工作空间文件变化，自动刷新文件列表");
          refreshFileList();
        }
      }
    }
  }

  return QMainWindow::eventFilter(_watched, _event);
}

}  // namespace robosense::lidar
