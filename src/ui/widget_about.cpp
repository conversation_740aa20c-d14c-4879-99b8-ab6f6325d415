﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "widget_about.h"

// NOTE if you dont use spdlog, uncomment this line
#define ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_SPDLOG 1  // NOLINT(cppcoreguidelines-macro-usage)

#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#  define ROBOSENSE_LIDAR_WIDGET_ABOUT_USE_RSFSC_LOG 1  // NOLINT(cppcoreguidelines-macro-usage)
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#  define ROBOSENSE_LIDAR_WIDGET_ABOUT_USE_RSFSC_LOG 1  // NOLINT(cppcoreguidelines-macro-usage)
#else
#  include <iostream>
#  define ROBOSENSE_LIDAR_WIDGET_ABOUT_USE_RSFSC_LOG 0  // NOLINT(cppcoreguidelines-macro-usage)
#endif

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_EIGEN
// #  include <Eigen/src/Core/util/Macros.h>
#  include <eigen3/Eigen/Core>
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_EIGEN

#include <QtCore/QtGlobal>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

namespace robosense
{
namespace lidar
{
class LabelWithURL : public QLabel
{
public:
  LabelWithURL(const QString& _title, const QString& _url, QWidget* _parent) : QLabel(_parent)
  {
    this->setOpenExternalLinks(true);
    QString text("<a href=\"");
    text += _url;
    text += "\">";
    text += _title;
    this->setText(text);

    auto size_policy = this->sizePolicy();
    size_policy.setHorizontalPolicy(QSizePolicy::Expanding);
    this->setSizePolicy(size_policy);
  }
};

WidgetAbout::WidgetAbout(QWidget* _parent) :
  QDialog(_parent),
  tab_widget_(new QTabWidget(this)),
  year_start_copyright_(0),
  label_contact_email_(new QLabel(this)),
  label_build_commit_(new QLabel(this)),
  label_build_time_(new QLabel(this)),
  label_version_(new QLabel(this)),
  label_brief_(new QLabel(this)),
  label_robosense_license_(new QLabel(this)),
  label_robosense_license_title_(new QLabel(this))
{
  // widget license
  label_robosense_license_->setWordWrap(true);
  QVBoxLayout* layout_license = new QVBoxLayout;
  layout_license->addWidget(label_robosense_license_title_);
  layout_license->addWidget(label_robosense_license_);
  QWidget* widget_license = new QWidget(this);
  widget_license->setLayout(layout_license);

  // widget libraries
  QLabel* label_libraries_license_declare = new QLabel("This software use open source components whose copyright and "
                                                       "other proprietary rights belong to their respective owners: ",
                                                       this);
  label_libraries_license_declare->setWordWrap(true);
  QTableWidget* tablewidget_libraries_list = new QTableWidget(this);
  tablewidget_libraries_list->setColumnCount(3);
  // tablewidget_libraries_list->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
  tablewidget_libraries_list->horizontalHeader()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
  tablewidget_libraries_list->horizontalHeader()->setSectionResizeMode(1, QHeaderView::ResizeToContents);
  tablewidget_libraries_list->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Stretch);
  // tablewidget_libraries_list->resizeColumnsToContents();
  tablewidget_libraries_list->setHorizontalHeaderLabels(QStringList() << "Name"
                                                                      << "License"
                                                                      << "Change State");
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters, readability-identifier-naming)
  auto insertLibInfo = [&tablewidget_libraries_list](const QString& _name, const QString& _home_url,
                                                     // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                                     const QString& _license, const QString& _license_url,
                                                     const QString& _change_state) {
    int table_row = tablewidget_libraries_list->rowCount();
    tablewidget_libraries_list->insertRow(table_row);
    tablewidget_libraries_list->setCellWidget(table_row, 0,
                                              new LabelWithURL(_name, _home_url, tablewidget_libraries_list));
    tablewidget_libraries_list->setCellWidget(table_row, 1,
                                              new LabelWithURL(_license, _license_url, tablewidget_libraries_list));
    tablewidget_libraries_list->setItem(table_row++, 2, new QTableWidgetItem(_change_state));
  };
  // Qt
  insertLibInfo(QString("Qt ") + QString(qVersion()), QString("https://www.qt.io/"), QString("LGPL v3"),
                QString("https://www.gnu.org/licenses/lgpl-3.0.en.html"), "not changed");

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_BOOST
#  include <boost/version.hpp>
  insertLibInfo(QString("Boost ") + QString(BOOST_LIB_VERSION), QString("https://www.boost.org/"),
                "Boost Software License 1.0", "https://www.boost.org/users/license.html", "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_BOOST

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_OPENCV
#  include <opencv2/core/version.hpp>
  if (CV_VERSION_MAJOR > 4 || (CV_VERSION_MAJOR == 4 && CV_VERSION_MINOR >= 5))
  {  // Apache 2
    insertLibInfo(QString("OpenCV ") + QString(CV_VERSION), QString("https://opencv.org/"), "Apache 2.0",
                  "https://www.apache.org/licenses/LICENSE-2.0.html", "not changed");
  }
  else
  {  // 3-clause BSD license
    insertLibInfo(QString("OpenCV ") + QString(CV_VERSION), QString("https://opencv.org/"), "The 3-Clause BSD License",
                  "https://opensource.org/licenses/BSD-3-Clause", "not changed");
  }
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_OPENCV

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_PCL
#  include <pcl/pcl_config.h>
  insertLibInfo(QString("PCL ") + QString(PCL_VERSION_PRETTY), QString("https://pointclouds.org/"), "BSD license",
                "https://opensource.org/licenses/BSD-3-Clause", "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_PCL

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_VTK
#  include <vtkVersionMacros.h>
  insertLibInfo(QString("VTK ") + QString(VTK_VERSION), QString("https://vtk.org/"), "BSD license",
                "https://opensource.org/licenses/BSD-3-Clause", "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_VTK

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_EIGEN
  if (EIGEN_VERSION_AT_LEAST(3, 1, 1))
  {  // MPL2
    insertLibInfo(QString("Eigen ") + QString::number(EIGEN_WORLD_VERSION) + "." +
                    QString::number(EIGEN_MAJOR_VERSION) + "." + QString::number(EIGEN_MINOR_VERSION),
                  QString("https://eigen.tuxfamily.org/"), "MPL2", "hhttps://www.mozilla.org/en-US/MPL/2.0/",
                  "not changed");
  }
  else
  {  // LGPL v3
    insertLibInfo(QString("Eigen ") + QString::number(EIGEN_WORLD_VERSION) + "." +
                    QString::number(EIGEN_MAJOR_VERSION) + "." + QString::number(EIGEN_MINOR_VERSION),
                  QString("https://eigen.tuxfamily.org/"), QString("LGPL v3"),
                  QString("https://www.gnu.org/licenses/lgpl-3.0.en.html"), "not changed");
  }
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_EIGEN

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_SPDLOG
  // #  include "rsfsc_log_src/spdlog/include/spdlog/version.h" // other user does not contain this file
  insertLibInfo(QString("spdlog ") + QString::number(1) + "." + QString::number(9) + "." + QString::number(2),
                QString("https://github.com/gabime/spdlog"), "MIT", "http://opensource.org/licenses/MIT",
                "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_SPDLOG

  // #if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_OPENMP
  // #include <openmp/openmp.hpp>
  // insertLibInfo();
  // #endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_OPENMP

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_YAMLCPP
  insertLibInfo(QString("yaml-cpp "), QString("https://github.com/jbeder/yaml-cpp"), "MIT",
                "http://opensource.org/licenses/MIT", "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_YAMLCPP

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_ROS
  std::string ros_distro(getenv("ROS_DISTRO"));
  insertLibInfo(QString("ROS ") + QString::fromStdString(ros_distro), QString("https://www.ros.org/"),
                "The 3-Clause BSD License", "https://opensource.org/licenses/BSD-3-Clause", "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_ROS

#if ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_PYTHON
#  include <patchlevel.h>
  insertLibInfo(QString("Python ") + QString::number(PY_MAJOR_VERSION) + "." + QString::number(PY_MINOR_VERSION) + "." +
                  QString::number(PY_MICRO_VERSION),
                QString("https://www.python.org/"), "PSF License Agreement",
                "https://docs.python.org/" + QString::number(PY_MAJOR_VERSION) + "." +
                  QString::number(PY_MINOR_VERSION) + "/license.html",
                "not changed");
#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_PYTHON

  QVBoxLayout* layout_libraries = new QVBoxLayout;
  layout_libraries->addWidget(label_libraries_license_declare);
  layout_libraries->addSpacing(1);
  layout_libraries->addWidget(tablewidget_libraries_list);
  QWidget* widget_lib = new QWidget(this);
  widget_lib->setLayout(layout_libraries);

  tab_widget_->addTab(setupLayoutInfo(), "Info");
  tab_widget_->addTab(widget_license, "License");
  tab_widget_->addTab(widget_lib, "Libraries");

  QVBoxLayout* layout_main = new QVBoxLayout;
  layout_main->addWidget(tab_widget_);
  QPushButton* pushbutton_ok = new QPushButton("OK", this);
  layout_main->addWidget(pushbutton_ok, 0, Qt::AlignRight);

  QObject::connect(pushbutton_ok, &QPushButton::clicked, this, &WidgetAbout::close);

  this->setLayout(layout_main);
  this->setWindowModality(Qt::WindowModal);
  this->setWindowFlags(this->windowFlags() & ~Qt::WindowMinMaxButtonsHint);
  //this->setWindowFlags(Qt::FramelessWindowHint);
}

QWidget* WidgetAbout::setupLayoutInfo()
{  // widget info
  QGridLayout* layout_info = new QGridLayout;

  // layout_info->addWidget(new QLabel(QString::fromUtf8(WIDGET_ABOUT_PROJECT_NAME), this), 0, 0, 1, 2, Qt::AlignCenter);
  layout_info->addWidget(new QLabel("<b>brief: </b>", this), 0, 0);
  layout_info->addWidget(label_brief_, 0, 1);
  layout_info->addWidget(new QLabel("<b>version: </b>", this), 1, 0);
  layout_info->addWidget(label_version_, 1, 1);
  layout_info->addWidget(new QLabel("<b>build time: </b>", this), 2, 0);
  layout_info->addWidget(label_build_time_, 2, 1);
  layout_info->addWidget(new QLabel("<b>build commit: </b>", this), 3, 0);
  layout_info->addWidget(label_build_commit_, 3, 1);
  layout_info->addWidget(new QLabel("<b>contact: <br>", this), 4, 0);
  layout_info->addWidget(label_contact_email_, 4, 1);
  QWidget* widget_info = new QWidget(this);
  widget_info->setLayout(layout_info);
  return widget_info;
}

void WidgetAbout::logError(const QString& _msg)
{
#if ROBOSENSE_LIDAR_WIDGET_ABOUT_USE_RSFSC_LOG
  RSFSCLog::getInstance()->error(_msg.toStdString());
#else
  std::cerr << _msg.toStdString() << std::endl;
#endif
}

void WidgetAbout::logInfo(const QString& _msg)
{
#if ROBOSENSE_LIDAR_WIDGET_ABOUT_USE_RSFSC_LOG
  RSFSCLog::getInstance()->info(_msg.toStdString());
#else
  std::cout << _msg.toStdString() << std::endl;
#endif
}

void WidgetAbout::setYearStartCopyright(const unsigned int _year) { year_start_copyright_ = _year; }

void WidgetAbout::setContactEmail(const QString& _email)
{
  if (_email.length() < 13 || _email.right(13) != "@robosense.cn")
  {
    logError("WidgetAbout::setContactEmail -> email must be end with .robosense.cn");
    return;
  }

  contact_email_ = _email;
}

void WidgetAbout::setBuildCommit(const QString& _build_commit) { project_build_commit_ = _build_commit; }

void WidgetAbout::setBuildTime(const QString& _build_time) { project_build_time_ = _build_time; }

void WidgetAbout::setVersionStr(const QString& _version_str)
{
  if (!_version_str.contains("v"))
  {
    logError("WidgetAbout::setVersionStr -> _version_str should contains v");
    return;
  }
  project_version_str_ = _version_str;
}

void WidgetAbout::setBrief(const QString& _brief) { project_brief_ = _brief; }

void WidgetAbout::setName(const QString& _name) { project_name_ = _name; }

void WidgetAbout::showEvent(QShowEvent* _event)
{
  QDialog::showEvent(_event);
  if (year_start_copyright_ == 0 || contact_email_.isEmpty() || project_build_time_.isEmpty() ||
      project_version_str_.isEmpty() || project_name_.isEmpty() || project_brief_.isEmpty() ||
      project_build_commit_.isEmpty())
  {
    logError("WidgetAbout::showEvent -> some item is not set, please set all item before show");
    QDialog::showEvent(_event);
  }
  label_contact_email_->setText(contact_email_);
  label_build_commit_->setText(project_build_commit_);
  label_build_time_->setText(project_build_time_);
  label_version_->setText(project_version_str_);
  label_brief_->setText(project_brief_);
  QString license_text = QString::fromUtf8("Copyright © %1 RoboSense All rights reserved.<br>"
                                           "Suteng Innovation Technology Co., Ltd. www.robosense.ai<br>"
                                           "<br>"
                                           "This software is provided to you directly by RoboSense and might<br>"
                                           "only be used to access RoboSense LiDAR. Any compilation,<br>"
                                           "modification, exploration, reproduction and redistribution are<br>"
                                           "restricted without RoboSense's prior consent.<br>"
                                           "<br>"
                                           "THIS SOFTWARE IS PROVIDED \"AS IS\" AND ANY EXPRESSED OR IMPLIED<br>"
                                           "WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES<br>"
                                           "OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE<br>"
                                           "DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,<br>"
                                           "INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES<br>"
                                           "(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR<br>"
                                           "SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)<br>"
                                           "HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,<br>"
                                           "STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING<br>"
                                           "IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE<br>"
                                           "POSSIBILITY OF SUCH DAMAGE.")
                           .arg(year_start_copyright_);
  label_robosense_license_title_->setText(QString("<h2>") + project_name_ + QString(" License</h2>"));
  label_robosense_license_->setText(license_text);
  this->setWindowTitle(QString("About ") + project_name_);
  auto size_policy = label_robosense_license_->sizePolicy();
  size_policy.setHorizontalPolicy(QSizePolicy::Minimum);
  label_robosense_license_->setSizePolicy(size_policy);
}
}  // namespace lidar
}  // namespace robosense
