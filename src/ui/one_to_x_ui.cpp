﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "one_to_x_ui.h"
#include "config.h"
#include "property/property_view.h"
#include "ui/app_event.h"
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

OneToXUi::OneToXUi(const int _current_index, QMainWindow* _main_win, QObject* _parent) :
  QObject(_parent), main_win_(_main_win), current_lidar_index_(_current_index)
{
  setupLayout();
}

QWidget* OneToXUi::getWidgetControl() const { return groupbox_lidar_ctrl_; }

void OneToXUi::setupLayout()
{
  QString device_name  = QString::fromUtf8("设备%1：").arg(QString::number(current_lidar_index_ + 1));
  groupbox_lidar_ctrl_ = new QGroupBox(device_name);
  layout_lidar_ctrl_   = new QVBoxLayout;

  layout_lidar_ctrl_->setSpacing(1);
  groupbox_lidar_ctrl_->setLayout(layout_lidar_ctrl_);
}
}  // namespace robosense::lidar
