# 工作空间多文件管理功能使用指南

## 🎯 **功能概述**

新增的工作空间功能允许您：
- 选择一个文件夹作为工作空间
- 自动检索文件夹中的所有.rsfsc文件
- 通过ComboBox快速切换不同的测试流程文件
- 在工作空间模式下便捷地保存和管理文件

## 🚀 **使用方法**

### 1. 选择工作空间文件夹

**方法一：通过菜单**
- 点击菜单栏 `文件` → `选择工作空间文件夹`
- 在弹出的对话框中选择包含.rsfsc文件的文件夹

**方法二：通过工具栏**
- 点击工具栏中的"选择工作空间文件夹"按钮
- 选择目标文件夹

### 2. 文件切换和管理

选择工作空间后，界面会显示：
- **工作空间标签**：显示当前工作空间文件夹名称
- **文件选择下拉框**：列出所有.rsfsc文件（显示文件名，不含扩展名）

**切换文件**：
- 在下拉框中选择不同的文件名
- 系统会自动加载选中的.rsfsc文件并更新所有UI界面
- 状态栏会显示当前加载的文件
- 当下拉框获得焦点时，自动检查文件夹变化并更新列表

### 3. 保存功能

**保存（Ctrl+S）**：
- 在工作空间模式下，直接保存到当前选中的文件
- 非工作空间模式下，弹出保存对话框

**另存为（Ctrl+Shift+S）**：
- 在工作空间模式下，默认保存到工作空间文件夹
- 如果保存在工作空间内，会自动刷新文件列表并选中新文件

## 📋 **界面布局**

```
菜单栏: 文件 → 选择工作空间文件夹
工具栏: [新建] [打开] [保存] [选择工作空间文件夹] [重排节点]
工作空间栏: [工作空间: 文件夹名] [文件选择下拉框]
```

## 🔧 **功能特性**

### 自动文件检索
- 自动扫描工作空间文件夹中的所有.rsfsc文件
- 按文件名排序显示
- 支持中文文件名
- 当下拉框获得焦点时自动检查文件变化

### 智能切换和保存
- 切换文件时自动加载并更新所有UI界面
- 工作空间模式下，保存操作直接更新当前文件
- 另存为操作在工作空间内时自动刷新列表
- 保存后自动选中新创建的文件

### 状态提示
- 状态栏显示当前工作空间路径
- 显示当前加载的文件名
- 保存操作的成功/失败提示

## 📝 **使用场景**

### 场景1：多个测试流程管理
```
工作空间文件夹/
├── 基础功能测试.rsfsc
├── 性能测试.rsfsc
├── 稳定性测试.rsfsc
└── 回归测试.rsfsc
```

### 场景2：不同设备配置
```
设备测试配置/
├── 设备A配置.rsfsc
├── 设备B配置.rsfsc
├── 设备C配置.rsfsc
└── 通用配置.rsfsc
```

### 场景3：版本管理
```
测试版本/
├── v1.0测试.rsfsc
├── v1.1测试.rsfsc
├── v2.0测试.rsfsc
└── 最新版本.rsfsc
```

## ⚠️ **注意事项**

### 文件要求
- 只检索.rsfsc扩展名的文件
- 文件必须是有效的测试工程文件格式
- 文件名建议使用有意义的描述

### 操作建议
- 选择工作空间前建议先保存当前工作
- 切换文件时会自动加载，当前未保存的修改可能丢失
- 建议定期使用"刷新"按钮更新文件列表

### 性能考虑
- 工作空间文件夹不宜包含过多文件（建议<100个）
- 大文件加载可能需要一些时间
- 文件切换时会重新加载整个配置

## 🔄 **工作流程示例**

### 典型工作流程
1. **启动应用** → 选择工作空间文件夹
2. **选择测试文件** → 从下拉框选择要执行的测试
3. **编辑配置** → 修改测试参数和流程
4. **保存更改** → Ctrl+S 直接保存到当前文件
5. **切换测试** → 选择其他测试文件继续工作
6. **创建新测试** → 另存为创建新的测试配置

### 团队协作流程
1. **共享工作空间** → 团队成员使用相同的文件夹
2. **分工测试** → 不同成员负责不同的测试文件
3. **版本控制** → 配合Git等工具管理测试文件版本
4. **快速切换** → 在不同测试场景间快速切换

## 🚀 **高级技巧**

### 快捷键
- `Ctrl+S`：快速保存到当前文件
- `Ctrl+Shift+S`：另存为新文件
- `F5`：刷新文件列表（如果实现了快捷键）

### 文件命名建议
- 使用描述性名称：`TCP通信测试.rsfsc`
- 包含版本信息：`设备A_v2.1.rsfsc`
- 按功能分类：`01_基础测试.rsfsc`、`02_性能测试.rsfsc`

### 工作空间组织
```
项目根目录/
├── 基础测试/
│   ├── 连接测试.rsfsc
│   └── 基本功能.rsfsc
├── 性能测试/
│   ├── 压力测试.rsfsc
│   └── 稳定性测试.rsfsc
└── 回归测试/
    ├── 版本对比.rsfsc
    └── 兼容性测试.rsfsc
```

## 📞 **故障排除**

### 常见问题

**Q: 工作空间中没有显示文件**
A: 检查文件夹中是否包含.rsfsc文件，点击刷新按钮重新扫描

**Q: 切换文件后配置丢失**
A: 切换前请先保存当前配置，切换会自动加载新文件

**Q: 保存失败**
A: 检查文件权限，确保对工作空间文件夹有写入权限

**Q: 文件列表不更新**
A: 点击刷新按钮，或重新选择工作空间文件夹

### 日志信息
系统会在日志中记录：
- 工作空间选择操作
- 文件扫描结果
- 文件加载成功/失败
- 保存操作结果

查看日志可以帮助诊断问题。

## 🎉 **总结**

工作空间多文件管理功能大大提升了测试流程的管理效率：
- ✅ 一键切换不同测试配置
- ✅ 统一管理相关测试文件
- ✅ 简化保存和文件操作
- ✅ 提高团队协作效率

这个功能特别适合需要管理多个测试场景、多个设备配置或多个版本测试的用户。
