﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H
#include "flow_editor/side_panel/side_panel_node_widget.h"
#include "property/display_switcher_widget.h"
#include "ptr_container.h"
#include "visualization/visualization_display_widget.h"
#include <QString>
#include <QtCore/QObject>
#include <QtWidgets/QMainWindow>
#include <cstddef>
#include <memory>

class QPushButton;
class QSplitter;
class LogTestStatus;
class QCloseEvent;
class QDockWidget;
class QAction;
class QComboBox;
class QLabel;

namespace robosense::lidar
{
class WidgetStatusbar;
class WidgetAbout;
class PropertyView;
class TestFlowEditor;
class SidePanelWidget;
class OneToXUi;
class VisualizationDisplayWidget;
namespace rsfsc_lib
{
class MessageBrowser;
}  // namespace rsfsc_lib

using MainWindowPtr = comm::PtrContainer<PropertyView, TestFlowEditor, SidePanelWidget>;

class MainWindow : public QMainWindow
{
  Q_OBJECT

public:
  explicit MainWindow(QWidget* _parent = nullptr);
  ~MainWindow() override;

  MainWindow(const MainWindow&)            = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  MainWindow(MainWindow&&)                 = delete;
  MainWindow& operator=(MainWindow&&)      = delete;

  static constexpr int restartCode() { return -112233550; }
  void closeEvent(QCloseEvent* _event) override;
  [[nodiscard]] OneToXUi* oneToXUi(const std::size_t& _index) const;
  [[nodiscard]] PropertyView* propertyView() const;
  [[nodiscard]] VisualizationDisplayWidget* visualizationDisplayWidget() const;

Q_SIGNALS:
  void signalAddPluginNodes(const robosense::lidar::NodeInfos& _node_infos);

private Q_SLOTS:
  void slotNewBt();
  void slotOpenFile();
  void slotSaveFile();
  void slotSaveFileAs();
  void slotLoadPlugins();
  void slotParamSetting();
  void slotAbout();
  void slotStartTest();
  void slotReorderNode();
  void slotRestart();
  void slotSelectWorkspaceFolder();
  void slotFileSelectionChanged(int _index);
  void slotNodeSelected(std::size_t _node_id);
  void slotNodeCreated(std::size_t _node_id);
  void slotNodeDeleted(std::size_t _node_id);

private:
  void createActions();
  void createDockWindows();
  void initPtr();
  void allConnect();
  void cleanBeforeQuit();
  void initParamSettings();
  void createLidarParameters(int _lidar_count);
  void setupOneToXUi();

  // 自动保存配置
  void autoSaveConfig();

  // 加载配置
  bool loadConfig(const QString& _file_path);
  bool loadConfigWithLidarCount(const std::string& _json);

  // 工作空间文件管理
  void setupWorkspaceFileSelector();
  void refreshFileList();

  // 事件过滤器
  bool eventFilter(QObject* _watched, QEvent* _event) override;

  // 菜单动作
  QAction* new_action_ { nullptr };
  QAction* open_action_ { nullptr };
  QAction* save_action_ { nullptr };
  QAction* save_as_action_ { nullptr };
  QAction* exit_action_ { nullptr };
  QAction* load_plugins_action_ { nullptr };
  QAction* param_setting_action_ { nullptr };
  QAction* about_action_ { nullptr };
  QAction* reorder_action_ { nullptr };
  QAction* select_workspace_action_ { nullptr };

  QPushButton* pushbutton_start_test_ { nullptr };

  // 停靠窗口
  QDockWidget* nodes_dock_ { nullptr };
  QDockWidget* ctrl_dock_ { nullptr };
  QDockWidget* properties_dock_ { nullptr };
  DisplaySwitcherWidget* display_switcher_widget_ { nullptr };
  QDockWidget* visualization_dock_ { nullptr };
  VisualizationDisplayWidget* visualization_display_widget_ { nullptr };
  QDockWidget* monitor_dock_ { nullptr };

  QSplitter* splitter_display_ { nullptr };
  rsfsc_lib::MessageBrowser* browser_message_ { nullptr };
  std::unique_ptr<MainWindowPtr> ptr_ { nullptr };
  std::vector<std::unique_ptr<OneToXUi>> all_one_to_x_ui_;

  // 当前文件路径
  QString current_file_path_;

  // 自动保存配置的文件路径
  QString auto_save_config_path_;

  // 是否正在加载配置（防止加载时触发自动保存）
  bool is_loading_config_ { false };

  int lidar_count_ { 1 };

  // 工作空间文件管理
  QString workspace_folder_path_;
  QComboBox* file_selector_combo_ { nullptr };
  QLabel* workspace_label_ { nullptr };
  QStringList rsfsc_files_;
};

}  // namespace robosense::lidar

#endif  // MAIN_WINDOW_H
