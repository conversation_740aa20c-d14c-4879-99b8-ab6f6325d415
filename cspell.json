// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "absdiff",
    "adcin",
    "Adjustn",
    "<PERSON><PERSON>",
    "Antialiasing",
    "AUTOSAR",
    "behaviortree",
    "bootsrap",
    "bugprone",
    "CALIB",
    "chardet",
    "CICD",
    "combobox",
    "controll",
    "cpack",
    "DIFOP",
    "doip",
    "dspinbox",
    "dtags",
    "EEPROM",
    "Eigen",
    "elradfmw",
    "eMMRHDZJ",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "GPIO",
    "groupbox",
    "gtest",
    "HAIKANG",
    "hexfile",
    "hhmmss",
    "hicpp",
    "imread",
    "imwrite",
    "Interactor",
    "iselabscn",
    "Liang",
    "libqt5serialport5",
    "librsfsc",
    "lineedit",
    "loguru",
    "LPTOP",
    "lrelease",
    "lsusb",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "METATYPE",
    "mosi",
    "MSOP",
    "munubar",
    "muxh",
    "nodeeditor",
    "NOLINT",
    "NOLINTNEXTLINE",
    "NOPASSWD",
    "opencv",
    "OPENMP",
    "pcap",
    "Pixmap",
    "PointXYZI",
    "polylines",
    "Postback",
    "psdp",
    "pyftpdlib",
    "qgraphicsitem",
    "QMESSAGE",
    "qobject",
    "qreal",
    "qsetting",
    "qsettings",
    "QVTKWidget",
    "rheight",
    "robosense",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "rwidth",
    "Shen",
    "SHIYAN",
    "spdlog",
    "suteng",
    "sysp",
    "tablewidget",
    "tabwidget",
    "thorlabs",
    "THRESH_OTSU",
    "tmon",
    "TSENSOR",
    "udev",
    "uintless",
    "unitless",
    "usbtmc",
    "utest",
    "VBIAS",
    "VCSEL",
    "vefp",
    "verfer",
    "vmon",
    "vptat",
    "widgetaction",
    "YAMLCPP",
    "Ying",
    "Zhang",
    "ZHONG",
    "Coro"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
