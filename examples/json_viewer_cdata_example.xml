<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="JsonViewerCDataExample">
        <Sequence name="MainSequence">
            
            <!-- 示例1: 使用CDATA包装包含特殊字符的JSON数据 -->
            <JsonViewerNode name="SpecialCharactersJson"
                json_data='<![CDATA[{
  "device_info": {
    "name": "Sensor <Model-X>",
    "description": "High-precision sensor with <special> characters & symbols",
    "formula": "value > threshold && value < max_limit",
    "tags": ["<critical>", "&important", "\"priority\""]
  },
  "measurements": {
    "temperature": 25.6,
    "humidity": 68.2,
    "pressure": 1013.25,
    "voltage": 3.3
  }
}]]>'
                enable_visualization="true"
                visualization_mode="auto"
                widget_type="NumericTable"
                visualization_title="特殊字符JSON数据"
                visualization_duration="5000"
                data_path="measurements"
                min_value="0"
                max_value="1100"
                decimal_places="2"/>
            
            <!-- 示例2: 使用CDATA包装复杂嵌套的JSON数据 -->
            <JsonViewerNode name="ComplexNestedJson"
                json_data='<![CDATA[{
  "system_config": {
    "database": {
      "connection_string": "server=localhost;database=test;uid=user;pwd=****;",
      "query_template": "SELECT * FROM table WHERE id > {id} AND status = \"active\""
    },
    "api_endpoints": [
      {
        "name": "user_api",
        "url": "https://api.example.com/users?filter={name}&sort=asc",
        "headers": {
          "Authorization": "Bearer <token>",
          "Content-Type": "application/json"
        }
      }
    ]
  },
  "performance_metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 67.8,
    "disk_io": 123.4,
    "network_throughput": 89.5
  }
}]]>'
                enable_visualization="true"
                visualization_mode="numeric"
                widget_type="NumericTable"
                visualization_title="复杂嵌套JSON数据"
                visualization_duration="6000"
                data_path="performance_metrics"
                min_value="0"
                max_value="200"
                decimal_places="1"/>
            
            <!-- 示例3: 使用CDATA包装包含XML标签的JSON数据 -->
            <JsonViewerNode name="XmlTagsJson"
                json_data='<![CDATA[{
  "xml_content": {
    "raw_xml": "<root><item id=\"1\"><name>Test</name><value>100</value></item></root>",
    "xpath_queries": [
      "//item[@id=\"1\"]/name",
      "//item[value > 50]"
    ]
  },
  "sensor_readings": {
    "temperature": 28.5,
    "humidity": 72.1,
    "light_level": 456.7
  },
  "validation_rules": {
    "temperature_rule": "temp >= 0 && temp <= 100",
    "humidity_rule": "humidity > 0 && humidity < 100"
  }
}]]>'
                enable_visualization="true"
                visualization_mode="auto"
                widget_type="NumericTable"
                visualization_title="包含XML标签的JSON数据"
                visualization_duration="4000"
                data_path="sensor_readings"
                min_value="0"
                max_value="500"
                decimal_places="1"/>
            
        </Sequence>
    </BehaviorTree>
</root>
