#include "behaviortree_cpp/bt_factory.h"
#include <iostream>
#include <rsfsc_log/rsfsc_log_macro.h>

using namespace BT;
using namespace robosense::lidar;

// 示例 XML，展示 Lua 脚本与黑板的交互
static const char* xml_text = R"(
<root BTCPP_format="4">
    <BehaviorTree ID="LuaBlackboardDemo">
        <Sequence>
            <!-- 初始化一些黑板数据 -->
            <Script code="counter = 0; message = 'Hello from BT Script'" />
            
            <!-- 使用 Lua 脚本读取和修改黑板数据 -->
            <LuaScript script="
                -- 从黑板读取数据
                local current_counter = bb_get_int('counter')
                local current_message = bb_get('message')
                
                -- 打印当前值
                log('Current counter: ' .. current_counter, 'info')
                log('Current message: ' .. current_message, 'info')
                
                -- 修改黑板数据
                bb_set_int('counter', current_counter + 10)
                bb_set('message', 'Modified by <PERSON><PERSON>!')
                bb_set_bool('lua_executed', true)
                
                -- 返回成功状态
                return SUCCESS
            " result="{lua_result}" />
            
            <!-- 验证 Lua 脚本的修改 -->
            <Script code="
                if counter == 10 and message == 'Modified by Lua!' and lua_executed == true then
                    print('✅ Lua script successfully modified blackboard data!')
                    print('Counter: ' + counter)
                    print('Message: ' + message)
                    print('Lua executed: ' + lua_executed)
                else
                    print('❌ Blackboard data was not modified correctly')
                end
            " />
            
            <!-- 使用异步 Lua 脚本进行更复杂的操作 -->
            <LuaAsyncScript script="
                -- 模拟一些计算
                local sum = 0
                for i = 1, 100 do
                    sum = sum + i
                end
                
                -- 将结果存储到黑板
                bb_set_int('calculated_sum', sum)
                bb_set('calculation_status', 'completed')
                
                log('Calculated sum: ' .. sum, 'info')
                return SUCCESS
            " result="{async_result}" />
            
            <!-- 最终验证 -->
            <Script code="
                print('Final verification:')
                print('Counter: ' + counter)
                print('Message: ' + message)
                print('Calculated sum: ' + calculated_sum)
                print('Calculation status: ' + calculation_status)
            " />
        </Sequence>
    </BehaviorTree>
</root>
)";

int main()
{
  try
  {
    // 创建 BehaviorTree 工厂
    BehaviorTreeFactory factory;

    // 注册 Lua 脚本节点
    registerLuaScriptNodes(factory);

    std::cout << "=== Lua 脚本与黑板交互示例 ===" << std::endl;
    std::cout << "这个示例展示了如何使用 Lua 脚本访问和修改 BehaviorTree 黑板数据" << std::endl;
    std::cout << std::endl;

    // 创建黑板
    auto blackboard = Blackboard::create();

    // 从 XML 创建行为树
    auto tree = factory.createTreeFromText(xml_text, blackboard);

    std::cout << "开始执行行为树..." << std::endl;
    std::cout << std::endl;

    // 执行行为树
    NodeStatus status = tree.tickWhileRunning();

    std::cout << std::endl;
    std::cout << "行为树执行完成，状态: ";
    switch (status)
    {
    case NodeStatus::SUCCESS: std::cout << "SUCCESS ✅" << std::endl; break;
    case NodeStatus::FAILURE: std::cout << "FAILURE ❌" << std::endl; break;
    case NodeStatus::RUNNING: std::cout << "RUNNING 🔄" << std::endl; break;
    }

    std::cout << std::endl;
    std::cout << "=== 最终黑板状态 ===" << std::endl;

    // 打印最终的黑板状态
    try
    {
      std::cout << "counter: " << blackboard->get<int>("counter") << std::endl;
      std::cout << "message: " << blackboard->get<std::string>("message") << std::endl;
      std::cout << "lua_executed: " << (blackboard->get<bool>("lua_executed") ? "true" : "false") << std::endl;
      std::cout << "calculated_sum: " << blackboard->get<int>("calculated_sum") << std::endl;
      std::cout << "calculation_status: " << blackboard->get<std::string>("calculation_status") << std::endl;
    }
    catch (const std::exception& e)
    {
      std::cout << "读取黑板数据时出错: " << e.what() << std::endl;
    }

    return 0;
  }
  catch (const std::exception& e)
  {
    std::cerr << "示例执行失败: " << e.what() << std::endl;
    return 1;
  }
}

/* 预期输出:
=== Lua 脚本与黑板交互示例 ===
这个示例展示了如何使用 Lua 脚本访问和修改 BehaviorTree 黑板数据

开始执行行为树...

Current counter: 0
Current message: Hello from BT Script
✅ Lua script successfully modified blackboard data!
Counter: 10
Message: Modified by Lua!
Lua executed: true
Calculated sum: 5050
Final verification:
Counter: 10
Message: Modified by Lua!
Calculated sum: 5050
Calculation status: completed

行为树执行完成，状态: SUCCESS ✅

=== 最终黑板状态 ===
counter: 10
message: Modified by Lua!
lua_executed: true
calculated_sum: 5050
calculation_status: completed
*/
