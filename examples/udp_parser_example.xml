<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- UdpDataParserNode 使用示例 -->
    <BehaviorTree ID="UdpDataProcessing">
        <Sequence>
            <!-- UDP数据解析节点 -->
            <UdpDataParserNode
                udp_ip="0.0.0.0"
                udp_port="6699"
                packet_length="16"
                timeout_ms="2000"
                auto_start="true"
                bb_prefix="udp"
                parser_script="
                    -- 解析UDP数据包
                    log('接收到数据: ' .. data_length .. ' 字节', 'info')
                    
                    -- 清空输出
                    clear_output()
                    
                    if data_length >= 8 then
                        -- 解析数据包头部
                        local msg_id = read_u8(0) * 256 + read_u8(1)
                        local msg_type = read_u8(2)
                        local status = read_u8(3)
                        
                        -- 解析数据内容
                        local value1 = read_u8(4) * 256 + read_u8(5)
                        local value2 = read_u8(6)
                        
                        -- 存储到黑板
                        bb_set_int('msg_id', msg_id)
                        bb_set_int('msg_type', msg_type)
                        bb_set_int('status', status)
                        bb_set_int('value1', value1)
                        bb_set_int('value2', value2)
                        
                        -- 设置自定义输出
                        set_output('msg_id', tostring(msg_id))
                        set_output('msg_type', tostring(msg_type))
                        set_output('status', status == 1 and 'ACTIVE' or 'INACTIVE')
                        set_output('value1', tostring(value1))
                        set_output('value2', tostring(value2))
                        
                        log('解析完成 - ID: ' .. msg_id .. ', 类型: ' .. msg_type, 'info')
                    else
                        set_output('error', 'PACKET_TOO_SHORT')
                        log('数据包太短', 'error')
                    end
                    
                    return OK
                "
                parse_result="{result}"
                output_json="{parsed_data}"
                data_available="{has_data}"
                timeout_occurred="{timeout}" />
        </Sequence>
    </BehaviorTree>
</root>
