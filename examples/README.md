# UdpDataParserNode 使用示例

## 示例文件
- `udp_parser_example.xml` - 完整的使用示例

## 核心配置

### 节点配置
```xml
<UdpDataParserNode
    udp_ip="0.0.0.0"              <!-- UDP监听IP -->
    udp_port="6699"               <!-- UDP监听端口 -->
    packet_length="16"            <!-- 期望数据包长度 -->
    timeout_ms="2000"             <!-- 接收超时时间 -->
    auto_start="true"             <!-- 是否自动启动 -->
    bb_prefix="udp"               <!-- 黑板键前缀 -->
    parser_script="..."           <!-- Lua解析脚本 -->
    
    <!-- 输出端口 -->
    parse_result="{result}"       <!-- 解析状态 -->
    output_json="{parsed_data}"   <!-- 自定义输出JSON -->
    data_available="{has_data}"   <!-- 是否有新数据 -->
    timeout_occurred="{timeout}"  <!-- 是否超时 -->
/>
```

### Lua 脚本模板
```lua
-- 清空输出
clear_output()

if data_length >= 8 then
    -- 解析数据
    local msg_id = read_u8(0) * 256 + read_u8(1)
    local msg_type = read_u8(2)
    
    -- 存储到黑板（自动添加前缀）
    bb_set_int('msg_id', msg_id)
    bb_set_int('msg_type', msg_type)
    
    -- 设置自定义输出
    set_output('msg_id', tostring(msg_id))
    set_output('msg_type', tostring(msg_type))
else
    set_output('error', 'PACKET_TOO_SHORT')
end

return OK
```

## 其他节点使用输出

### 方式1: JSON输出（推荐）
```javascript
if (has_data == 'true') {
    var data = JSON.parse(parsed_data);
    console.log('消息ID: ' + data.msg_id);
    console.log('消息类型: ' + data.msg_type);
}
```

### 方式2: 黑板数据
```javascript
var msgId = blackboard.get('udp.msg_id');
var msgType = blackboard.get('udp.msg_type');
```

## 测试方法

### 发送测试数据
```bash
# 发送测试数据包: ID=258, 类型=1, 状态=1, 数值1=1000, 数值2=50
echo -n -e '\x01\x02\x01\x01\x03\xE8\x32\x00\x00\x00\x00\x00\x00\x00\x00\x00' | nc -u 127.0.0.1 6699
```

### 运行示例
```bash
./build/fixture_test examples/udp_parser_example.xml
```

## 关键特性

- ✅ **自定义输出**: 通过 `set_output()` 设置结构化数据
- ✅ **黑板存储**: 自动添加前缀，避免键名冲突
- ✅ **解析辅助**: `read_u8()`, `slice_hex()` 等函数
- ✅ **错误处理**: 完善的超时和错误处理机制
- ✅ **灵活使用**: 支持JSON输出和黑板访问两种方式
