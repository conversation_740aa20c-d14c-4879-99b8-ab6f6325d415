#include "behaviortree_cpp/bt_factory.h"
#include "src/common/node_models/json_viewer_node.h"
#include "src/common/visualization/visualization_manager.h"
#include "src/common/visualization/widgets/numeric_gauge_widget.h"
#include <QApplication>
#include <QFile>
#include <QTextStream>
#include <chrono>
#include <iostream>
#include <thread>

using namespace BT;
using namespace robosense::lidar;

// 创建测试JSON文件
void createTestJsonFile()
{
  QFile file("test_data.json");
  if (file.open(QIODevice::WriteOnly))
  {
    QTextStream stream(&file);
    stream << R"({
    "test_data": {
        "version": "1.0",
        "created": "2024-01-15T10:30:00Z",
        "data": [
            {"id": 1, "name": "测试项目1", "value": 100},
            {"id": 2, "name": "测试项目2", "value": 200},
            {"id": 3, "name": "测试项目3", "value": 300}
        ],
        "settings": {
            "debug": true,
            "timeout": 5000,
            "retries": 3
        }
    }
})";
    file.close();
    std::cout << "测试JSON文件创建完成: test_data.json" << std::endl;
  }
}

int main(int argc, char* argv[])
{
  QApplication app(argc, argv);

  try
  {
    // 创建 BehaviorTree 工厂
    BT::BehaviorTreeFactory factory;

    // 注册 JSON 查看器节点
    factory.registerNodeType<JsonViewerNode>("JsonViewerNode");

    // 注册可视化组件
    std::cout << "🚀 注册可视化组件..." << std::endl;
    UnifiedVisualizationManager::instance().registerVisualizationWidget(
      "NumericGauge", QStringList { "numeric", "voltage", "current", "temperature", "sensor_data" },
      []() { return std::make_unique<NumericGaugeWidget>(); });

    UnifiedVisualizationManager::instance().registerVisualizationWidget(
      "NumericTable", QStringList { "numeric", "voltage", "current", "temperature", "sensor_data" },
      []() { return std::make_unique<NumericGaugeWidget>(); });

    std::cout << "✅ 可视化组件注册完成" << std::endl;
    std::cout << std::endl;

    std::cout << "=== JSON查看器节点示例 ===" << std::endl;
    std::cout << "这个示例展示了如何使用JsonViewerNode处理和显示JSON数据" << std::endl;
    std::cout << std::endl;

    // 创建测试文件
    createTestJsonFile();

    // 从 XML 文件创建行为树
    auto tree = factory.createTreeFromFile("json_viewer_example.xml");

    std::cout << "🚀 开始执行JSON处理流程..." << std::endl;
    std::cout << "⏱️  将运行15秒，然后自动停止" << std::endl;
    std::cout << std::endl;

    // 运行行为树15秒
    auto start_time       = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::seconds(15);

    while (std::chrono::steady_clock::now() - start_time < timeout_duration)
    {
      BT::NodeStatus status = tree.tickOnce();

      // 短暂休眠，避免CPU占用过高
      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      // 如果行为树完成（成功或失败），则继续下一轮
      if (status != BT::NodeStatus::RUNNING)
      {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
    }

    std::cout << std::endl;
    std::cout << "⏹️  JSON查看器示例运行完成" << std::endl;

    return 0;
  }
  catch (const std::exception& e)
  {
    std::cerr << "示例执行失败: " << e.what() << std::endl;
    return 1;
  }
}

/* 
这个示例展示了JsonViewerNode的以下功能:

1. **JSON字符串处理**:
   - 直接处理JSON字符串数据
   - 自动弹窗显示JSON树形结构
   - 支持自定义窗口标题和大小

2. **JSON文件加载**:
   - 从文件加载JSON数据
   - 支持静默处理（不显示界面）
   - 错误处理和状态反馈

3. **Widget嵌入模式**:
   - 尝试嵌入到指定的Widget中
   - 如果找不到目标Widget则回退到弹窗模式
   - 支持可编辑和只读模式

4. **高级配置**:
   - 自定义异常列表（忽略特定键名）
   - 自动展开控制
   - 样式自定义

5. **与其他节点集成**:
   - 可以接收UDP解析器的JSON输出
   - 支持条件显示
   - 完整的错误处理和状态反馈

使用方法:
1. 编译程序: make json_viewer_example
2. 运行程序: ./json_viewer_example
3. 观察JSON数据的树形显示界面

注意事项:
- 需要Qt环境支持
- 确保QJsonModel库已正确链接
- 可以通过修改XML配置来测试不同的显示模式
*/
