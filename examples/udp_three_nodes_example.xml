<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <BehaviorTree ID="UdpThreeNodesExample">
        <Sequence name="MainSequence">
            
            <!-- 步骤1: 启动UDP接收器 -->
            <UdpStartNode name="StartUdpReceiver"
                         udp_ip="0.0.0.0"
                         udp_port="6699"
                         packet_length="1024"
                         timeout_ms="1000"
                         group_ip=""
                         receiver_id="sensor_data"/>
            
            <!-- 步骤2: 采集和解析UDP数据 -->
            <UdpCollectNode name="CollectSensorData"
                           udp_handle="{udp_handle}"
                           packet_length="1024"
                           timeout_ms="2000"
                           parser_script='<![CDATA[-- UDP传感器数据解析脚本
-- 假设接收到的是传感器数据包
-- 数据格式: [header(4字节)] [temperature(4字节)] [humidity(4字节)] [pressure(4字节)] [checksum(4字节)]

log("开始解析UDP传感器数据，长度: " .. data_length, "info")

if data_length < 20 then
    log("数据长度不足，期望至少20字节", "error")
    return "FAILURE"
end

-- 解析温度数据 (字节5-8，假设为IEEE 754 float)
local temp_bytes = {}
for i = 5, 8 do
    temp_bytes[#temp_bytes + 1] = data_bytes[i]
end

-- 解析湿度数据 (字节9-12)
local humidity_bytes = {}
for i = 9, 12 do
    humidity_bytes[#humidity_bytes + 1] = data_bytes[i]
end

-- 解析压力数据 (字节13-16)
local pressure_bytes = {}
for i = 13, 16 do
    pressure_bytes[#pressure_bytes + 1] = data_bytes[i]
end

-- 简化的数据解析（实际应用中需要正确的字节序转换）
local temperature = (temp_bytes[1] + temp_bytes[2] * 256) / 100.0
local humidity = (humidity_bytes[1] + humidity_bytes[2] * 256) / 100.0
local pressure = (pressure_bytes[1] + pressure_bytes[2] * 256 + pressure_bytes[3] * 65536) / 100.0

log("解析结果 - 温度: " .. temperature .. "°C, 湿度: " .. humidity .. "%, 压力: " .. pressure .. "hPa", "info")

-- 存储到黑板
bb_set("temperature", tostring(temperature), "float")
bb_set("humidity", tostring(humidity), "float")
bb_set("pressure", tostring(pressure), "float")
bb_set("data_timestamp", tostring(os.time()), "string")

-- 设置输出数据
set_output("temperature", tostring(temperature))
set_output("humidity", tostring(humidity))
set_output("pressure", tostring(pressure))
set_output("status", "parsed")

return "SUCCESS"]]>'
                           bb_prefix="sensor"/>
            
            <!-- 步骤3: 再次采集数据（演示多次采集） -->
            <UdpCollectNode name="CollectMoreData"
                           udp_handle="{udp_handle}"
                           packet_length="1024"
                           timeout_ms="1500"
                           parser_script='<![CDATA[-- 第二次数据采集和简单处理
log("第二次数据采集，数据长度: " .. data_length, "info")

-- 简单的数据统计
local byte_sum = 0
for i = 1, math.min(data_length, 100) do
    byte_sum = byte_sum + data_bytes[i]
end

local average_byte = byte_sum / math.min(data_length, 100)

log("前100字节平均值: " .. average_byte, "info")

-- 存储统计信息
bb_set("byte_average", tostring(average_byte), "float")
bb_set("sample_count", "2", "int")

-- 设置输出
set_output("byte_average", tostring(average_byte))
set_output("collection_round", "2")

return "SUCCESS"]]>'
                           bb_prefix="stats"/>
            
            <!-- 步骤4: 停止UDP接收器 -->
            <UdpStopNode name="StopUdpReceiver"
                        udp_handle="{udp_handle}"/>
            
        </Sequence>
    </BehaviorTree>
</root>
