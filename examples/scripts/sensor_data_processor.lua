-- 传感器数据处理脚本示例
-- 这个脚本展示了如何在 BehaviorTree 中使用 Lua 处理复杂的传感器数据

-- 从黑板获取传感器数据
local sensor_x = bb_get_double('sensor_x') or 0.0
local sensor_y = bb_get_double('sensor_y') or 0.0
local sensor_z = bb_get_double('sensor_z') or 0.0

log('Processing sensor data: x=' .. sensor_x .. ', y=' .. sensor_y .. ', z=' .. sensor_z, 'info')

-- 计算传感器数据的模长
local magnitude = math.sqrt(sensor_x * sensor_x + sensor_y * sensor_y + sensor_z * sensor_z)

-- 检查数据是否在正常范围内
local min_threshold = bb_get_double('min_threshold') or 0.1
local max_threshold = bb_get_double('max_threshold') or 10.0

local status_message = ""
local result_status = SUCCESS

if magnitude < min_threshold then
    status_message = "传感器数据过小，可能存在故障"
    result_status = FAILURE
    log('Sensor data too small: ' .. magnitude, 'warn')
elseif magnitude > max_threshold then
    status_message = "传感器数据过大，可能存在异常"
    result_status = FAILURE
    log('Sensor data too large: ' .. magnitude, 'warn')
else
    status_message = "传感器数据正常"
    result_status = SUCCESS
    log('Sensor data normal: ' .. magnitude, 'info')
end

-- 计算归一化向量
local norm_x = 0.0
local norm_y = 0.0
local norm_z = 0.0

if magnitude > 0.001 then
    norm_x = sensor_x / magnitude
    norm_y = sensor_y / magnitude
    norm_z = sensor_z / magnitude
end

-- 将处理结果写回黑板
bb_set_double('sensor_magnitude', magnitude)
bb_set_double('normalized_x', norm_x)
bb_set_double('normalized_y', norm_y)
bb_set_double('normalized_z', norm_z)
bb_set('sensor_status', status_message)

-- 更新处理计数器
local process_count = bb_get_int('process_count') or 0
bb_set_int('process_count', process_count + 1)

-- 记录处理时间戳
bb_set('last_processed_time', os.date('%Y-%m-%d %H:%M:%S'))

log('Sensor data processed successfully. Count: ' .. (process_count + 1), 'info')

-- 返回处理结果状态
return result_status
