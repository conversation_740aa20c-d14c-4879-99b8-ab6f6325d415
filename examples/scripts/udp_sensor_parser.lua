-- UDP传感器数据解析脚本
-- 这个脚本展示了如何解析来自传感器的UDP数据包

log('=== 传感器UDP数据解析开始 ===', 'info')
log('接收数据长度: ' .. data_length .. ' 字节', 'info')

-- 检查数据包最小长度
if data_length < 16 then
    log('数据包长度不足，期望至少16字节，实际: ' .. data_length, 'error')
    bb_set('parse_error', 'Packet too short')
    return NG
end

-- 解析数据包头部 (前4字节: 魔数)
local magic_number = 0
for i = 1, 4 do
    magic_number = magic_number * 256 + data_bytes[i]
end

log('魔数: 0x' .. string.format('%08X', magic_number), 'info')

-- 验证魔数
if magic_number ~= 0x12345678 then
    log('无效的魔数，期望: 0x12345678，实际: 0x' .. string.format('%08X', magic_number), 'error')
    bb_set('parse_error', 'Invalid magic number')
    return NG
end

-- 解析版本号 (第5字节)
local version = data_bytes[5]
log('协议版本: ' .. version, 'info')
bb_set_int('protocol_version', version)

-- 解析数据类型 (第6字节)
local data_type = data_bytes[6]
log('数据类型: 0x' .. string.format('%02X', data_type), 'info')

local type_name = 'UNKNOWN'
if data_type == 0x01 then
    type_name = 'TEMPERATURE'
elseif data_type == 0x02 then
    type_name = 'HUMIDITY'
elseif data_type == 0x03 then
    type_name = 'PRESSURE'
elseif data_type == 0x04 then
    type_name = 'ACCELERATION'
elseif data_type == 0x05 then
    type_name = 'GYROSCOPE'
end

bb_set('sensor_type', type_name)
log('传感器类型: ' .. type_name, 'info')

-- 解析序列号 (第7-8字节)
local sequence = data_bytes[7] * 256 + data_bytes[8]
bb_set_int('sequence_number', sequence)
log('序列号: ' .. sequence, 'info')

-- 解析时间戳 (第9-12字节, 大端序)
local timestamp = 0
for i = 9, 12 do
    timestamp = timestamp * 256 + data_bytes[i]
end
bb_set_int('timestamp', timestamp)
log('时间戳: ' .. timestamp, 'info')

-- 解析传感器数据 (第13-16字节, IEEE 754 单精度浮点数的简化处理)
-- 注意: 这里为了简化，我们将4个字节作为整数处理
local sensor_data_raw = 0
for i = 13, 16 do
    sensor_data_raw = sensor_data_raw * 256 + data_bytes[i]
end

-- 简单的数据转换 (实际应用中需要正确的IEEE 754转换)
local sensor_value = sensor_data_raw / 1000000.0
bb_set_double('sensor_value', sensor_value)
log('传感器值: ' .. string.format('%.6f', sensor_value), 'info')

-- 数据有效性检查
local is_valid = true
local error_msg = ''

-- 检查版本号
if version > 3 then
    is_valid = false
    error_msg = error_msg .. 'Unsupported version; '
end

-- 检查序列号连续性
local last_sequence = bb_get_int('last_sequence_number') or 0
if sequence ~= 0 and last_sequence ~= 0 then
    local expected_sequence = (last_sequence + 1) % 65536
    if sequence ~= expected_sequence then
        log('序列号不连续，期望: ' .. expected_sequence .. '，实际: ' .. sequence, 'warn')
        bb_set_int('sequence_gaps', bb_get_int('sequence_gaps') + 1)
    end
end
bb_set_int('last_sequence_number', sequence)

-- 检查传感器值范围
if type_name == 'TEMPERATURE' and (sensor_value < -50 or sensor_value > 150) then
    is_valid = false
    error_msg = error_msg .. 'Temperature out of range; '
elseif type_name == 'HUMIDITY' and (sensor_value < 0 or sensor_value > 100) then
    is_valid = false
    error_msg = error_msg .. 'Humidity out of range; '
elseif type_name == 'PRESSURE' and (sensor_value < 0 or sensor_value > 2000) then
    is_valid = false
    error_msg = error_msg .. 'Pressure out of range; '
end

-- 更新统计信息
local total_packets = bb_get_int('total_sensor_packets') + 1
bb_set_int('total_sensor_packets', total_packets)

if is_valid then
    local valid_packets = bb_get_int('valid_sensor_packets') + 1
    bb_set_int('valid_sensor_packets', valid_packets)
    bb_set('last_valid_data_time', os.date('%Y-%m-%d %H:%M:%S'))
    
    -- 计算数据质量
    local quality = (valid_packets / total_packets) * 100
    bb_set_double('data_quality_percent', quality)
    
    log('数据验证通过', 'info')
    log('数据质量: ' .. string.format('%.1f%%', quality) .. ' (' .. valid_packets .. '/' .. total_packets .. ')', 'info')
else
    local invalid_packets = bb_get_int('invalid_sensor_packets') + 1
    bb_set_int('invalid_sensor_packets', invalid_packets)
    bb_set('parse_error', error_msg)
    
    log('数据验证失败: ' .. error_msg, 'error')
end

-- 存储解析结果
bb_set('parse_status', is_valid and 'SUCCESS' or 'FAILED')
bb_set('raw_hex_data', data_hex)

-- 如果有额外的数据，记录下来
if data_length > 16 then
    local extra_bytes = data_length - 16
    log('检测到额外数据: ' .. extra_bytes .. ' 字节', 'info')
    bb_set_int('extra_data_bytes', extra_bytes)
end

log('=== 传感器UDP数据解析完成 ===', 'info')

-- 返回解析结果
return is_valid and OK or NG
