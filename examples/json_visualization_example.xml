<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
    <!-- JSON数据可视化示例 -->
    <BehaviorTree ID="JsonVisualizationExample">
        <Sequence name="JSON可视化流程">
            
            <!-- 示例1: 传感器数据可视化 -->
            <JsonViewerNode name="传感器数据可视化"
                          json_data='<![CDATA[{
  "sensor_data": {
    "temperature": 25.6,
    "humidity": 68.2,
    "pressure": 1013.25,
    "voltage": 3.3,
    "current": 0.85
  },
  "status": {
    "power_on": true,
    "sensor_ready": true,
    "error_count": 0
  }
}]]>'
                          enable_visualization="true"
                          visualization_mode="numeric"
                          widget_type="NumericTable"
                          visualization_title="环境传感器数据"
                          visualization_duration="6000"
                          data_path="sensor_data"
                          min_value="0.0"
                          max_value="100.0"
                          decimal_places="2"/>
            
            <!-- 示例2: 设备状态可视化 -->
            <JsonViewerNode name="设备状态可视化"
                          json_data='{
                            "device_status": {
                              "motor_running": true,
                              "sensor_active": true,
                              "communication_ok": false,
                              "temperature_normal": true,
                              "error_code": 0,
                              "uptime_hours": 24.5
                            }
                          }'
                          enable_visualization="true"
                          visualization_mode="status"
                          widget_type="NumericTable"
                          visualization_title="设备运行状态"
                          visualization_duration="5000"
                          data_path="device_status"
                          min_value="0.0"
                          max_value="1.0"
                          decimal_places="0"/>
            
            <!-- 示例3: 仪表盘显示 -->
            <JsonViewerNode name="仪表盘显示"
                          json_data='{
                            "dashboard": {
                              "cpu_usage": 45.2,
                              "memory_usage": 67.8,
                              "disk_usage": 23.1,
                              "network_speed": 89.5
                            }
                          }'
                          enable_visualization="true"
                          visualization_mode="numeric"
                          widget_type="NumericGauge"
                          visualization_title="系统监控仪表盘"
                          visualization_duration="8000"
                          data_path="dashboard"
                          min_value="0.0"
                          max_value="100.0"
                          decimal_places="1"/>
            
            <!-- 示例4: 自动模式 - 根据组件类型自动选择 -->
            <JsonViewerNode name="自动模式可视化"
                          json_data='{
                            "measurements": {
                              "value1": 12.34,
                              "value2": 56.78,
                              "value3": 90.12,
                              "average": 53.08
                            }
                          }'
                          enable_visualization="true"
                          visualization_mode="auto"
                          widget_type="NumericTable"
                          visualization_title="测量数据自动显示"
                          visualization_duration="4000"
                          data_path="measurements"
                          min_value="0.0"
                          max_value="100.0"
                          decimal_places="2"/>
                          
        </Sequence>
    </BehaviorTree>
</root>
