#include "behaviortree_cpp/bt_factory.h"
#include <iostream>
#include <random>

using namespace BT;
using namespace robosense::lidar;

// 自定义节点：传感器数据生成器
class SensorDataGenerator : public SyncActionNode
{
public:
  SensorDataGenerator(const std::string& name, const NodeConfig& config) :
    SyncActionNode(name, config), generator_(std::random_device {}())
  {}

  static PortsList providedPorts()
  {
    return { OutputPort<double>("sensor_x"), OutputPort<double>("sensor_y"), OutputPort<double>("sensor_z") };
  }

  NodeStatus tick() override
  {
    // 生成随机传感器数据
    std::uniform_real_distribution<double> dist(-5.0, 5.0);

    double x = dist(generator_);
    double y = dist(generator_);
    double z = dist(generator_);

    // 输出到端口（这些会自动同步到黑板）
    setOutput("sensor_x", x);
    setOutput("sensor_y", y);
    setOutput("sensor_z", z);

    std::cout << "生成传感器数据: x=" << x << ", y=" << y << ", z=" << z << std::endl;

    return NodeStatus::SUCCESS;
  }

private:
  std::mt19937 generator_;
};

// 自定义节点：决策执行器
class DecisionExecutor : public SyncActionNode
{
public:
  DecisionExecutor(const std::string& name, const NodeConfig& config) : SyncActionNode(name, config) {}

  static PortsList providedPorts()
  {
    return { InputPort<std::string>("sensor_status"), InputPort<double>("sensor_magnitude"),
             InputPort<int>("process_count") };
  }

  NodeStatus tick() override
  {
    auto status    = getInput<std::string>("sensor_status");
    auto magnitude = getInput<double>("sensor_magnitude");
    auto count     = getInput<int>("process_count");

    if (status && magnitude && count)
    {
      std::cout << "执行决策:" << std::endl;
      std::cout << "  状态: " << status.value() << std::endl;
      std::cout << "  幅度: " << magnitude.value() << std::endl;
      std::cout << "  处理次数: " << count.value() << std::endl;

      // 根据传感器状态执行不同的动作
      if (status.value() == "传感器数据正常")
      {
        std::cout << "  动作: 继续正常操作" << std::endl;
        return NodeStatus::SUCCESS;
      }
      else
      {
        std::cout << "  动作: 进入安全模式" << std::endl;
        return NodeStatus::FAILURE;
      }
    }

    return NodeStatus::FAILURE;
  }
};

// 示例 XML，展示完整的传感器数据处理流程
static const char* xml_text = R"(
<root BTCPP_format="4">
    <BehaviorTree ID="SensorProcessingDemo">
        <Sequence>
            <!-- 初始化阈值参数 -->
            <Script code="
                min_threshold = 0.5;
                max_threshold = 8.0;
                process_count = 0;
            " />
            
            <!-- 重复处理多次传感器数据 -->
            <Repeat num_cycles="5">
                <Sequence>
                    <!-- 生成传感器数据 -->
                    <SensorDataGenerator sensor_x="{sensor_x}" 
                                       sensor_y="{sensor_y}" 
                                       sensor_z="{sensor_z}" />
                    
                    <!-- 使用 Lua 脚本处理传感器数据 -->
                    <LuaScript script_file="examples/scripts/sensor_data_processor.lua" 
                             result="{processing_result}" />
                    
                    <!-- 根据处理结果执行决策 -->
                    <DecisionExecutor sensor_status="{sensor_status}"
                                    sensor_magnitude="{sensor_magnitude}"
                                    process_count="{process_count}" />
                    
                    <!-- 短暂延迟 -->
                    <Sleep msec="100" />
                </Sequence>
            </Repeat>
            
            <!-- 最终统计 -->
            <LuaScript script="
                local total_count = bb_get_int('process_count')
                local last_time = bb_get('last_processed_time')
                
                log('=== 处理完成统计 ===', 'info')
                log('总处理次数: ' .. total_count, 'info')
                log('最后处理时间: ' .. last_time, 'info')
                
                bb_set('final_summary', '共处理 ' .. total_count .. ' 次传感器数据')
                
                return SUCCESS
            " result="{final_result}" />
        </Sequence>
    </BehaviorTree>
</root>
)";

int main()
{
  try
  {
    // 创建 BehaviorTree 工厂
    BehaviorTreeFactory factory;

    // 注册 Lua 脚本节点
    registerLuaScriptNodes(factory);

    // 注册自定义节点
    factory.registerNodeType<SensorDataGenerator>("SensorDataGenerator");
    factory.registerNodeType<DecisionExecutor>("DecisionExecutor");

    std::cout << "=== 传感器数据处理与 Lua 脚本集成示例 ===" << std::endl;
    std::cout << "这个示例展示了如何将 Lua 脚本集成到复杂的传感器数据处理流程中" << std::endl;
    std::cout << std::endl;

    // 创建黑板
    auto blackboard = Blackboard::create();

    // 从 XML 创建行为树
    auto tree = factory.createTreeFromText(xml_text, blackboard);

    std::cout << "开始执行传感器数据处理流程..." << std::endl;
    std::cout << std::endl;

    // 执行行为树
    NodeStatus status = tree.tickWhileRunning();

    std::cout << std::endl;
    std::cout << "处理流程完成，状态: ";
    switch (status)
    {
    case NodeStatus::SUCCESS: std::cout << "SUCCESS ✅" << std::endl; break;
    case NodeStatus::FAILURE: std::cout << "FAILURE ❌" << std::endl; break;
    case NodeStatus::RUNNING: std::cout << "RUNNING 🔄" << std::endl; break;
    }

    std::cout << std::endl;
    std::cout << "=== 最终处理结果 ===" << std::endl;

    // 打印最终结果
    try
    {
      if (blackboard->getEntry("final_summary"))
      {
        std::cout << blackboard->get<std::string>("final_summary") << std::endl;
      }

      if (blackboard->getEntry("last_processed_time"))
      {
        std::cout << "最后处理时间: " << blackboard->get<std::string>("last_processed_time") << std::endl;
      }
    }
    catch (const std::exception& e)
    {
      std::cout << "读取最终结果时出错: " << e.what() << std::endl;
    }

    return 0;
  }
  catch (const std::exception& e)
  {
    std::cerr << "示例执行失败: " << e.what() << std::endl;
    return 1;
  }
}

/* 这个示例展示了:
1. 自定义节点生成传感器数据
2. Lua 脚本处理和验证数据
3. 自定义节点根据 Lua 处理结果执行决策
4. 完整的数据流：C++ -> Lua -> C++
5. 黑板作为数据交换的中心枢纽
*/
