<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4" main_tree_to_execute="JsonViewerDemo">
    <!-- JSON可视化节点使用示例 -->
    <BehaviorTree ID="JsonViewerDemo">
        <Sequence>
            <!-- 示例1: 传感器数据可视化 -->
            <JsonViewerNode
                json_data='<![CDATA[{
  "sensor_data": {
    "temperature": 25.6,
    "humidity": 65.2,
    "pressure": 1013.25,
    "voltage": 3.3,
    "current": 0.85
  },
  "status": {
    "power_on": true,
    "sensor_ready": true,
    "calibrated": false,
    "error_count": 0
  },
  "location": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "altitude": 43.5
  }
}]]>'
                enable_visualization="true"
                visualization_mode="numeric"
                widget_type="NumericTable"
                visualization_title="传感器数据可视化"
                visualization_duration="6000"
                data_path="sensor_data"
                min_value="0.0"
                max_value="100.0"
                decimal_places="2"
                result="{json_result1}"
                error_message="{json_error1}"
                json_size="{json_size1}"
                visualization_shown="{viz_shown1}"
                visualization_data_count="{viz_count1}" />

            <!-- 示例2: 设备状态可视化 -->
            <JsonViewerNode
                json_data='<![CDATA[{
  "device_status": {
    "motor_running": true,
    "sensor_active": true,
    "communication_ok": false,
    "temperature_normal": true,
    "error_code": 0
  }
}]]>'
                enable_visualization="true"
                visualization_mode="status"
                widget_type="NumericTable"
                visualization_title="设备状态监控"
                visualization_duration="5000"
                data_path="device_status"
                min_value="0.0"
                max_value="1.0"
                decimal_places="0"
                result="{json_result2}"
                error_message="{json_error2}"
                json_size="{json_size2}"
                visualization_shown="{viz_shown2}"
                visualization_data_count="{viz_count2}" />

            <!-- 示例3: 仪表盘可视化 -->
            <JsonViewerNode
                json_data='<![CDATA[{
  "dashboard": {
    "cpu_usage": 45.2,
    "memory_usage": 67.8,
    "disk_usage": 23.1,
    "network_speed": 89.5,
    "temperature": 42.0
  }
}]]>'
                enable_visualization="true"
                visualization_mode="numeric"
                widget_type="NumericGauge"
                visualization_title="系统监控仪表盘"
                visualization_duration="8000"
                data_path="dashboard"
                min_value="0.0"
                max_value="100.0"
                decimal_places="1"
                result="{json_result3}"
                error_message="{json_error3}"
                json_size="{json_size3}"
                visualization_shown="{viz_shown3}"
                visualization_data_count="{viz_count3}" />
        </Sequence>
    </BehaviorTree>

    <!-- 简化的测试示例 -->
    <BehaviorTree ID="SimpleJsonTest">
        <Sequence>
            <!-- 自动模式可视化测试 -->
            <JsonViewerNode
                json_data='<![CDATA[{
  "measurements": {
    "value1": 12.34,
    "value2": 56.78,
    "value3": 90.12,
    "average": 53.08
  }
}]]>'
                enable_visualization="true"
                visualization_mode="auto"
                widget_type="NumericTable"
                visualization_title="测量数据自动显示"
                visualization_duration="4000"
                data_path="measurements"
                min_value="0.0"
                max_value="100.0"
                decimal_places="2"
                result="{simple_result}"
                error_message="{simple_error}"
                json_size="{simple_size}"
                visualization_shown="{simple_viz_shown}"
                visualization_data_count="{simple_viz_count}" />
        </Sequence>
    </BehaviorTree>
</root>
