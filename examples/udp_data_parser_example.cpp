#include "behaviortree_cpp/bt_factory.h"
#include "src/common/node_models/udp_data_parser_node.h"
#include <chrono>
#include <iostream>
#include <thread>

using namespace BT;
using namespace robosense::lidar;

int main()
{
  try
  {
    // 创建 BehaviorTree 工厂
    BT::BehaviorTreeFactory factory;

    // 注册 UDP 数据解析节点
    factory.registerNodeType<UdpDataParserNode>("UdpDataParserNode");

    std::cout << "=== UDP数据解析示例 ===\n";
    std::cout << "监听端口: 6699\n";
    std::cout << "发送测试数据: echo -n -e '\\x01\\x02\\x01\\x01\\x03\\xE8\\x32\\x00' | nc -u localhost 6699\n\n";

    // 从 XML 文件创建行为树
    auto tree = factory.createTreeFromFile("udp_parser_example.xml");

    std::cout << "🚀 开始运行...\n";

    // 运行行为树10秒
    auto start_time       = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::seconds(10);

    while (std::chrono::steady_clock::now() - start_time < timeout_duration)
    {
      BT::NodeStatus status = tree.tickOnce();
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    std::cout << "\n⏹️ 示例运行完成\n";
    return 0;
  }
  catch (const std::exception& e)
  {
    std::cerr << "错误: " << e.what() << '\n';
    return 1;
  }
}

/* 
这个示例展示了:
1. 使用MEMSUDP接收UDP数据
2. 通过Lua脚本解析UDP数据包
3. 将解析结果存储到BehaviorTree黑板
4. 根据数据类型进行不同的处理
5. 统计和监控UDP数据接收情况
6. 完整的错误处理和超时处理

测试方法:
1. 运行程序
2. 在另一个终端使用nc命令发送测试数据
3. 观察程序输出的解析结果
*/
